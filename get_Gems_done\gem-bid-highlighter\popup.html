<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GEM Bid Highlighter v2.0</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header>
            <h1>🎯 GEM Bid Highlighter</h1>
            <p class="version">v2.0 - Clean & Functional</p>
        </header>

        <!-- Upload Section -->
        <section class="upload-section">
            <div class="upload-area" id="uploadArea">
                <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
                <div class="upload-content">
                    <div class="upload-icon">📊</div>
                    <div class="upload-text">
                        <strong>Upload Excel File</strong>
                        <p>Drag & drop or click to select</p>
                    </div>
                </div>
            </div>
            
            <div class="file-info" id="fileInfo" style="display: none;">
                <span class="file-name" id="fileName"></span>
                <button class="clear-btn" id="clearBtn">✕</button>
            </div>
        </section>

        <!-- Export Type -->
        <section class="export-type" id="exportType" style="display: none;">
            <div class="type-badge" id="typeBadge"></div>
            <p class="type-description" id="typeDescription"></p>
        </section>

        <!-- Statistics -->
        <section class="stats" id="stats" style="display: none;">
            <h3>📊 Bid Statistics</h3>
            <div class="stat-grid">
                <div class="stat-item">
                    <span class="stat-label">Total Bids:</span>
                    <span class="stat-value" id="totalBids">0</span>
                </div>
                <div class="stat-item" id="acceptedStat" style="display: none;">
                    <span class="stat-label">🟢 Accepted:</span>
                    <span class="stat-value" id="acceptedBids">0</span>
                </div>
                <div class="stat-item" id="rejectedStat" style="display: none;">
                    <span class="stat-label">🔴 Rejected:</span>
                    <span class="stat-value" id="rejectedBids">0</span>
                </div>
                <div class="stat-item" id="pendingStat" style="display: none;">
                    <span class="stat-label">🟡 Pending:</span>
                    <span class="stat-value" id="pendingBids">0</span>
                </div>
            </div>
        </section>

        <!-- Status -->
        <section class="status" id="status">
            <div class="status-item">
                <span class="status-label">🌐 Current Page:</span>
                <span class="status-value" id="pageStatus">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">🎨 Highlighting:</span>
                <span class="status-value" id="highlightStatus">No Data</span>
            </div>
            <div class="status-item">
                <span class="status-label">📍 Page Bids:</span>
                <span class="status-value" id="pageBids">0</span>
            </div>
        </section>

        <!-- Actions -->
        <section class="actions">
            <button class="btn btn-primary" id="highlightBtn" disabled>
                🎨 Highlight This Page
            </button>
            <button class="btn btn-secondary" id="clearHighlights">
                🧹 Clear Highlights
            </button>
        </section>

        <!-- Messages -->
        <section class="messages" id="messages"></section>
    </div>

    <!-- Scripts -->
    <script src="xlsx.min.js"></script>
    <script src="popup.js"></script>
</body>
</html>
