"""Add scraping session tracking fields

Revision ID: 30abb5f2c386
Revises: af08c0a9e455
Create Date: 2025-05-21 14:41:01.943295

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '30abb5f2c386'
down_revision: Union[str, None] = 'af08c0a9e455'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bids', sa.Column('scrape_session_id', sa.String(), nullable=True))
    op.add_column('bids', sa.Column('scrape_source_type', sa.Enum('MINISTRY_ORGANIZATION', 'LOCATION', name='scrapesourcetypeenum'), nullable=True))
    op.add_column('bids', sa.Column('scrape_source_details', sa.String(), nullable=True))
    op.create_index(op.f('ix_bids_scrape_session_id'), 'bids', ['scrape_session_id'], unique=False)
    op.create_index(op.f('ix_bids_scrape_source_type'), 'bids', ['scrape_source_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bids_scrape_source_type'), table_name='bids')
    op.drop_index(op.f('ix_bids_scrape_session_id'), table_name='bids')
    op.drop_column('bids', 'scrape_source_details')
    op.drop_column('bids', 'scrape_source_type')
    op.drop_column('bids', 'scrape_session_id')
    # ### end Alembic commands ###
