#!/usr/bin/env python3
"""
Interactive User Interface for Vendor API Tool
==============================================

A user-friendly interface for configuring and running the vendor registration portal API tool.

Author: AI Assistant
Date: 2025-07-02
"""

import json
import os
import sys
from datetime import datetime
from vendor_api_tool import VendorAPITool, HTMLTableExtractor, ExcelReportGenerator
import logging

# Configure logging for UI
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class VendorAPIUI:
    """Interactive user interface for the Vendor API Tool."""
    
    def __init__(self):
        self.config = {
            'search_params': {},
            'output_settings': {
                'filename': f'vendor_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx',
                'title': 'Vendor Registration Report'
            }
        }
    
    def display_banner(self):
        """Display the application banner."""
        print("=" * 60)
        print("    VENDOR REGISTRATION PORTAL API TOOL")
        print("    Extract vendor data and generate Excel reports")
        print("=" * 60)
        print()
    
    def display_menu(self):
        """Display the main menu."""
        print("\nMain Menu:")
        print("1. Configure Search Parameters")
        print("2. Configure Output Settings")
        print("3. Run Data Extraction")
        print("4. Load Configuration from File")
        print("5. Save Configuration to File")
        print("6. View Current Configuration")
        print("7. Exit")
        print()
    
    def get_user_choice(self, prompt: str, valid_choices: list) -> str:
        """Get user choice with validation."""
        while True:
            choice = input(prompt).strip()
            if choice in valid_choices:
                return choice
            print(f"Invalid choice. Please select from: {', '.join(valid_choices)}")
    
    def configure_search_parameters(self):
        """Configure search parameters interactively."""
        print("\n--- Configure Search Parameters ---")
        print("Leave blank to skip a parameter")
        print()
        
        # Factory ID
        factory_id = input("Factory ID (e.g., 20): ").strip()
        if factory_id:
            self.config['search_params']['factory_id'] = factory_id
        
        # Location
        location = input("Location (e.g., Rajkot): ").strip()
        if location:
            self.config['search_params']['location'] = location
        
        # Industry Category
        print("\nIndustry Category options:")
        print("L - Large Scale")
        print("M - Medium Scale")
        print("S - Small Scale")
        industry_category = input("Industry Category (L/M/S): ").strip().upper()
        if industry_category in ['L', 'M', 'S']:
            self.config['search_params']['industry_category'] = industry_category
        
        # Product
        product = input("Product (e.g., Cylinder): ").strip()
        if product:
            self.config['search_params']['product'] = product
        
        # Used For
        print("\nUsed For options:")
        print("R - Residential")
        print("C - Commercial")
        print("I - Industrial")
        used_for = input("Used For (R/C/I): ").strip().upper()
        if used_for in ['R', 'C', 'I']:
            self.config['search_params']['used_for'] = used_for
        
        # Vendor
        vendor = input("Vendor name/keyword: ").strip()
        if vendor:
            self.config['search_params']['vendor'] = vendor
        
        print("\nSearch parameters configured successfully!")
    
    def configure_output_settings(self):
        """Configure output settings."""
        print("\n--- Configure Output Settings ---")
        
        # Filename
        current_filename = self.config['output_settings']['filename']
        print(f"Current filename: {current_filename}")
        new_filename = input("New filename (press Enter to keep current): ").strip()
        if new_filename:
            if not new_filename.endswith('.xlsx'):
                new_filename += '.xlsx'
            self.config['output_settings']['filename'] = new_filename
        
        # Report title
        current_title = self.config['output_settings']['title']
        print(f"Current title: {current_title}")
        new_title = input("New report title (press Enter to keep current): ").strip()
        if new_title:
            self.config['output_settings']['title'] = new_title
        
        print("\nOutput settings configured successfully!")
    
    def view_configuration(self):
        """Display current configuration."""
        print("\n--- Current Configuration ---")
        
        print("\nSearch Parameters:")
        if self.config['search_params']:
            for key, value in self.config['search_params'].items():
                print(f"  {key.replace('_', ' ').title()}: {value}")
        else:
            print("  No search parameters configured")
        
        print("\nOutput Settings:")
        print(f"  Filename: {self.config['output_settings']['filename']}")
        print(f"  Title: {self.config['output_settings']['title']}")
    
    def load_configuration(self):
        """Load configuration from a JSON file."""
        print("\n--- Load Configuration ---")
        filename = input("Enter configuration filename (default: config.json): ").strip()
        if not filename:
            filename = "config.json"
        
        try:
            if os.path.exists(filename):
                with open(filename, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"Configuration loaded from {filename}")
            else:
                print(f"File {filename} not found")
        except Exception as e:
            print(f"Error loading configuration: {e}")
    
    def save_configuration(self):
        """Save configuration to a JSON file."""
        print("\n--- Save Configuration ---")
        filename = input("Enter filename to save (default: config.json): ").strip()
        if not filename:
            filename = "config.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.config, f, indent=4)
            print(f"Configuration saved to {filename}")
        except Exception as e:
            print(f"Error saving configuration: {e}")
    
    def run_extraction(self):
        """Run the data extraction process."""
        print("\n--- Running Data Extraction ---")
        
        if not self.config['search_params']:
            print("Warning: No search parameters configured. Will fetch default data.")
            proceed = self.get_user_choice("Continue? (y/n): ", ['y', 'n', 'Y', 'N'])
            if proceed.lower() == 'n':
                return
        
        try:
            # Initialize components
            print("Initializing API tool...")
            api_tool = VendorAPITool()
            extractor = HTMLTableExtractor()
            excel_generator = ExcelReportGenerator()
            
            # Make API request
            print("Making API request...")
            if self.config['search_params']:
                html_content = api_tool.make_search_request(self.config['search_params'])
            else:
                html_content = api_tool.get_paginated_data()
            
            if not html_content:
                print("Error: Failed to get data from API")
                return
            
            print("Extracting table data...")
            table_data = extractor.extract_main_table(html_content)
            
            if not table_data:
                print("Error: No table data extracted")
                return
            
            print(f"Found {len(table_data)} rows of data")
            
            # Generate Excel report
            print("Generating Excel report...")
            filename = self.config['output_settings']['filename']
            title = self.config['output_settings']['title']
            
            success = excel_generator.create_report(
                table_data, 
                filename, 
                title, 
                self.config['search_params']
            )
            
            if success:
                print(f"\n✓ Report generated successfully: {filename}")
                print(f"  Records: {len(table_data) - 1}")
                print(f"  Columns: {len(table_data[0]) if table_data else 0}")
            else:
                print("✗ Failed to generate report")
                
        except Exception as e:
            print(f"Error during extraction: {e}")
            logger.error(f"Extraction error: {e}")
    
    def run(self):
        """Run the interactive user interface."""
        self.display_banner()
        
        while True:
            self.display_menu()
            choice = self.get_user_choice("Select an option (1-7): ", ['1', '2', '3', '4', '5', '6', '7'])
            
            if choice == '1':
                self.configure_search_parameters()
            elif choice == '2':
                self.configure_output_settings()
            elif choice == '3':
                self.run_extraction()
            elif choice == '4':
                self.load_configuration()
            elif choice == '5':
                self.save_configuration()
            elif choice == '6':
                self.view_configuration()
            elif choice == '7':
                print("\nThank you for using the Vendor API Tool!")
                break
            
            input("\nPress Enter to continue...")

def main():
    """Main function to run the interactive UI."""
    try:
        ui = VendorAPIUI()
        ui.run()
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        logger.error(f"UI error: {e}")

if __name__ == "__main__":
    main()
