#!/usr/bin/env python3
"""
Enhanced Interactive User Interface for Vendor API Tool
=======================================================

A beautiful and user-friendly interface for configuring and running the vendor registration portal API tool.
Features comprehensive filter options, nested table support, and PDF download functionality.

Author: AI Assistant
Date: 2025-07-02
"""

import json
import os
import sys
from datetime import datetime
from vendor_api_tool import VendorAPITool, HTMLTableExtractor, ExcelReportGenerator, FILTER_OPTIONS
import logging

# Configure logging for UI
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class VendorAPIUI:
    """Enhanced interactive user interface for the Vendor API Tool."""

    def __init__(self):
        self.config = {
            'search_params': {},
            'output_settings': {
                'filename': f'vendor_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx',
                'title': 'Vendor Registration Report',
                'download_pdf': True
            }
        }

    def display_banner(self):
        """Display the enhanced application banner."""
        print("=" * 80)
        print("    🏭 VENDOR REGISTRATION PORTAL API TOOL - ENHANCED VERSION 🏭")
        print("    Extract vendor data, handle nested tables & generate Excel reports")
        print("    ✨ Features: Comprehensive Filters | PDF Downloads | Nested Tables ✨")
        print("=" * 80)
        print()

    def display_menu(self):
        """Display the enhanced main menu with status indicators."""
        print("\n" + "=" * 60)
        print("📋 MAIN MENU")
        print("=" * 60)

        # Show configuration status
        param_count = len([v for v in self.config['search_params'].values() if v]) if self.config['search_params'] else 0
        param_status = f"({param_count} filters)" if param_count > 0 else "(no filters)"
        pdf_status = "✅" if self.config['output_settings'].get('download_pdf', True) else "❌"

        print("1. 🔍 Configure Search Parameters " + param_status)
        print("2. ⚙️  Configure Output Settings")
        print("3. 🚀 Run Data Extraction")
        print("4. 📁 Load Configuration from File")
        print("5. 💾 Save Configuration to File")
        print("6. 👁️  View Current Configuration")
        print("7. 📄 Download PDF Reports " + pdf_status)
        print("8. 🔍 Validate Configuration")
        print("9. 🔧 System Status")
        print("10. ❌ Exit")
        print("=" * 60)
    
    def get_user_choice(self, prompt: str, valid_choices: list) -> str:
        """Get user choice with validation."""
        while True:
            choice = input(prompt).strip()
            if choice in valid_choices:
                return choice
            print(f"Invalid choice. Please select from: {', '.join(valid_choices)}")
    
    def configure_search_parameters(self):
        """Configure search parameters with comprehensive filter options."""
        print("\n" + "=" * 60)
        print("🔍 CONFIGURE SEARCH PARAMETERS")
        print("=" * 60)
        print("Select filters to narrow down your search. Leave blank to skip.")
        print()

        while True:
            print("\n📋 Available Filter Categories:")
            print("1. 🏭 Factory Selection")
            print("2. 🏢 Industry Category")
            print("3. 📍 Location")
            print("4. 📦 Product")
            print("5. 🎯 Used For")
            print("6. 👤 Vendor Name")
            print("7. 📊 Sorting Options")
            print("8. ✅ Finish Configuration")
            print("9. 🔄 Clear All Parameters")

            choice = self.get_user_choice("\nSelect option (1-9): ", ['1', '2', '3', '4', '5', '6', '7', '8', '9'])

            if choice == '1':
                self._configure_factory()
            elif choice == '2':
                self._configure_industry_category()
            elif choice == '3':
                self._configure_location()
            elif choice == '4':
                self._configure_product()
            elif choice == '5':
                self._configure_used_for()
            elif choice == '6':
                self._configure_vendor()
            elif choice == '7':
                self._configure_sorting()
            elif choice == '8':
                break
            elif choice == '9':
                self.config['search_params'] = {}
                print("\n🗑️ All search parameters cleared!")

            # Show current configuration after each change
            if self.config['search_params']:
                self.display_current_search_params()

        print("\n✅ Search parameters configuration completed!")
        if self.config['search_params']:
            self.display_current_search_params()
        else:
            print("ℹ️ No search parameters configured. All data will be retrieved.")

    def display_current_search_params(self):
        """Display current search parameters in a formatted way."""
        print("\n📋 Current Search Parameters:")
        print("=" * 40)

        if not self.config['search_params']:
            print("  ℹ️ No parameters configured")
            return

        for key, value in self.config['search_params'].items():
            if value:  # Only show non-empty parameters
                # Format the parameter name
                param_name = key.replace('_', ' ').title()

                # Handle different parameter types
                try:
                    if key == 'factory_id' and value in FILTER_OPTIONS['factories']:
                        display_value = f"{FILTER_OPTIONS['factories'][value]} ({value})"
                    elif key == 'industry_category' and value in FILTER_OPTIONS['industry_category']:
                        display_value = f"{FILTER_OPTIONS['industry_category'][value]} ({value})"
                    elif key == 'used_for' and value in FILTER_OPTIONS['used_for']:
                        display_value = f"{FILTER_OPTIONS['used_for'][value]} ({value})"
                    elif key == 'sort_by' and value in FILTER_OPTIONS['sort_by']:
                        display_value = f"{FILTER_OPTIONS['sort_by'][value]} ({value})"
                    else:
                        display_value = str(value)
                except KeyError:
                    # Fallback if key not found in FILTER_OPTIONS
                    display_value = str(value)

                print(f"  🔹 {param_name}: {display_value}")

        print("=" * 40)

    def _configure_factory(self):
        """Configure factory selection with comprehensive options."""
        print("\n🏭 FACTORY SELECTION")
        print("=" * 40)

        factories = FILTER_OPTIONS['factories']

        # Display factories in a paginated manner
        factories_list = list(factories.items())
        page_size = 10
        total_pages = (len(factories_list) + page_size - 1) // page_size

        current_page = 0
        while True:
            start_idx = current_page * page_size
            end_idx = min(start_idx + page_size, len(factories_list))

            print(f"\nPage {current_page + 1} of {total_pages}")
            print("-" * 40)

            for i in range(start_idx, end_idx):
                factory_id, factory_name = factories_list[i]
                print(f"{factory_id:2}. {factory_name}")

            print("\nOptions:")
            print("N - Next page" if current_page < total_pages - 1 else "")
            print("P - Previous page" if current_page > 0 else "")
            print("S - Select factory by ID")
            print("B - Back to main menu")

            choice = input("\nEnter choice: ").strip().upper()

            if choice == 'N' and current_page < total_pages - 1:
                current_page += 1
            elif choice == 'P' and current_page > 0:
                current_page -= 1
            elif choice == 'S':
                factory_id = input("Enter Factory ID: ").strip()
                if factory_id in factories:
                    self.config['search_params']['factory_id'] = factory_id
                    print(f"✅ Selected: {factories[factory_id]}")
                    break
                else:
                    print("❌ Invalid Factory ID. Please try again.")
            elif choice == 'B':
                break

    def _configure_industry_category(self):
        """Configure industry category selection."""
        print("\n🏢 INDUSTRY CATEGORY SELECTION")
        print("=" * 40)

        categories = FILTER_OPTIONS['industry_categories']

        for code, name in categories.items():
            print(f"{code} - {name}")

        choice = input("\nEnter category code (or press Enter to skip): ").strip().upper()

        if choice in categories:
            self.config['search_params']['industry_category'] = choice
            print(f"✅ Selected: {categories[choice]}")
        elif choice:
            print("❌ Invalid category code.")

    def _configure_location(self):
        """Configure location filter."""
        print("\n📍 LOCATION FILTER")
        print("=" * 40)

        location = input("Enter location (e.g., Rajkot, Mumbai): ").strip()
        if location:
            self.config['search_params']['location'] = location
            print(f"✅ Location filter set: {location}")

    def _configure_product(self):
        """Configure product filter."""
        print("\n📦 PRODUCT FILTER")
        print("=" * 40)

        product = input("Enter product name/keyword: ").strip()
        if product:
            self.config['search_params']['product'] = product
            print(f"✅ Product filter set: {product}")

    def _configure_used_for(self):
        """Configure 'used for' selection."""
        print("\n🎯 USED FOR SELECTION")
        print("=" * 40)

        used_for_options = FILTER_OPTIONS['used_for']

        for code, name in used_for_options.items():
            print(f"{code} - {name}")

        choice = input("\nEnter code (or press Enter to skip): ").strip().upper()

        if choice in used_for_options:
            self.config['search_params']['used_for'] = choice
            print(f"✅ Selected: {used_for_options[choice]}")
        elif choice:
            print("❌ Invalid code.")

    def _configure_vendor(self):
        """Configure vendor name filter."""
        print("\n👤 VENDOR NAME FILTER")
        print("=" * 40)

        vendor = input("Enter vendor name/keyword: ").strip()
        if vendor:
            self.config['search_params']['vendor'] = vendor
            print(f"✅ Vendor filter set: {vendor}")

    def _configure_sorting(self):
        """Configure sorting options."""
        print("\n📊 SORTING OPTIONS")
        print("=" * 40)

        sorting_options = FILTER_OPTIONS['sorting']

        for code, name in sorting_options.items():
            print(f"{code} - {name}")

        choice = input("\nEnter sorting code (or press Enter to skip): ").strip().lower()

        if choice in sorting_options:
            self.config['search_params']['sort_by'] = choice
            print(f"✅ Sorting by: {sorting_options[choice]}")
        elif choice:
            print("❌ Invalid sorting code.")

    def configure_output_settings(self):
        """Configure enhanced output settings."""
        print("\n" + "=" * 60)
        print("⚙️ CONFIGURE OUTPUT SETTINGS")
        print("=" * 60)

        while True:
            print("\n📋 Output Configuration Options:")
            print("1. 📄 Excel Filename")
            print("2. 📝 Report Title")
            print("3. 📥 PDF Download Settings")
            print("4. ✅ Finish Configuration")

            choice = self.get_user_choice("\nSelect option (1-4): ", ['1', '2', '3', '4'])

            if choice == '1':
                self._configure_filename()
            elif choice == '2':
                self._configure_title()
            elif choice == '3':
                self._configure_pdf_settings()
            elif choice == '4':
                break

            # Show current settings
            self._display_output_settings()

        print("\n✅ Output settings configuration completed!")

    def _configure_filename(self):
        """Configure Excel filename."""
        current_filename = self.config['output_settings']['filename']
        print(f"\n📄 Current filename: {current_filename}")
        new_filename = input("Enter new filename (or press Enter to keep current): ").strip()
        if new_filename:
            if not new_filename.endswith('.xlsx'):
                new_filename += '.xlsx'
            self.config['output_settings']['filename'] = new_filename
            print(f"✅ Filename updated: {new_filename}")

    def _configure_title(self):
        """Configure report title."""
        current_title = self.config['output_settings']['title']
        print(f"\n📝 Current report title: {current_title}")
        new_title = input("Enter new report title (or press Enter to keep current): ").strip()
        if new_title:
            self.config['output_settings']['title'] = new_title
            print(f"✅ Title updated: {new_title}")

    def _configure_pdf_settings(self):
        """Configure PDF download settings."""
        print("\n📥 PDF DOWNLOAD SETTINGS")
        print("=" * 30)

        current_setting = self.config['output_settings'].get('download_pdf', True)
        print(f"Current setting: {'Enabled' if current_setting else 'Disabled'}")

        choice = self.get_user_choice("Enable PDF downloads? (y/n): ", ['y', 'n', 'Y', 'N'])

        self.config['output_settings']['download_pdf'] = choice.lower() == 'y'
        status = "enabled" if self.config['output_settings']['download_pdf'] else "disabled"
        print(f"✅ PDF downloads {status}")

    def _display_output_settings(self):
        """Display current output settings."""
        print("\n📋 Current Output Settings:")
        print(f"  📄 Filename: {self.config['output_settings']['filename']}")
        print(f"  📝 Title: {self.config['output_settings']['title']}")
        print(f"  📥 PDF Downloads: {'Enabled' if self.config['output_settings'].get('download_pdf', True) else 'Disabled'}")
    
    def view_configuration(self):
        """Display comprehensive current configuration."""
        print("\n" + "=" * 70)
        print("👁️ CURRENT CONFIGURATION OVERVIEW")
        print("=" * 70)

        # Search Parameters Section
        print("\n🔍 SEARCH PARAMETERS:")
        print("-" * 30)
        if self.config['search_params']:
            self.display_current_search_params()
        else:
            print("  ℹ️ No search parameters configured - will retrieve all data")

        # Output Settings Section
        print("\n⚙️ OUTPUT SETTINGS:")
        print("-" * 30)
        print(f"  📄 Excel Filename: {self.config['output_settings']['filename']}")
        print(f"  📝 Report Title: {self.config['output_settings']['title']}")
        print(f"  📥 PDF Downloads: {'✅ Enabled' if self.config['output_settings'].get('download_pdf', True) else '❌ Disabled'}")

        # Configuration Summary
        param_count = len([v for v in self.config['search_params'].values() if v]) if self.config['search_params'] else 0
        print(f"\n📊 SUMMARY:")
        print("-" * 30)
        print(f"  🔹 Active Filters: {param_count}")
        print(f"  🔹 Output Format: Excel (.xlsx)")
        print(f"  🔹 PDF Support: {'Yes' if self.config['output_settings'].get('download_pdf', True) else 'No'}")
        print(f"  🔹 Nested Tables: ✅ Supported")

        print("=" * 70)

    def validate_configuration(self):
        """Validate current configuration and provide recommendations."""
        print("\n🔍 CONFIGURATION VALIDATION")
        print("=" * 40)

        issues = []
        recommendations = []

        # Check search parameters
        if not self.config['search_params']:
            recommendations.append("💡 Consider adding search filters to narrow down results")
        else:
            param_count = len([v for v in self.config['search_params'].values() if v])
            if param_count > 5:
                recommendations.append("⚠️ Many filters active - results might be very specific")

        # Check output settings
        filename = self.config['output_settings']['filename']
        if not filename.endswith('.xlsx'):
            issues.append("❌ Output filename should end with .xlsx")

        # Check for potential conflicts
        if self.config['search_params'].get('factory_id') and self.config['search_params'].get('location'):
            recommendations.append("💡 Factory and location filters might be redundant")

        # Display results
        if issues:
            print("🚨 ISSUES FOUND:")
            for issue in issues:
                print(f"  {issue}")

        if recommendations:
            print("\n💡 RECOMMENDATIONS:")
            for rec in recommendations:
                print(f"  {rec}")

        if not issues and not recommendations:
            print("✅ Configuration looks good!")

        print("=" * 40)

    def show_system_status(self):
        """Display comprehensive system status."""
        print("\n🔧 SYSTEM STATUS CHECK")
        print("=" * 50)

        try:
            # Create API tool instance to check status
            from vendor_api_tool import VendorAPITool
            api_tool = VendorAPITool()
            status = api_tool.get_system_status()

            print(f"🕐 Timestamp: {status['timestamp']}")
            print(f"🌐 Base URL: {status['base_url']}")
            print(f"📡 Endpoint: {status['endpoint']}")
            print(f"🔗 Connection: {'✅ Verified' if status['connection'] else '❌ Not verified'}")
            print(f"🖥️ Server Reachable: {'✅ Yes' if status['server_reachable'] else '❌ No'}")

            if status['response_time']:
                print(f"⚡ Response Time: {status['response_time']:.2f}s")

            print(f"🔧 Session Active: {'✅ Yes' if status['session_active'] else '❌ No'}")
            print(f"🤖 User Agent: {status['user_agent'][:50]}...")

            # Additional system checks
            import sys
            import platform
            print(f"\n💻 System Info:")
            print(f"  🐍 Python: {sys.version.split()[0]}")
            print(f"  💾 Platform: {platform.system()} {platform.release()}")

            # Check required modules
            required_modules = {
                'requests': 'requests',
                'beautifulsoup4': 'bs4',
                'openpyxl': 'openpyxl',
                'lxml': 'lxml'
            }
            print(f"\n📦 Dependencies:")
            for display_name, import_name in required_modules.items():
                try:
                    __import__(import_name)
                    print(f"  ✅ {display_name}")
                except ImportError:
                    print(f"  ❌ {display_name} - Missing!")

        except Exception as e:
            print(f"❌ Error checking system status: {e}")
            logger.error(f"System status error: {e}")

        print("=" * 50)

    def load_configuration(self):
        """Load configuration from a JSON file."""
        print("\n--- Load Configuration ---")
        filename = input("Enter configuration filename (default: config.json): ").strip()
        if not filename:
            filename = "config.json"
        
        try:
            if os.path.exists(filename):
                with open(filename, 'r') as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)
                print(f"Configuration loaded from {filename}")
            else:
                print(f"File {filename} not found")
        except Exception as e:
            print(f"Error loading configuration: {e}")
    
    def save_configuration(self):
        """Save configuration to a JSON file."""
        print("\n--- Save Configuration ---")
        filename = input("Enter filename to save (default: config.json): ").strip()
        if not filename:
            filename = "config.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.config, f, indent=4)
            print(f"Configuration saved to {filename}")
        except Exception as e:
            print(f"Error saving configuration: {e}")
    
    def run_extraction(self):
        """Run the enhanced data extraction process with PDF download support."""
        print("\n" + "=" * 60)
        print("🚀 RUNNING DATA EXTRACTION")
        print("=" * 60)

        if not self.config['search_params']:
            print("⚠️ Warning: No search parameters configured. Will fetch default data.")
            proceed = self.get_user_choice("Continue? (y/n): ", ['y', 'n', 'Y', 'N'])
            if proceed.lower() == 'n':
                return

        try:
            # Initialize components
            print("🔧 Initializing API tool...")
            api_tool = VendorAPITool()
            extractor = HTMLTableExtractor()
            excel_generator = ExcelReportGenerator()

            # Make API request
            print("📡 Making API request...")
            if self.config['search_params']:
                html_content = api_tool.make_search_request(self.config['search_params'])
            else:
                html_content = api_tool.get_paginated_data()

            if not html_content:
                print("❌ Error: Failed to get data from API")
                return

            print("📊 Extracting table data (with nested table support)...")
            table_data = extractor.extract_main_table(html_content)

            if not table_data:
                print("❌ Error: No table data extracted")
                return

            print(f"✅ Found {len(table_data)} rows of data")

            # Extract download links if PDF download is enabled
            download_links = []
            if self.config['output_settings'].get('download_pdf', True):
                print("🔍 Searching for download links...")
                download_links = extractor.extract_download_links(html_content)
                if download_links:
                    print(f"📥 Found {len(download_links)} download links")
                else:
                    print("ℹ️ No download links found")

            # Generate Excel report
            print("📈 Generating Excel report...")
            filename = self.config['output_settings']['filename']
            title = self.config['output_settings']['title']

            success = excel_generator.create_report(
                table_data,
                filename,
                title,
                self.config['search_params']
            )

            if success:
                print(f"\n✅ Excel report generated successfully!")
                print(f"  📄 File: {filename}")
                print(f"  📊 Records: {len(table_data) - 1}")
                print(f"  📋 Columns: {len(table_data[0]) if table_data else 0}")

                # Handle PDF downloads
                if download_links and self.config['output_settings'].get('download_pdf', True):
                    self._handle_pdf_downloads(api_tool, download_links)

            else:
                print("❌ Failed to generate Excel report")

        except Exception as e:
            print(f"❌ Error during extraction: {e}")
            logger.error(f"Extraction error: {e}")

    def _handle_pdf_downloads(self, api_tool, download_links):
        """Handle PDF downloads from extracted links."""
        print(f"\n📥 Processing {len(download_links)} download links...")

        for i, link in enumerate(download_links, 1):
            print(f"\n{i}. {link['text']}")
            print(f"   URL: {link['url']}")

            download = self.get_user_choice(f"Download this file? (y/n): ", ['y', 'n', 'Y', 'N'])

            if download.lower() == 'y':
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"download_{i}_{timestamp}.pdf"

                print(f"📥 Downloading {link['text']}...")
                success = api_tool.download_pdf(link['url'], filename)

                if success:
                    print(f"✅ Downloaded: {filename}")
                else:
                    print(f"❌ Failed to download: {link['text']}")

    def download_pdf_reports(self):
        """Dedicated method for downloading PDF reports."""
        print("\n" + "=" * 60)
        print("📄 PDF DOWNLOAD MANAGER")
        print("=" * 60)

        try:
            api_tool = VendorAPITool()
            extractor = HTMLTableExtractor()

            # Get current data to extract download links
            print("📡 Fetching current data to find download links...")
            if self.config['search_params']:
                html_content = api_tool.make_search_request(self.config['search_params'])
            else:
                html_content = api_tool.get_paginated_data()

            if not html_content:
                print("❌ Error: Failed to get data from API")
                return

            download_links = extractor.extract_download_links(html_content)

            if not download_links:
                print("ℹ️ No download links found in current data")
                return

            self._handle_pdf_downloads(api_tool, download_links)

        except Exception as e:
            print(f"❌ Error during PDF download: {e}")
            logger.error(f"PDF download error: {e}")
    
    def run(self):
        """Run the interactive user interface."""
        self.display_banner()
        
        while True:
            try:
                self.display_menu()
                choice = self.get_user_choice("\nSelect an option (1-10): ", ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'])

                if choice == '1':
                    self.configure_search_parameters()
                elif choice == '2':
                    self.configure_output_settings()
                elif choice == '3':
                    self.run_extraction()
                elif choice == '4':
                    self.load_configuration()
                elif choice == '5':
                    self.save_configuration()
                elif choice == '6':
                    self.view_configuration()
                elif choice == '7':
                    self.download_pdf_reports()
                elif choice == '8':
                    self.validate_configuration()
                elif choice == '9':
                    self.show_system_status()
                elif choice == '10':
                    print("\n" + "=" * 70)
                    print("🙏 Thank you for using the Enhanced Vendor API Tool!")
                    print("   ✨ Features: Comprehensive Filters | Nested Tables | PDF Downloads")
                    print("   🚀 Enhanced with beautiful GUI and robust error handling")
                    print("=" * 70)
                    break

            except KeyboardInterrupt:
                print("\n\n⏹️ Operation interrupted by user.")
                confirm = self.get_user_choice("Do you want to exit? (y/n): ", ['y', 'n', 'Y', 'N'])
                if confirm.lower() == 'y':
                    break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                logger.error(f"Menu error: {e}")
                print("🔄 Returning to main menu...")

            input("\n⏸️ Press Enter to continue...")

def main():
    """Main function to run the enhanced interactive UI."""
    print("🚀 Starting Enhanced Vendor Registration Portal API Tool...")
    try:
        ui = VendorAPIUI()
        ui.run()
    except KeyboardInterrupt:
        print("\n\n⏹️ Operation cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        logger.error(f"UI error: {e}")

if __name__ == "__main__":
    main()
