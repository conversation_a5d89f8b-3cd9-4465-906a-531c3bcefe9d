nine things

-> add logo [ done ]
-> loading bar while scrapping , and pretty much in every process [ done ]
-> load last scrap setting in quick scrap section [ done ]
-> in bid list section add option to show only last scrap bids [ done ]
-> change every bid process [ like pdf processing , excel export , filtering] every thing to in last scrap bids [ done ]
-> in excel name add date and time [ done ]
-> scrap all button in quick scrap section for all ministry scrapping and all location scrapping
-> make stop button for every process [ done ]
-> make all_bids show button as a filter also so we could so the oprations on all bids [cancel]
-> improve pdf processing
-> filter by from scrap source

-> remove extention and start code again from start
-> pdf processing through ai [ content caching ]
-> make a perfact working extention 
-> 