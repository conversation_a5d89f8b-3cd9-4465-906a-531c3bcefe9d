"""add_pdf_analysis_fields_to_bids

Revision ID: af08c0a9e455
Revises: a0828a756b3e
Create Date: 2025-05-12 16:51:37.180647

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'af08c0a9e455'
down_revision: Union[str, None] = 'a0828a756b3e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bids', sa.Column('pdf_analysis_status', sa.Enum('PENDING', 'ACCEPTED', 'REJECTED', 'NO_DOCUMENT_URL', 'PROCESSING_ERROR', name='pdfanalysisstatusenum'), nullable=True))
    op.add_column('bids', sa.Column('pdf_analysis_reasons', sa.Text(), nullable=True))
    op.add_column('bids', sa.Column('pdf_last_analysed_at', sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f('ix_bids_pdf_analysis_status'), 'bids', ['pdf_analysis_status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bids_pdf_analysis_status'), table_name='bids')
    op.drop_column('bids', 'pdf_last_analysed_at')
    op.drop_column('bids', 'pdf_analysis_reasons')
    op.drop_column('bids', 'pdf_analysis_status')
    # ### end Alembic commands ###
