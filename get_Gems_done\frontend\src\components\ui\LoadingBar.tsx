// src/components/ui/LoadingBar.tsx
"use client";

import React from "react";

interface LoadingBarProps {
	/** Progress percentage (0-100). If not provided, shows indeterminate animation */
	progress?: number;
	/** Loading bar height in pixels */
	height?: number;
	/** Whether to show pulse animation */
	pulse?: boolean;
	/** Custom className for styling */
	className?: string;
	/** Color variant */
	variant?: "primary" | "success" | "warning" | "error";
	/** Whether to show percentage text */
	showPercentage?: boolean;
	/** Custom label to display */
	label?: string;
}

const LoadingBar: React.FC<LoadingBarProps> = ({
	progress,
	height = 4,
	pulse = false,
	className = "",
	variant = "primary",
	showPercentage = false,
	label,
}) => {
	const isIndeterminate = progress === undefined;
	const safeProgress = Math.min(Math.max(progress || 0, 0), 100);

	const getVariantClasses = () => {
		switch (variant) {
			case "success":
				return "bg-gradient-to-r from-green-500 to-green-600";
			case "warning":
				return "bg-gradient-to-r from-yellow-500 to-yellow-600";
			case "error":
				return "bg-gradient-to-r from-red-500 to-red-600";
			default:
				return "bg-gradient-to-r from-blue-500 to-blue-600";
		}
	};

	return (
		<div className={`w-full ${className}`}>
			{(label || showPercentage) && (
				<div className="flex justify-between items-center mb-1">
					{label && <span className="text-sm text-gray-600">{label}</span>}
					{showPercentage && !isIndeterminate && (
						<span className="text-sm text-gray-500">{safeProgress}%</span>
					)}
				</div>
			)}
			<div
				className="loading-bar-container"
				style={{ height: `${height}px` }}
			>
				<div
					className={`loading-bar ${getVariantClasses()} ${
						isIndeterminate ? "indeterminate" : ""
					} ${pulse ? "pulse" : ""}`}
					style={{
						width: isIndeterminate ? "30%" : `${safeProgress}%`,
					}}
				/>
			</div>
		</div>
	);
};

export default LoadingBar;
