from logging.config import fileConfig
from sqlalchemy import create_engine, engine_from_config
from sqlalchemy import pool
from alembic import context
import os
import sys
from dotenv import load_dotenv
import app.db.models 
from app.db.database import Base  # Import your Base from your app's database module
from app.core.config import settings


PROJECT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# Load .env file from the project root
load_dotenv(os.path.join(PROJECT_DIR, '.env')) 

# Add project root to Python's module search path
if PROJECT_DIR not in sys.path:
    sys.path.append(PROJECT_DIR)
    
config = context.config
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Get the database URL from the settings
target_metadata = Base.metadata


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    
    from app.core.config import settings
    url = settings.DATABASE_URL 
    if url and url.startswith("sqlite:///./"): 
        url = "sqlite:///" + os.path.join(PROJECT_DIR, url.split("sqlite:///./", 1)[1])
    # url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    db_url_from_settings = settings.DATABASE_URL
    connect_args_for_engine = {}
    if db_url_from_settings.startswith("sqlite"):
        connect_args_for_engine = {"check_same_thread": False}
        if db_url_from_settings.startswith("sqlite:///./"):
             db_url_from_settings = "sqlite:///" + os.path.join(PROJECT_DIR, db_url_from_settings.split("sqlite:///./", 1)[1])
             
    engine = create_engine(
        db_url_from_settings,
        poolclass=pool.NullPool,
        connect_args=connect_args_for_engine
    )

    with engine.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata # <<< Passed here
        )
        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
