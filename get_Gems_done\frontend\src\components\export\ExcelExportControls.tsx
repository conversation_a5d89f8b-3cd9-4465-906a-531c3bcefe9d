"use client";

import { useState, useRef } from "react";
import { fetchApi, ApiError } from "@/lib/api";
import StatusBadge from "@/components/ui/StatusBadge";
import LoadingBar from "@/components/ui/LoadingBar";

interface Bid {
	id: number;
	bid_number: string;
	items: string | null;
	ministry_name: string | null;
	department_name: string | null;
	scraped_at: string;
}

interface ExcelExportControlsProps {
	currentFilteredBids: Bid[] | null;
	customKeywordsInput: string;
	excelExportApiUrl: string;
}

enum ExcelExportFilterType {
	PDF_PROCESSED = "pdf_processed",
	FILTERED_BIDS = "filtered_bids",
}

export default function ExcelExportControls({
	currentFilteredBids,
	customKeywordsInput,
	excelExportApiUrl,
}: ExcelExportControlsProps) {
	const [isExportingExcel, setIsExportingExcel] = useState(false);
	const [exportError, setExportError] = useState<string | null>(null);
	const existingExcelFileRef = useRef<HTMLInputElement>(null);

	const parseKeywordsFromInput = (input: string): string[] => {
		if (!input.trim()) return [];
		return input
			.split(/[,\n]/)
			.map((kw) => kw.trim())
			.filter((kw) => kw.length > 0);
	};

	const generateFileName = (
		type: "filtered_bids" | "pdf_processed_bids"
	): string => {
		const now = new Date();
		const day = String(now.getDate()).padStart(2, "0");
		const month = String(now.getMonth() + 1).padStart(2, "0");
		const year = now.getFullYear();
		const hours = String(now.getHours()).padStart(2, "0");
		const minutes = String(now.getMinutes()).padStart(2, "0");

		return `${day}_${month}_${year}_${hours}:${minutes}_${type}.xlsx`;
	};

	const handleExcelExport = async (
		exportType: "filtered_bids" | "pdf_processed"
	) => {
		console.log("Excel Export clicked:", exportType);
		console.log("Current filtered bids:", currentFilteredBids?.length || 0);

		let filterTypeForExport: ExcelExportFilterType;

		if (exportType === "filtered_bids") {
			// Check if we have currently displayed/filtered bids
			if (!currentFilteredBids || currentFilteredBids.length === 0) {
				console.log("No filtered bids available for export");
				setExportError(
					"Please filter some bids first before exporting. Use the filter buttons or keywords to get bids to export."
				);
				return;
			}

			// Use FILTERED_BIDS type and send the current displayed bid numbers
			filterTypeForExport = ExcelExportFilterType.FILTERED_BIDS;
			console.log("Will export current filtered bids by bid numbers");
		} else {
			filterTypeForExport = ExcelExportFilterType.PDF_PROCESSED;
		}

		setIsExportingExcel(true);
		setExportError(null);

		const formData = new FormData();
		formData.append("filter_condition_type", filterTypeForExport);

		if (filterTypeForExport === ExcelExportFilterType.FILTERED_BIDS) {
			// For filtered export, always send the current displayed bid numbers
			const bidNumbers =
				currentFilteredBids?.map((bid) => bid.bid_number) || [];
			console.log(
				"Sending current filtered bid numbers to backend:",
				bidNumbers.length,
				bidNumbers.slice(0, 5)
			);
			formData.append("bid_numbers", bidNumbers.join(","));
			formData.append("keywords_str", ""); // Empty keywords since we're using bid numbers
		}

		if (
			existingExcelFileRef.current?.files &&
			existingExcelFileRef.current.files.length > 0
		) {
			formData.append(
				"existing_excel_file",
				existingExcelFileRef.current.files[0]
			);
		}

		try {
			console.log("Making request to:", excelExportApiUrl);
			console.log("FormData contents:");
			for (let [key, value] of formData.entries()) {
				console.log(`  ${key}:`, value);
			}

			const response = await fetch(excelExportApiUrl, {
				method: "POST",
				body: formData,
			});

			console.log("Response status:", response.status);

			if (!response.ok) {
				let errorData;
				try {
					errorData = await response.json();
				} catch {
					/* ignore if not json */
				}
				const detail =
					errorData?.detail ||
					`Server responded with status ${response.status}`;
				throw new ApiError(
					`API Error: ${response.status} ${response.statusText}`,
					response.status,
					detail
				);
			}

			const blob = await response.blob();

			// Generate filename based on export type
			const suggestedFilename = generateFileName(
				exportType === "filtered_bids" ? "filtered_bids" : "pdf_processed_bids"
			);

			// Create download link
			const link = document.createElement("a");
			link.href = window.URL.createObjectURL(blob);
			link.download = suggestedFilename;

			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			window.URL.revokeObjectURL(link.href);

			console.log("Excel export download initiated:", suggestedFilename);

			if (existingExcelFileRef.current) {
				existingExcelFileRef.current.value = "";
			}
			setExportError(null);
		} catch (err) {
			console.error("Excel Export API Error:", err);
			let errMsg = "An unknown error occurred during Excel export.";
			if (err instanceof ApiError) {
				errMsg = `Export Error: ${err.message} ${
					err.details ? `(${JSON.stringify(err.details)})` : ""
				}`;
			} else if (err instanceof Error) {
				errMsg = `Export Error: ${err.message}`;
			}
			setExportError(errMsg);
		} finally {
			setIsExportingExcel(false);
		}
	};

	return (
		<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
			<h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
				Export to Excel
			</h4>

			{/* Upload Excel File Option */}
			<div className="mb-4">
				<label
					htmlFor="existingExcelUpload"
					className="block text-xs text-gray-600 dark:text-gray-400 mb-2"
				>
					Optional: Upload existing Excel to update/append (.xlsx)
				</label>
				<input
					type="file"
					id="existingExcelUpload"
					ref={existingExcelFileRef}
					accept=".xlsx"
					className="w-full p-2 border border-gray-300 rounded-lg text-sm cursor-pointer"
				/>
			</div>

			{/* Export Buttons */}
			<div className="space-y-3">
				<button
					onClick={() => handleExcelExport("filtered_bids")}
					disabled={
						isExportingExcel ||
						!currentFilteredBids ||
						currentFilteredBids.length === 0
					}
					className="btn btn-primary w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
					title={
						!currentFilteredBids || currentFilteredBids.length === 0
							? "Please filter bids first - no bids are currently displayed"
							: `Export ${currentFilteredBids.length} currently displayed bids to Excel`
					}
				>
					{isExportingExcel
						? "Generating..."
						: `Export Filtered Bids to Excel (${
								currentFilteredBids?.length || 0
						  })`}
				</button>

				<button
					onClick={() => handleExcelExport("pdf_processed")}
					disabled={isExportingExcel}
					className="btn btn-secondary w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
				>
					{isExportingExcel
						? "Generating..."
						: "Export PDF Processed Bids to Excel"}
				</button>
			</div>

			{/* Loading State */}
			{isExportingExcel && (
				<div className="mt-4 bg-yellow-50 text-yellow-700 p-3 rounded-md border border-yellow-200">
					<div className="flex items-center gap-2 mb-2">
						<StatusBadge status="running" text="Exporting" />
						<span>Generating Excel file...</span>
					</div>
					<LoadingBar height={3} variant="warning" />
				</div>
			)}

			{/* Error State */}
			{exportError && (
				<div className="mt-4 bg-red-50 text-red-700 p-3 rounded-md border border-red-200">
					<div className="flex items-center gap-2">
						<StatusBadge status="failed" text="Export Error" />
						<span>{exportError}</span>
					</div>
				</div>
			)}
		</div>
	);
}
