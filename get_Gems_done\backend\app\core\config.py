from pydantic_settings import BaseSettings # New import for BaseSettings
from pydantic import HttpUrl # HttpUrl and other Pydantic core types remain in 'pydantic'

class Settings(BaseSettings):
    DATABASE_URL: str
    GEM_API_ENDPOINT_URL: HttpUrl
    GEM_INITIAL_PAGE_URL: HttpUrl
    CSRF_FORM_FIELD_NAME: str
    CSRF_COOKIE_NAME: str
    PAYLOAD_FORM_FIELD_NAME: str
    ITEMS_PER_PAGE: int = 10
    DOWNLOADS_BASE_DIR: str = "gem_downloaded_bids"  # Default value if not in .env
    TEMP_ZIP_DIR: str = "temp_zip_archives"
    PDF_PROCESSING_BASE_DIR: str = "processed_bid_pdfs" # Base for sorted results
    PDF_UPLOAD_TEMP_DIR: str = "temp_pdf_uploads"

    # Gemini AI Configuration
    GEMINI_API_KEY: str = ""  # Add your Gemini API key to .env file

    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/gem_scraper.log"

    # Performance Configuration
    MAX_CONCURRENT_DOWNLOADS: int = 5
    SCRAPER_DELAY_SECONDS: float = 0.6
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 2

    # PDF Processing Configuration
    MAX_PDF_SIZE_MB: int = 50
    MIN_TEXT_LENGTH: int = 100
    MAX_PROCESSING_TIME: int = 300

    # Security Configuration
    SECRET_KEY: str = "your-secret-key-here"
    ALLOWED_HOSTS: str = "localhost,127.0.0.1"

    # Development Configuration
    DEBUG: bool = True
    RELOAD: bool = True


    REQUEST_HEADERS: dict = { # Keep common headers here
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "X-Requested-With": "XMLHttpRequest",
        "Origin": "https://bidplus.gem.gov.in", # Will be set by requests based on URL
        "Referer": "https://bidplus.gem.gov.in/advance-search", # Will be set by requests based on URL
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
    }


    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'

settings = Settings()