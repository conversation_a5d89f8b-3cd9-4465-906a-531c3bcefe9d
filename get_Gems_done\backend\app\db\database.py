# app/db/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings # Import our settings

# For SQLite, we need to add an argument for FastAPI's async nature
connect_args = {}
if settings.DATABASE_URL.startswith("sqlite"):
    connect_args = {"check_same_thread": False}

engine = create_engine(
    settings.DATABASE_URL,
    connect_args=connect_args
    # echo=True # Set echo=True to see SQL queries printed to console (good for debugging)
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base() # All DB models will inherit from this

# Dependency to get DB session in API endpoints
def get_db():
    db = SessionLocal()
    try:
        yield db  # Provides the session to the endpoint
    finally:
        db.close() # Ensures the session is closed after the request