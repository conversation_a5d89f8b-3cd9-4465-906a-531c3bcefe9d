// src/contexts/LoadingContext.tsx
"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

interface LoadingState {
	isLoading: boolean;
	message: string;
	progress?: number;
	status: "pending" | "running" | "completed" | "failed" | "idle";
	details?: string;
	operation?: string;
}

interface LoadingContextType {
	loadingState: LoadingState;
	setLoading: (loading: Partial<LoadingState>) => void;
	clearLoading: () => void;
	updateProgress: (progress: number, message?: string) => void;
	setStatus: (status: LoadingState["status"], message?: string) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
	const context = useContext(LoadingContext);
	if (!context) {
		throw new Error("useLoading must be used within a LoadingProvider");
	}
	return context;
};

interface LoadingProviderProps {
	children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
	const [loadingState, setLoadingState] = useState<LoadingState>({
		isLoading: false,
		message: "",
		status: "idle",
	});

	const setLoading = (loading: Partial<LoadingState>) => {
		setLoadingState(prev => ({
			...prev,
			...loading,
			isLoading: true,
		}));
	};

	const clearLoading = () => {
		setLoadingState({
			isLoading: false,
			message: "",
			status: "idle",
		});
	};

	const updateProgress = (progress: number, message?: string) => {
		setLoadingState(prev => ({
			...prev,
			progress,
			...(message && { message }),
			isLoading: true,
			status: "running",
		}));
	};

	const setStatus = (status: LoadingState["status"], message?: string) => {
		setLoadingState(prev => ({
			...prev,
			status,
			...(message && { message }),
			isLoading: status === "running" || status === "pending",
		}));
	};

	return (
		<LoadingContext.Provider
			value={{
				loadingState,
				setLoading,
				clearLoading,
				updateProgress,
				setStatus,
			}}
		>
			{children}
		</LoadingContext.Provider>
	);
};

export default LoadingContext;
