import os
import sys
import argparse
import subprocess
import json
import time

# Add the current directory to the path so we can import from subfolders
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from parsing and filtering modules
from parsing.bid_document_batch import process_bid_document
from filtering.bid_document_filter import BidD<PERSON>ument<PERSON><PERSON><PERSON>

def run_command(command):
    """Run a command and return its output."""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=True,
        universal_newlines=True
    )
    stdout, stderr = process.communicate()
    
    if process.returncode != 0:
        print(f"Error running command: {command}")
        print(f"Error: {stderr}")
        return False
    
    return True

def process_pdfs(input_dir, output_dir, workers=4):
    """
    Process PDF files to extract EMD details and bid terms.
    
    Args:
        input_dir: Directory containing PDF files
        output_dir: Directory to save extracted data
        workers: Number of parallel workers
        
    Returns:
        Path to the directory containing extracted data
    """
    print(f"Step 1: Extracting data from PDFs in {input_dir}...")
    start_time = time.time()
    
    # Create the extraction command
    extraction_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "parsing", "bid_document_batch.py")
    extraction_command = f"python {extraction_script} --input_dir \"{input_dir}\" --output_dir \"{output_dir}\" --workers {workers}"
    
    # Run the extraction
    if not run_command(extraction_command):
        print("Extraction failed. Aborting.")
        return None
    
    end_time = time.time()
    print(f"Extraction completed in {end_time - start_time:.2f} seconds")
    
    return output_dir

def filter_documents(input_dir, filter_output):
    """
    Filter documents based on extracted data.
    
    Args:
        input_dir: Directory containing extracted data
        filter_output: Path to save filtering results
        
    Returns:
        Dictionary with filtering results
    """
    print(f"\nStep 2: Filtering documents based on extracted data...")
    start_time = time.time()
    
    # Create and run the filter
    filter = BidDocumentFilter()
    results = filter.filter_documents(input_dir, filter_output)
    
    end_time = time.time()
    print(f"Filtering completed in {end_time - start_time:.2f} seconds")
    
    return results

def create_accepted_directory(results, input_dir, output_dir):
    """
    Create a directory of accepted documents.
    
    Args:
        results: Dictionary with filtering results
        input_dir: Directory containing original PDF files
        output_dir: Directory to save accepted documents
    """
    print(f"\nStep 3: Creating directory of accepted documents...")
    
    # Create a directory for accepted documents
    accepted_dir = os.path.join(output_dir, "accepted")
    os.makedirs(accepted_dir, exist_ok=True)
    
    # Copy or link accepted documents to the accepted directory
    print(f"Creating directory of accepted documents: {accepted_dir}")
    for doc in results["accepted"]:
        doc_name = doc["name"]
        source_pdf = None
        
        # Find the original PDF file
        for root, _, files in os.walk(input_dir):
            for file in files:
                if file.endswith(".pdf") and os.path.splitext(file)[0] == doc_name:
                    source_pdf = os.path.join(root, file)
                    break
        
        if source_pdf:
            # Create a symbolic link to the original PDF
            link_path = os.path.join(accepted_dir, os.path.basename(source_pdf))
            try:
                if os.path.exists(link_path):
                    os.remove(link_path)
                
                # On Windows, use copy instead of symlink
                if os.name == 'nt':
                    import shutil
                    shutil.copy2(source_pdf, link_path)
                    print(f"  Copied: {doc_name}")
                else:
                    os.symlink(source_pdf, link_path)
                    print(f"  Linked: {doc_name}")
            except Exception as e:
                print(f"  Error processing {doc_name}: {str(e)}")

def print_summary(results):
    """Print a summary of the filtering results."""
    print("\nFiltering Results Summary:")
    print(f"Total documents: {results['summary']['total']}")
    print(f"Accepted documents: {results['summary']['accepted']}")
    print(f"Rejected documents: {results['summary']['rejected']}")
    
    print("\nRejection Reasons Summary:")
    for reason, count in results["summary"]["rejection_reasons"].items():
        print(f"- {reason}: {count}")

def main():
    parser = argparse.ArgumentParser(description="Process and filter bid documents")
    parser.add_argument("--input_dir", required=True, help="Directory containing PDF files")
    parser.add_argument("--output_dir", default="processed_bids", help="Directory to save processed data")
    parser.add_argument("--filter_output", default=None, help="Path to save filtering results")
    parser.add_argument("--workers", type=int, default=4, help="Number of parallel workers for extraction")
    parser.add_argument("--parse_only", action="store_true", help="Only parse documents, don't filter")
    parser.add_argument("--filter_only", action="store_true", help="Only filter documents, don't parse")
    args = parser.parse_args()
    
    # Set default filter output path if not provided
    if args.filter_output is None:
        args.filter_output = os.path.join(args.output_dir, "filtering_results.json")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Process PDFs
    if not args.filter_only:
        extraction_dir = process_pdfs(args.input_dir, args.output_dir, args.workers)
        if extraction_dir is None:
            return
    else:
        extraction_dir = args.output_dir
        print("Skipping parsing step as --filter_only was specified")
    
    # Filter documents
    if not args.parse_only:
        results = filter_documents(extraction_dir, args.filter_output)
        print_summary(results)
        create_accepted_directory(results, args.input_dir, args.output_dir)
        print(f"\nProcessing complete. Results saved to {args.filter_output}")
        print(f"Accepted documents copied to {os.path.join(args.output_dir, 'accepted')}")
    else:
        print("Skipping filtering step as --parse_only was specified")
        print(f"\nParsing complete. Results saved to {args.output_dir}")

if __name__ == "__main__":
    main()
