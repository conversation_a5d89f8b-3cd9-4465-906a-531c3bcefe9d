# app/services/scraper_service.py
import requests
import json
from datetime import datetime
import time
import math
from typing import Optional, List, Tuple, Set

from app.core.config import settings
from app.schemas import bid_schemas

# --- PARSING FUNCTIONS (remain the same, ensure robust error handling in parse_bid_data_from_api_doc) ---
def parse_value_from_list(data_list):
    if isinstance(data_list, list) and len(data_list) > 0: return data_list[0]
    return data_list

def parse_date_from_iso_string(date_str_list):
    date_str = parse_value_from_list(date_str_list)
    if not date_str: return None
    try:
        if date_str.endswith('Z'): date_str = date_str[:-1]
        return datetime.fromisoformat(date_str)
    except ValueError as e: print(f"Scraper Warning: Could not parse date '{date_str}': {e}"); return None

def parse_bid_data_from_api_doc(api_doc: dict) -> Optional[bid_schemas.BidCreate]:
    try:
        bid_number = parse_value_from_list(api_doc.get("b_bid_number"))
        if not bid_number:
            return None
        items_list = api_doc.get("b_category_name", [])
        items_str = ", ".join(items_list) if isinstance(items_list, list) else items_list
        quantity_val = parse_value_from_list(api_doc.get("b_total_quantity"))
        quantity = int(quantity_val) if quantity_val is not None else None
        ministry_name = parse_value_from_list(api_doc.get("ba_official_details_minName"))
        department_name = parse_value_from_list(api_doc.get("ba_official_details_deptName"))
        start_date_obj = parse_date_from_iso_string(api_doc.get("final_start_date_sort"))
        end_date_obj = parse_date_from_iso_string(api_doc.get("final_end_date_sort"))
        bid_id_internal_val = parse_value_from_list(api_doc.get('b_id', api_doc.get('id')))
        bid_details_url_val = f"https://bidplus.gem.gov.in/showbidDocument/{bid_id_internal_val}" if bid_id_internal_val else None


        return bid_schemas.BidCreate(
            bid_id_internal=str(bid_id_internal_val) if bid_id_internal_val else None,
            bid_number=bid_number,
            items=items_str,
            quantity=quantity,
            ministry_name=ministry_name,
            department_name=department_name,
            start_date=start_date_obj,
            end_date=end_date_obj,
            bid_details_url=bid_details_url_val
            # raw_api_doc=api_doc # REMOVED THIS ARGUMENT
        )
    except Exception as e:
        bid_num_for_log = parse_value_from_list(api_doc.get("b_bid_number", ["UNKNOWN_BID"]))
        print(f"Scraper Error parsing bid doc ({bid_num_for_log[0]}): {e}")
        return None
# --- END PARSING FUNCTIONS ---

# def _fetch_page_bids_from_api(
#     session: requests.Session, 
#     payload_json_string_for_api: str, 
#     csrf_token: str,
#     page_num_logging: str = "?"
# ) -> Tuple[List[bid_schemas.BidCreate], int]:
#     form_data_for_post = {
#         settings.PAYLOAD_FORM_FIELD_NAME: payload_json_string_for_api,
#         settings.CSRF_FORM_FIELD_NAME: csrf_token
#     }
#     parsed_bids_on_page: List[bid_schemas.BidCreate] = []
#     num_found_on_call = 0
    
#     print(f"  [Fetch Page {page_num_logging}] Preparing POST to {settings.GEM_API_ENDPOINT_URL}") # LOG BEFORE
#     # print(f"  [Fetch Page {page_num_logging}] Payload: {payload_json_string_for_api}") # Optional: Log payload if needed
#     # print(f"  [Fetch Page {page_num_logging}] CSRF: {csrf_token}") # Optional
    
#     try:
#         response = session.post(
#             str(settings.GEM_API_ENDPOINT_URL), data=form_data_for_post, timeout=(15, 30)
#         )
#         # --- ADD LOGGING HERE ---
#         print(f"  [Fetch Page {page_num_logging}] Received Response Status Code: {response.status_code}")
#         # Log first part of content *before* potential raise_for_status or json decode error
#         print(f"  [Fetch Page {page_num_logging}] Response Text (first 200): {response.text[:200]}") 
#         # --- END ADDED LOGGING ---
        
#         response.raise_for_status() # Raise HTTPError for bad responses (4XX or 5XX)
        
#         api_response_data = response.json() # This might fail if response text isn't JSON

#         # ... rest of the function remains the same ...
#         # (checking status, code, parsing docs)

#     except requests.exceptions.Timeout: print(f"Scraper Error: Request timed out for page {page_num_logging}.")
#     except requests.exceptions.RequestException as e: print(f"Scraper Error: Request exception page {page_num_logging}: {e}")
#     except json.JSONDecodeError as e: print(f"Scraper Error: JSON decode error page {page_num_logging}: {e}") # Will catch if response.text wasn't JSON
#     except Exception as e: print(f"Scraper Error: Unexpected error fetching page {page_num_logging}: {e}");
    
#     return parsed_bids_on_page, num_found_on_call # Ensure returning tuple

def _fetch_page_bids_from_api( # This helper remains the same
    session: requests.Session, 
    payload_json_string_for_api: str, 
    csrf_token: str,
    page_num_logging: str = "?"
) -> Tuple[List[bid_schemas.BidCreate], int]:
    form_data_for_post = {
        settings.PAYLOAD_FORM_FIELD_NAME: payload_json_string_for_api,
        settings.CSRF_FORM_FIELD_NAME: csrf_token
    }
    parsed_bids_on_page: List[bid_schemas.BidCreate] = []
    num_found_on_call = 0
    try:
        response = session.post(
            str(settings.GEM_API_ENDPOINT_URL), data=form_data_for_post, timeout=(15, 30)
        )
        if response.status_code != 200:
             print(f"Scraper Error: Status Code {response.status_code} for page {page_num_logging}. Response: {response.text[:200]}")
        response.raise_for_status()
        api_response_data = response.json()
        if api_response_data.get("status") == 1 and api_response_data.get("code") == 200:
            inner_response = api_response_data.get("response", {}).get("response", {})
            bids_docs_from_api = inner_response.get("docs", [])
            num_found_on_call = inner_response.get("numFound", 0)
            for doc in bids_docs_from_api:
                parsed_bid_schema = parse_bid_data_from_api_doc(doc)
                if parsed_bid_schema:
                    parsed_bids_on_page.append(parsed_bid_schema)
            return parsed_bids_on_page, num_found_on_call
        else: # API returned success status but logical error in its own response
            print(f"Scraper Warning: API returned non-success for page {page_num_logging}: status {api_response_data.get('status')}/code {api_response_data.get('code')}, Msg: {api_response_data.get('message')}")
            return [], num_found_on_call # Still return num_found if API provided it, even on logical error
    except requests.exceptions.Timeout: print(f"Scraper Error: Request timed out for page {page_num_logging}.")
    except requests.exceptions.RequestException as e: print(f"Scraper Error: Request exception for page {page_num_logging}: {e}")
    except json.JSONDecodeError as e: print(f"Scraper Error: JSON decode error for page {page_num_logging}: {e}")
    except Exception as e: print(f"Scraper Error: Unexpected error fetching page {page_num_logging}: {e}");
    return [], 0 # Default to 0 num_found if critical error


def scrape_bids_by_ministry_organization(
    ministry_name: str, 
    organization_name: str,
    max_total_iterations: int = 3 # Total number of full passes to attempt
) -> List[bid_schemas.BidCreate]:
    print(f"Scraper Service: Starting scrape for Ministry '{ministry_name}', Organization '{organization_name}' (Max iterations: {max_total_iterations})")
    
    all_unique_scraped_bids: List[bid_schemas.BidCreate] = []
    seen_bid_numbers: Set[str] = set()

    session = requests.Session()
    session.headers.update(settings.REQUEST_HEADERS)

    try:
        initial_response = session.get(str(settings.GEM_INITIAL_PAGE_URL), timeout=(30, 60))
        initial_response.raise_for_status()
    except requests.RequestException as e:
        print(f"Scraper Error: Failed to fetch initial page for CSRF: {e}. Aborting scrape.")
        return []

    csrf_token_value = session.cookies.get(settings.CSRF_COOKIE_NAME)
    if not csrf_token_value:
        print(f"Scraper Error: CSRF cookie '{settings.CSRF_COOKIE_NAME}' not found. Aborting scrape.")
        return []
    
    total_bids_reported_by_api = 0 # Will be updated by API responses
    
    MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION = 4 # Stop a single pass if this many pages add no new uniques
    MAX_PAGES_PER_ITERATION = 150 # Safety limit for pages in a single full pass/iteration

    for iteration_num in range(1, max_total_iterations + 1):
        print(f"\nScraper Service: Starting Iteration {iteration_num} of {max_total_iterations}")
        current_page_in_iteration = 1
        consecutive_no_new_unique_streak = 0
        bids_added_this_iteration = 0
        
        # This inner loop represents one full pass through the pages
        while True:
            payload_dict = {
                "searchType": "ministry-search", "ministry": ministry_name, "buyerState": "", 
                "organization": organization_name, "department": "", "bidEndFromMin": "", 
                "bidEndToMin": "", "page": current_page_in_iteration
            }
            payload_json_str = json.dumps(payload_dict)

            # Log which page of which iteration we are fetching
            page_log_str = f"Iter {iteration_num}, Page {current_page_in_iteration}"
            
            bids_from_api_page, num_found_call = _fetch_page_bids_from_api(
                session, payload_json_str, csrf_token_value, page_num_logging=page_log_str
            )

            # Update API's reported total if available and positive
            if num_found_call > 0:
                total_bids_reported_by_api = num_found_call
            
            if not bids_from_api_page and current_page_in_iteration == 1:
                # If first page of an iteration is empty, and API also reports 0, something is wrong or no bids
                if total_bids_reported_by_api == 0 :
                    print(f"Scraper Service: Iteration {iteration_num} - No bids found on first page and API reports 0 total. Ending this iteration.")
                else: # API reports items, but first page is empty - might be a temp issue or we overshot
                    print(f"Scraper Service: Iteration {iteration_num} - No bids on first page, but API reports {total_bids_reported_by_api}. Trying a few pages.")
                # This will likely trigger consecutive_no_new_unique_streak quickly if it persists
            
            newly_added_on_this_api_page = 0
            if bids_from_api_page:
                for bid_schema in bids_from_api_page:
                    if bid_schema.bid_number not in seen_bid_numbers:
                        seen_bid_numbers.add(bid_schema.bid_number)
                        all_unique_scraped_bids.append(bid_schema)
                        newly_added_on_this_api_page += 1
                
                # print(f"  {page_log_str}: Found {len(bids_from_api_page)} items, Added {newly_added_on_this_api_page} new. Total unique: {len(all_unique_scraped_bids)}")
            # else:
                # print(f"  {page_log_str}: No items from API. Total unique: {len(all_unique_scraped_bids)}")

            if newly_added_on_this_api_page == 0 : # If this API page yielded no *new* unique bids
                if current_page_in_iteration > 1 : # Don't count the very first page of an iteration against the streak if it was empty
                    consecutive_no_new_unique_streak += 1
            else: # New unique bids were found
                bids_added_this_iteration += newly_added_on_this_api_page
                consecutive_no_new_unique_streak = 0 # Reset streak

            # --- Stopping conditions for the CURRENT ITERATION (inner loop) ---
            if consecutive_no_new_unique_streak >= MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION:
                print(f"Scraper Service: Iteration {iteration_num} - Ending pass. No new unique bids for {MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION} consecutive pages (current page: {current_page_in_iteration}).")
                break
            
            if current_page_in_iteration >= MAX_PAGES_PER_ITERATION:
                print(f"Scraper Service: Iteration {iteration_num} - Reached max pages per pass ({MAX_PAGES_PER_ITERATION}). Ending this pass.")
                break
            
            # If API reports a total, and we've fetched pages that would exceed that total
            if total_bids_reported_by_api > 0:
                estimated_total_pages_by_api = math.ceil(total_bids_reported_by_api / settings.ITEMS_PER_PAGE)
                if current_page_in_iteration > estimated_total_pages_by_api + MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION: # Allow some leeway
                    print(f"Scraper Service: Iteration {iteration_num} - Current page {current_page_in_iteration} significantly exceeds API's estimated total pages ({estimated_total_pages_by_api}). Ending this pass.")
                    break
            
            # If the API returns an empty list of docs AND numFound is also 0 (or less than collected), consider ending pass
            if not bids_from_api_page and num_found_call <= len(all_unique_scraped_bids) and num_found_call > 0 and current_page_in_iteration > 1:
                print(f"Scraper Service: Iteration {iteration_num} - API returned no docs on page {current_page_in_iteration}, and numFound ({num_found_call}) isn't more than collected. Ending pass.")
                break


            current_page_in_iteration += 1
            time.sleep(0.6) # Sleep between page fetches within an iteration

        # --- End of one full iteration (inner loop) ---
        print(f"Scraper Service: Iteration {iteration_num} finished. Added {bids_added_this_iteration} new unique bids in this iteration.")
        print(f"Total unique bids collected so far: {len(all_unique_scraped_bids)}. API last reported total: {total_bids_reported_by_api}")

        # --- Stopping conditions for the OVERALL SCRAPE (outer loop) ---
        if total_bids_reported_by_api > 0 and len(all_unique_scraped_bids) >= total_bids_reported_by_api:
            print(f"Scraper Service: Collected count ({len(all_unique_scraped_bids)}) matches or exceeds API reported total ({total_bids_reported_by_api}). Stopping.")
            break 
        
        if bids_added_this_iteration == 0 and iteration_num > 1 : # If an iteration (not the first) adds no new bids
            print(f"Scraper Service: Iteration {iteration_num} yielded no new unique bids. Stopping further iterations.")
            break
        
        if iteration_num < max_total_iterations:
            print("Scraper Service: Pausing before next iteration...")
            time.sleep(3) # Longer pause between full iterations/passes
        
    # --- End of all iterations (outer loop) ---
    print(f"\nScraper Service: All scraping iterations complete. Collected {len(all_unique_scraped_bids)} unique bid schemas.")
    if total_bids_reported_by_api > 0:
        print(f"Scraper Service: API last reported a potential total of {total_bids_reported_by_api} bids.")
    
    return all_unique_scraped_bids


# app/services/scraper_service.py
# ... (other imports and parsing functions remain the same) ...
# ... (_fetch_page_bids_from_api also remains the same) ...
# ... (scrape_bids_by_ministry_organization also remains the same) ...

def scrape_bids_by_location(
    state_name: str,
    city_name: Optional[str], # City can be optional
    max_total_iterations: int = 1
) -> List[bid_schemas.BidCreate]:
    print(f"Scraper Service: Starting location scrape for State '{state_name}', City '{city_name or 'N/A'}' (Max iterations: {max_total_iterations})")
    
    all_unique_scraped_bids: List[bid_schemas.BidCreate] = []
    seen_bid_numbers: Set[str] = set()

    session = requests.Session()
    session.headers.update(settings.REQUEST_HEADERS)

    try:
        initial_response = session.get(str(settings.GEM_INITIAL_PAGE_URL), timeout=(30, 60))
        initial_response.raise_for_status()
    except requests.RequestException as e:
        print(f"Scraper Error: Failed to fetch initial page for CSRF (location scrape): {e}. Aborting scrape.")
        return []

    csrf_token_value = session.cookies.get(settings.CSRF_COOKIE_NAME)
    if not csrf_token_value:
        print(f"Scraper Error: CSRF cookie '{settings.CSRF_COOKIE_NAME}' not found (location scrape). Aborting scrape.")
        return []
    
    total_bids_reported_by_api = 0
    MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION = 4
    MAX_PAGES_PER_ITERATION = 150 # Safety limit for pages in a single full pass

    for iteration_num in range(1, max_total_iterations + 1):
        print(f"\nScraper Service: Location Scrape - Iteration {iteration_num} of {max_total_iterations}")
        current_page_in_iteration = 1 # API uses "page": 1 for first page in this payload
        consecutive_no_new_unique_streak = 0
        bids_added_this_iteration = 0
        
        while True:
            payload_dict = {
                "searchType": "con", # Specific for consignee/location search
                "state_name_con": state_name,
                "city_name_con": city_name or "", # API expects city_name_con, even if empty
                "bidEndFromCon": "", # As per your captured payload
                "bidEndToCon": "",   # As per your captured payload
                "page": current_page_in_iteration
            }
            payload_json_str = json.dumps(payload_dict)
            page_log_str = f"Location Iter {iteration_num}, Page {current_page_in_iteration}"
            
            bids_from_api_page, num_found_call = _fetch_page_bids_from_api(
                session, payload_json_str, csrf_token_value, page_num_logging=page_log_str
            )

            if num_found_call > 0:
                total_bids_reported_by_api = num_found_call
            
            if not bids_from_api_page and current_page_in_iteration == 1:
                if total_bids_reported_by_api == 0 :
                    print(f"Scraper Service: Location Iter {iteration_num} - No bids found on first page and API reports 0 total.")
                else:
                    print(f"Scraper Service: Location Iter {iteration_num} - No bids on first page, but API reports {total_bids_reported_by_api}.")
            
            newly_added_on_this_api_page = 0
            if bids_from_api_page:
                for bid_schema in bids_from_api_page:
                    if bid_schema.bid_number not in seen_bid_numbers:
                        seen_bid_numbers.add(bid_schema.bid_number)
                        all_unique_scraped_bids.append(bid_schema)
                        newly_added_on_this_api_page += 1
            
            if newly_added_on_this_api_page == 0 :
                if current_page_in_iteration > 1 : 
                    consecutive_no_new_unique_streak += 1
            else:
                bids_added_this_iteration += newly_added_on_this_api_page
                consecutive_no_new_unique_streak = 0

            # Log progress for this page
            # print(f"  {page_log_str}: Found {len(bids_from_api_page)} items, Added {newly_added_on_this_api_page} new. Total unique: {len(all_unique_scraped_bids)}. Streak: {consecutive_no_new_unique_streak}")


            # --- Stopping conditions for the CURRENT ITERATION (inner loop) ---
            if consecutive_no_new_unique_streak >= MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION:
                print(f"Scraper Service: Location Iter {iteration_num} - Ending pass. No new unique bids for {MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION} consecutive pages (current page: {current_page_in_iteration}).")
                break
            if current_page_in_iteration >= MAX_PAGES_PER_ITERATION:
                print(f"Scraper Service: Location Iter {iteration_num} - Reached max pages per pass ({MAX_PAGES_PER_ITERATION}). Ending this pass.")
                break
            if total_bids_reported_by_api > 0:
                estimated_total_pages_by_api = math.ceil(total_bids_reported_by_api / settings.ITEMS_PER_PAGE)
                if current_page_in_iteration > estimated_total_pages_by_api + MAX_CONSECUTIVE_PAGES_NO_NEW_UNIQUE_PER_ITERATION:
                    print(f"Scraper Service: Location Iter {iteration_num} - Current page {current_page_in_iteration} significantly exceeds API's estimated total pages ({estimated_total_pages_by_api}). Ending this pass.")
                    break
            if not bids_from_api_page and num_found_call <= len(all_unique_scraped_bids) and num_found_call > 0 and current_page_in_iteration > 1:
                print(f"Scraper Service: Location Iter {iteration_num} - API returned no docs on page {current_page_in_iteration}, and numFound ({num_found_call}) isn't more than collected. Ending pass.")
                break

            current_page_in_iteration += 1
            time.sleep(0.6) 

        print(f"Scraper Service: Location Iteration {iteration_num} finished. Added {bids_added_this_iteration} new unique bids.")
        print(f"Total unique bids collected so far for location: {len(all_unique_scraped_bids)}. API last reported total: {total_bids_reported_by_api}")

        if total_bids_reported_by_api > 0 and len(all_unique_scraped_bids) >= total_bids_reported_by_api:
            print(f"Scraper Service: Location - Collected count matches or exceeds API reported total. Stopping.")
            break 
        if bids_added_this_iteration == 0 and iteration_num > 1 :
            print(f"Scraper Service: Location Iteration {iteration_num} yielded no new unique bids. Stopping further iterations.")
            break
        if iteration_num < max_total_iterations:
            print("Scraper Service: Location - Pausing before next iteration...")
            time.sleep(5) 
        
    print(f"\nScraper Service: All location scraping iterations complete. Collected {len(all_unique_scraped_bids)} unique bid schemas.")
    if total_bids_reported_by_api > 0:
        print(f"Scraper Service: Location - API last reported a potential total of {total_bids_reported_by_api} bids.")
    
    return all_unique_scraped_bids

# In app/services/scraper_service.py
import httpx # Add import
import asyncio # Add import

async def _fetch_initial_page_async(session: httpx.AsyncClient) -> bool:
    """Attempts to fetch the initial page asynchronously."""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            print(f"Scraper Service Async: Attempt {attempt + 1} GET {settings.GEM_INITIAL_PAGE_URL}")
            response = await session.get(str(settings.GEM_INITIAL_PAGE_URL), timeout=httpx.Timeout(30.0, read=60.0))
            response.raise_for_status()
            print("Scraper Service Async: Initial GET successful.")
            return True
        except httpx.TimeoutException:
            print(f"Scraper Warning Async: Initial GET timed out (Attempt {attempt + 1}).")
        except httpx.RequestError as e:
             print(f"Scraper Warning Async: Initial GET failed (Attempt {attempt + 1}): {e}")
        
        if attempt < max_retries - 1:
            wait_time = 2 * (attempt + 1)
            print(f"Scraper Service Async: Waiting {wait_time}s before retry...")
            await asyncio.sleep(wait_time) # Use asyncio.sleep
    return False

# Modify the start of BOTH scraping functions to use httpx for initial GET
async def scrape_bids_by_location_async( # Make the function async
    state_name: str, city_name: Optional[str], max_total_iterations: int = 1
) -> List[bid_schemas.BidCreate]:
    print(f"Scraper Async: Starting location scrape for State '{state_name}'...")
    all_unique_scraped_bids: List[bid_schemas.BidCreate] = []
    seen_bid_numbers: Set[str] = set()

    # Use httpx.AsyncClient as a context manager
    async with httpx.AsyncClient(headers=settings.REQUEST_HEADERS, follow_redirects=True) as client:
        if not await _fetch_initial_page_async(client):
            print("Scraper Async Error: Failed initial page fetch. Aborting.")
            return []

        csrf_token_value = client.cookies.get(settings.CSRF_COOKIE_NAME)
        if not csrf_token_value:
             print(f"Scraper Async Error: CSRF cookie '{settings.CSRF_COOKIE_NAME}' not found. Aborting.")
             return []
        
        # --- IMPORTANT ---
        # The REST of the loop needs to use await client.post(...) instead of session.post(...)
        # The _fetch_page_bids_from_api helper needs to be converted to async using httpx
        # --- This requires more extensive changes to the scraping loop ---
        
        # Placeholder - returning empty until loop is converted
        print("Scraper Async: Placeholder - loop needs conversion to httpx.")
        return [] 

# app/services/scraper_service.py




# --- Standalone Test Block ---
if __name__ == "__main__":
    
    # --- Test Location Scraping ---
    print("Testing scraper_service: scrape_bids_by_location...")
    # Ensure .env is loaded relative to this script if needed, 
    # or run using 'python -m app.services.scraper_service' from root
    test_state = "MAHARASHTRA"
    test_city = "PUNE"
    test_iterations = 1 # Or 0 for just one pass if default is 1 pass

    scraped_data_location = scrape_bids_by_location(
        state_name=test_state, 
        city_name=test_city, 
        max_total_iterations=test_iterations
    )

    if scraped_data_location:
        print(f"\nStandalone Test: Successfully scraped {len(scraped_data_location)} unique bids for {test_state}/{test_city}.")
        print("First 3 bid numbers and items:")
        for i, bid_schema in enumerate(scraped_data_location[:3]):
            print(f"  {i+1}. Bid Number: {bid_schema.bid_number}, Items: {bid_schema.items[:60]}...")
        
        # Optionally save results
        output_filename = f"bids_{test_state}_{test_city}.json"
        try:
            with open(output_filename, "w") as f:
               json.dump([s.dict() for s in scraped_data_location], f, indent=2, default=str)
            print(f"Saved data to {output_filename}")
        except Exception as e:
            print(f"Error saving to JSON: {e}")

    else:
        print(f"Standalone Test: No data scraped for {test_state}/{test_city}.")

    # --- Optionally Test Ministry Scraping (Comment out if not needed) ---
    # print("\nTesting scraper_service: scrape_bids_by_ministry_organization...")
    # test_ministry = "Ministry of Defence"
    # test_organization = "Armoured Vehicles Nigam Limited" 
    # test_iterations_ministry = 1 

    # scraped_data_ministry = scrape_bids_by_ministry_organization(
    #     ministry_name=test_ministry, 
    #     organization_name=test_organization, 
    #     max_total_iterations=test_iterations_ministry
    # )
    # if scraped_data_ministry:
    #     print(f"\nStandalone Test: Successfully scraped {len(scraped_data_ministry)} unique bids for {test_ministry}/{test_organization}.")
    # else:
    #     print(f"Standalone Test: No data scraped for {test_ministry}/{test_organization}.")