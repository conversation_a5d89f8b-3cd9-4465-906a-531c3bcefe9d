#!/usr/bin/env python3
"""
Comprehensive PDF Processing Test Script

This script takes a directory containing bid PDFs and processes them using
our PDF processing functionality. It shows which PDFs are accepted/rejected
and the reasons for each decision.

Usage:
    python test_pdf_processing.py <directory_path>
    python test_pdf_processing.py --help

Examples:
    python test_pdf_processing.py "../../bids/NILAY 2"
    python test_pdf_processing.py "../../bids" --output results.json
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path
from typing import List, Dict, Any

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.pdf_processor_service import analyze_with_rules, BidAnalysisResult
from app.config.criteria_config import (
    DEFAULT_CRITERIA,
    print_criteria_summary
)

class PDFProcessingTester:
    """Comprehensive PDF processing tester"""

    def __init__(self):
        """Initialize with default criteria"""
        self.criteria = DEFAULT_CRITERIA
        self.results = []
        
    def find_pdf_files(self, directory: str) -> List[str]:
        """Find all PDF files in directory and subdirectories"""
        pdf_files = []
        
        if not os.path.exists(directory):
            print(f"❌ Directory not found: {directory}")
            return pdf_files
        
        print(f"🔍 Searching for PDF files in: {directory}")
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_path = os.path.join(root, file)
                    pdf_files.append(pdf_path)
        
        print(f"📄 Found {len(pdf_files)} PDF files")
        return sorted(pdf_files)
    
    def process_single_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """Process a single PDF and return results"""
        print(f"\n📄 Processing: {Path(pdf_path).name}")
        
        start_time = time.time()
        
        try:
            # Use rule-based analysis (no API calls)
            result = analyze_with_rules(pdf_path, self.criteria)
            
            processing_time = time.time() - start_time
            
            # Create result summary
            result_summary = {
                "pdf_name": Path(pdf_path).name,
                "pdf_path": pdf_path,
                "status": result.status,
                "confidence": result.confidence_score,
                "processing_time": processing_time,
                "reasons": result.reasons,
                "restrictive_clauses": result.buyer_terms_analysis.get('restrictive_clauses_found', []),
                "emd_present": result.emd_analysis.get('emd_present', False)
            }
            
            # Print immediate results
            status_icon = "✅" if result.status == "ACCEPT" else "❌"
            print(f"{status_icon} Status: {result.status}")
            print(f"🎯 Confidence: {result.confidence_score:.3f}")
            print(f"⏱️  Processing Time: {processing_time:.3f}s")
            
            if result.status == "REJECT" and result.reasons:
                print("📋 Rejection Reasons:")
                for reason in result.reasons[:2]:  # Show first 2 reasons
                    print(f"   - {reason[:80]}...")
            
            if result.buyer_terms_analysis.get('restrictive_clauses_found'):
                print("🚫 Restrictive Clauses Found:")
                for clause in result.buyer_terms_analysis['restrictive_clauses_found'][:1]:
                    print(f"   - {clause[:80]}...")
            
            return result_summary
            
        except Exception as e:
            processing_time = time.time() - start_time
            print(f"❌ ERROR: {e}")
            
            return {
                "pdf_name": Path(pdf_path).name,
                "pdf_path": pdf_path,
                "status": "ERROR",
                "confidence": 0.0,
                "processing_time": processing_time,
                "reasons": [f"Processing error: {str(e)}"],
                "restrictive_clauses": [],
                "emd_present": False
            }
    
    def process_directory(self, directory: str) -> Dict[str, Any]:
        """Process all PDFs in directory"""
        print("=" * 80)
        print("PDF PROCESSING TEST")
        print("=" * 80)
        print(f"Directory: {directory}")
        print()
        
        # Show criteria summary
        print_criteria_summary(self.criteria)
        print()
        
        # Find PDF files
        pdf_files = self.find_pdf_files(directory)
        
        if not pdf_files:
            return {
                "summary": {"total": 0, "accepted": 0, "rejected": 0, "errors": 0},
                "results": [],
                "processing_time": 0.0
            }
        
        # Process each PDF
        start_time = time.time()
        
        for pdf_path in pdf_files:
            result = self.process_single_pdf(pdf_path)
            self.results.append(result)
        
        total_time = time.time() - start_time
        
        # Generate summary
        summary = self.generate_summary(total_time)
        
        return {
            "summary": summary,
            "results": self.results,
            "processing_time": total_time,
            "directory": directory
        }
    
    def generate_summary(self, total_time: float) -> Dict[str, Any]:
        """Generate processing summary"""
        total = len(self.results)
        accepted = sum(1 for r in self.results if r["status"] == "ACCEPT")
        rejected = sum(1 for r in self.results if r["status"] == "REJECT")
        errors = sum(1 for r in self.results if r["status"] == "ERROR")
        
        avg_time = total_time / total if total > 0 else 0
        avg_confidence = sum(r["confidence"] for r in self.results) / total if total > 0 else 0
        
        print(f"\n" + "=" * 80)
        print("PROCESSING SUMMARY")
        print("=" * 80)
        print(f"Total PDFs Processed: {total}")
        print(f"✅ Accepted: {accepted} ({accepted/total*100:.1f}%)" if total > 0 else "✅ Accepted: 0")
        print(f"❌ Rejected: {rejected} ({rejected/total*100:.1f}%)" if total > 0 else "❌ Rejected: 0")
        print(f"🚫 Errors: {errors} ({errors/total*100:.1f}%)" if total > 0 else "🚫 Errors: 0")
        print(f"⏱️  Total Time: {total_time:.2f}s")
        print(f"📊 Average Time per PDF: {avg_time:.3f}s")
        print(f"🎯 Average Confidence: {avg_confidence:.3f}")
        
        # Show breakdown by status
        if rejected > 0:
            print(f"\n📋 REJECTED PDFs:")
            for result in self.results:
                if result["status"] == "REJECT":
                    print(f"   ❌ {result['pdf_name']}")
                    if result["reasons"]:
                        print(f"      → {result['reasons'][0][:60]}...")
        
        if accepted > 0:
            print(f"\n✅ ACCEPTED PDFs:")
            for result in self.results:
                if result["status"] == "ACCEPT":
                    print(f"   ✅ {result['pdf_name']}")
        
        if errors > 0:
            print(f"\n🚫 ERROR PDFs:")
            for result in self.results:
                if result["status"] == "ERROR":
                    print(f"   🚫 {result['pdf_name']}: {result['reasons'][0] if result['reasons'] else 'Unknown error'}")
        
        return {
            "total": total,
            "accepted": accepted,
            "rejected": rejected,
            "errors": errors,
            "acceptance_rate": accepted/total*100 if total > 0 else 0,
            "rejection_rate": rejected/total*100 if total > 0 else 0,
            "error_rate": errors/total*100 if total > 0 else 0,
            "total_time": total_time,
            "avg_time_per_pdf": avg_time,
            "avg_confidence": avg_confidence
        }
    
    def save_results(self, output_file: str, full_results: Dict[str, Any]):
        """Save results to JSON file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(full_results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Results saved to: {output_file}")
        except Exception as e:
            print(f"\n❌ Error saving results: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Test PDF processing functionality on a directory of bid PDFs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_pdf_processing.py "../../bids/NILAY 2"
  python test_pdf_processing.py "../../bids" --output results.json
        """
    )
    
    parser.add_argument(
        "directory",
        help="Directory containing PDF files to process"
    )
    
# Removed criteria argument - using single strict criteria only
    
    parser.add_argument(
        "--output",
        help="Output file to save results (JSON format)"
    )
    
    args = parser.parse_args()
    
    # Validate directory
    if not os.path.exists(args.directory):
        print(f"❌ Error: Directory '{args.directory}' does not exist")
        sys.exit(1)
    
    # Initialize tester
    tester = PDFProcessingTester()
    
    # Process directory
    results = tester.process_directory(args.directory)
    
    # Save results if requested
    if args.output:
        tester.save_results(args.output, results)
    
    print(f"\n🎉 Processing complete!")

if __name__ == "__main__":
    main()
