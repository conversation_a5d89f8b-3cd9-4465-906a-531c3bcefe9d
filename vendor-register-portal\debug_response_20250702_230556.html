<!DOCTYPE html>

<html lang="en">

    <head>

        <meta charset="utf-8" />

        <!-- <title>DoO(C&S) Vendor Panel | Page ,abd0efe7fa08b664fc5d57fac09ba5cf30e42408</title> -->

       <!-- <title>Home : INDIAN ORDNANCE FACTORIES </title> -->

                <title>Search | INDIAN ORDNANCE FACTORIES</title>

        <meta name="viewport" content="width=device-width, initial-scale=1" />

        <meta name="keywords" content="DoO(C&S) Vendor Panel

        , Page ,1533c3ae37025027f0ef8974a6a38547735ce1ba        ">

        <meta http-equiv="X-UA-Compatible" content="IE=edge" />

        <meta name="description" content="DoO(C&S) Vendor Panel

        , Page ,ad7812397af3c9ffb2b4ee463a7f92ed823598b2        ">



        <!-- The styles -->

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/bootstrap-cerulean.min.css" rel="stylesheet" />

        <!-- <link id="bs-css" href="https://ddpdoo.gov.in/vendor/resource/admin/css/bootstrap-cerulean.min.css" rel="stylesheet"> -->



        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/charisma-app.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/fullcalendar/dist/fullcalendar.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/fullcalendar/dist/fullcalendar.print.css" rel="stylesheet" media="print" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/chosen/chosen.min.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/colorbox/example3/colorbox.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/responsive-tables/responsive-tables.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/bootstrap-tour/build/css/bootstrap-tour.min.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/jquery.noty.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/noty_theme_default.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/elfinder.min.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/elfinder.theme.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/jquery.iphone.toggle.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/uploadify.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/admin/css/animate.min.css" rel="stylesheet" />

        <link href="https://ddpdoo.gov.in/vendor/resource/jquery_ui/jquery-ui.min.css" rel="stylesheet" />

        <!-- <link id="bs-css" href="https://ddpdoo.gov.in/vendor/resource/jquery_ui/jquery-ui.min.css" rel="stylesheet"> -->

        <link rel="stylesheet" type="text/css" href="https://ddpdoo.gov.in/vendor/resource/css/ofb.css" media="all">
        <!-- jQuery -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/jquery/jquery.min.js"></script>

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>

        <!-- The HTML5 shim, for IE6-8 support of HTML5 elements -->

        <!--[if lt IE 9]>

            <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>

        <![endif]-->



        <!-- The fav icon -->

        <link rel="shortcut icon" href="https://ddpdoo.gov.in/vendor/resource/admin/img/favicon.ico" />

        <style>

          h1::before {

    content: "";

    position: absolute;

    width: 45px;

    height: 2px;

    background-color: white;

    top: -10px;}

        </style>



        <!-- <script type="text/javascript"> -->

        <script>



            	var validNavigation = false;



            function endSession() {

              // Browser or broswer tab is closed

              // Do sth here ...

              $.get("https://ddpdoo.gov.in/vendor/login/logout");

            }



            function wireUpEvents() {

              /*

              * For a list of events that triggers onbeforeunload on IE

              * check http://msdn.microsoft.com/en-us/library/ms536907(VS.85).aspx

              */

              window.onbeforeunload = function() {

                  if (!validNavigation) {

                     endSession();

                  }

              }



              // Attach the event keypress to exclude the F5 refresh

              $(document).on('keypress keydown keyup', function(e) {



                  validNavigation = true;

                //}

              });



              // Attach the event click for all links in the page

              $("a").bind("click", function() {

                validNavigation = true;

              });



              // Attach the event submit for all forms in the page

              $("form").bind("submit", function() {

                validNavigation = true;

              });



              // Attach the event click for all inputs in the page

              $("input[type=submit]").bind("click", function() {

                validNavigation = true;

              });



            }



            // Wire up the events as soon as the DOM tree is ready

            $(document).ready(function() {

              wireUpEvents();

            });



            	var basr_url = "https://ddpdoo.gov.in/vendor/";

            	$(document).ready(function(){

            		$('.delete').on('click',function(){

            			if(confirm('Do you want to delete?'))

            				return true;

            			else

            				return false;

            		})

            	})



            		$(document).ready(function(){

            			$(".regen_captcha").on("click",function(){

            				$.get( "https://ddpdoo.gov.in/vendor/login/ajax_recreate_captcha", function( data ) {

            				  $( "#re_capcha" ).html( data );

            				  //alert( "Load was performed." );

            				});

            		});

            	});



            	$(window).load(function() {



            		   if($(window).width()>768){

            		   var divtwo = $("#parentDiv").height();





            			$("#menulf").height(divtwo-0);

            			//jQuery("#right-sidebar").height(maxdiv);



            		   }

            		});

            		$(window).load(function () {

            		$(window).resize(function(){

            	    	if($(window).width()>768){

            		   var divtwo = $("#parentDiv").height();





            			$("#menulf").height(divtwo-0);

            			//jQuery("#right-sidebar").height(maxdiv);



            		   }

            		});

            		$(window).resize();



            			

            		});



            		//auto compleate off

            		$(document).ready(function(){

            			$('form').attr('autocomplete', 'off');

            			$( document ).on( 'focus', ':input', function(){

            				$( this ).attr( 'autocomplete', 'off' );

            			});

            		});

        </script>

    </head>



    <body>

        <main class="outer-area">

            <!-- topbar starts -->

            <div class="navbar navbar-default" role="navigation">

                <div class="navbar-inner">

                <div class="container-fluid">

    <div class="row">

        <!-- Left Section: Logo and Toggle Button -->

        <div class="col-xs-12 col-sm-2 col-lg-2 colnogapL">

            <button type="button" class="navbar-toggle pull-left animated flip" data-toggle="collapse" aria-expanded="false" aria-controls="mobile-menu" data-target="#navbar" style="background-color: black;margin: 10px;">

                <span class="sr-only">Toggle navigation</span>

                <span class="icon-bar"></span>

                <span class="icon-bar"></span>

                <span class="icon-bar"></span>

            </button>

            

            <div class="logoSection clearfix text-center text-sm-left">

                <div class="logogrey">

                    <img alt="INDIAN ORDNANCE FACTORIES logo" style="height: 100px;" title="OFB" src="https://ddpdoo.gov.in/vendor/resource/images/grey_logo.gif" class="" />

                </div>

                <div class="logo_text">INDIAN ORDNANCE FACTORIES</div>

            </div>

        </div>



        <!-- Right Section: Heading and Logo -->

        <div class="col-xs-12 col-sm-10 col-lg-10 top-gapL">

            <div class="row">

                <!-- Heading -->

                <div class="col-xs-8 col-sm-10 text-sm-left">

                    <h1 class="logohead" style="color: black; font-size: 20px; display: block; padding-top: 20px;line-height: 25px;margin-top: 4%">

                        Centralised Online Vendor Registration Portal

                    </h1>

                </div>

                <!-- OFB Logo -->

                <div class="col-xs-2 col-sm-2 text-center text-sm-right">

                    <img style="height: 95px;" alt="ORDNANCE FACTORIES BOARD logo" title="OFB" src="https://ddpdoo.gov.in/vendor/resource/images/ofb_logo.jpg" class="" />

                </div>

            </div>

        </div>

    </div>



    <!-- user dropdown starts -->

        <!-- user dropdown ends -->

</div>

            </div>



            <!-- topbar ends -->

            <div class="ch-container" id="parentDiv">

                <div class="row">

                    <!-- left menu starts -->

                                        <div class="col-sm-2 col-lg-2 colnogapL">

                        <div class="sidebar-nav" id="menulf">

                            <div class="nav-canvas">

  <div class="nav-sm nav nav-stacked"> </div>

  <ul class="nav nav-pills nav-stacked main-menu">

    <!--<li class="nav-header">Ordnance Factory Board</li> -->

    <li><a class="ajax-link" href="https://ddpdoo.gov.in/vendor/welcome">About OFB</a></li>

            	<li><a href="https://ddpdoo.gov.in/vendor/advertisement" class="ajax-link">Items open for Registration</a></li>

        <li><a class="ajax-link" href="https://ddpdoo.gov.in/vendor/login">Login </a></li>

        <li><a class="ajax-link" href="https://ddpdoo.gov.in/vendor/vendor/vendor_registration">Enrolment </a></li>

        <li><a class="ajax-link" href="https://ddpdoo.gov.in/vendor/general_reports/show/registered_vendors">Registered Vendors</a></li>

        

    <li><a class="ajax-link" href="https://ddpdoo.gov.in/vendor/static/faq">FAQ</a></li>

    <li><a class="ajax-link" href="https://ddpdoo.gov.in/vendor/static/help">Help </a></li>

  </ul>

  

</div>

                        </div>

                    </div>

                                        <!--/span-->

                    <!-- left menu ends -->



                    <noscript>

                        <div class="alert alert-block col-md-12">

                            <h4 class="alert-heading">Warning!</h4>



                            <p>You need to have <a href="http://en.wikipedia.org/wiki/JavaScript" target="_blank">JavaScript</a> enabled to use this site.</p>

                        </div>

                    </noscript>



                    <div id="content" class="col-lg-10 col-sm-10">

                        <!-- content starts -->



                        <div>

  <ul class="breadcrumb">

    <li><a href="https://ddpdoo.gov.in/vendor/admin/dashboard">Home</a></li>

    <li><a href="https://ddpdoo.gov.in/vendor/mis_reports">List of Reports</a></li>

    <li><a >List of registered vendors</a></li>

  </ul>

</div>

<div class="row">

  <div class="box col-md-12">

    <div class="box-inner">

      <div class="box-header well" data-original-title="">

        <h2><i class="glyphicon glyphicon-user"></i>List of registered vendors</h2>

      </div>

      <!--search panel-->

      <div class="box-content">

		<fieldset>

			<legend><i class="glyphicon glyphicon-search" ></i> Search</legend>

			<form action="https://ddpdoo.gov.in/vendor/general_reports/show/registered_vendors/search" id="wrd_frm" role="form" data-toggle="validator" enctype="multipart/form-data" method="post" accept-charset="utf-8">
<input type="hidden" name="csrf_ofb" value="894f833b51ebec3512ad1773aae2168d" style="display:none;" />
			<div class="row mgb10">

				<div class="col-md-2 mgb10">

					<label for="factory_id">Select Factory</label>

					<select name="factory_id" id="factory_id" class="form-control">
<option value="0">Select Factory</option>
<option value="1">Ammunition Factory Khadki</option>
<option value="2">Cordite Factory Arvankadu</option>
<option value="44">Engine Factory Avadi</option>
<option value="18">Field Gun Factory Kanpur</option>
<option value="20">Grey Iron Foundry</option>
<option value="13">Gun and Shell Factory</option>
<option value="11">Gun Carriage Factory</option>
<option value="43">Heavy Alloys Penetrator Project</option>
<option value="35">Heavy Vehicle Factory</option>
<option value="3">High Explosive Factory</option>
<option value="26">Machine Tool Proto Type Factory</option>
<option value="21">Metal and Steel Factory</option>
<option value="45">Opto Electronic Factory</option>
<option value="27">Ordnance Cable Factory Chandigarh</option>
<option value="33">Ordnance Clothing Factory Avadi</option>
<option value="31">Ordnance Clothing Factory Shahjahanpur</option>
<option value="30">Ordnance Equipment Factory</option>
<option value="34">Ordnance Equipment Factory Hazaratpur</option>
<option value="28">Ordnance Factory Ambajhari</option>
<option value="22">Ordnance Factory Ambernath</option>
<option value="5">Ordnance Factory Bhandara</option>
<option value="25">Ordnance Factory Bhusawal</option>
<option value="40">Ordnance Factory Board</option>
<option value="46">Ordnance Factory Board</option>
<option value="10">Ordnance Factory Bolangir</option>
<option value="7">Ordnance Factory Chanda</option>
<option value="39">Ordnance Factory Dehradun</option>
<option value="9">Ordnance Factory Dehu Road</option>
<option value="16">Ordnance Factory Dumdum</option>
<option value="8">Ordnance Factory Itarsi</option>
<option value="14">Ordnance Factory Kanpur</option>
<option value="23">Ordnance Factory Katni</option>
<option value="4">Ordnance Factory Khamaria</option>
<option value="42">Ordnance Factory Medak</option>
<option value="24">Ordnance Factory Muradnagar</option>
<option value="36">Ordnance Factory Nalanda</option>
<option value="47">Ordnance Factory Project Korwa</option>
<option value="17">Ordnance Factory Tiruchirapalli</option>
<option value="6">Ordnance Factory Varangaon</option>
<option value="32">Ordnance Parachute Factory</option>
<option value="12">Rifle Factory Ishapur</option>
<option value="15">Small Arms Factory</option>
<option value="19">Vehicle Factory Jabalpur</option>
</select>
				</div>

				<div class="col-md-2 mgb10">

					<label for="location">Location</label>

					<input type="text" id="location" name="location" title="Location" class="form-control" value="Rajkot">

				</div>

				<div class="col-md-2 mgb10">

					<label for="industry_category">Industry category</label>

					<select name="industry_category" id="industry_category" class="form-control">
<option value="" selected="selected">Category</option>
<option value="L">Large Scale</option>
<option value="M">Medium Scale</option>
<option value="S">Micro and Small Enterprises</option>
<option value="SS">Micro and Small Enterprises (owned by SC/ST entrepreneurs)</option>
</select>
				</div>

				<div class="col-md-2 mgb10">

					<label for="product">Product</label>

					<input type="text" id="product" name="product" title="Product" class="form-control filter_product" value="">

				</div>

				<div class="col-md-2 mgb10">

					<label for="used_for">Used For</label>

					<select name="used_for"  id="used_for" class="form-control">
<option value="" selected="selected">Select Used for</option>
<option value="R">Raw material</option>
<option value="C">Component</option>
<option value="E">End Product</option>
<option value="S">Special Tools</option>
<option value="P">Production Consumables</option>
</select>
				</div>

				<div class="col-md-2 mgb10">

					<label for="vendor">Vendor name</label>

					<input type="text" id="vendor" name="vendor" title="Vendor name" class="form-control" value="">

				</div>

			</div>

			<div class="row">

				<div class="col-md-3">

					<button type="submit" name="search" value="search" class="btn btn-info">Search</button>

					<a href="https://ddpdoo.gov.in/vendor/general_reports/show/registered_vendors" class="btn btn-info">Reset</a>

					<input type="button" value="Print" onclick="" class="btn btn-success" id="Print">

				</div>

			</div>

        	</form>		</fieldset>

       <p>&nbsp;</p>

	   <fieldset>

				<legend><i class="glyphicon glyphicon-sort"></i> Sort by </legend>

				<form action="https://ddpdoo.gov.in/vendor/general_reports/set_sort/registered_vendors" id="srt_frm" role="form" data-toggle="validator" enctype="multipart/form-data" method="post" accept-charset="utf-8">
<input type="hidden" name="csrf_ofb" value="894f833b51ebec3512ad1773aae2168d" style="display:none;" />
        <div class="row mgb10">

          <div class="col-md-3 mgb10">

          	<input type="hidden" name="current_url" value="%2Fvendor%2Fgeneral_reports%2Fshow%2Fregistered_vendors%2Fsearch"  >

            <select name="sort_by" class="form-control" onchange="this.form.submit()">
<option value="valid_upto">Register Upto</option>
<option value="name">Vendor Name</option>
</select>
          </div>

        </div>

        </form>      

      </div> 		</fieldset>

    

      <!--search panel ends-->

      <div class="box-content" id="print_div">

                <div class="table-responsive">

                <table class="table  table-bordered bootstrap-datatable responsive" id="example">

			<thead>

			<tr>

				<th >No</th>

				<th >Name, Address & Phone No.</th>

				<th >Vendor Code</th>

				<th >Factory</th>

				<!--<th width="20%">Vendor Code</th>-->

				<th >Products</th>

				<!--<th width="5%">Registered Upto</th>-->

				<th >Category, Grading, Size</th>

			</tr>

			</thead>

          <tbody>

                        <tr>

            	<td colspan="6" id="pag_nav"></td>

            </tr>

          </tbody>

        </table>

        </div>

        <div class="" id="print_foot" style="display:none;">

	<div style="float:left">Signature</div>

    <div style="float:right">02-07-2025</div>

</div>      </div>

    </div>

  </div>

</div>

<div class="modal fade" id="item_modal" tabindex="-1" role="dialog">

  <div class="modal-dialog">

    <div class="modal-content">

      <div class="modal-header">

        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>

        <h4 class="modal-title">Item Details</h4>

      </div>

      <div class="modal-body"> </div>

      <div class="modal-footer">

        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>

        <button type="button" class="btn btn-primary">Send</button>

      </div>

    </div>

    <!-- /.modal-content -->

  </div>

  <!-- /.modal-dialog -->

</div>

<script >

$(function(){



$("#item_modal").on("show.bs.modal", function(e) {

    var link = $(e.relatedTarget);

    $(this).find(".modal-body").html(link.next(".show_dtls_div").html());

});

})

</script>

<script  src="https://ddpdoo.gov.in/vendor/resource/js/printThis.js"></script>
<script >

	$(document).ready(function(){

		document.getElementById("Print").onclick = function () {

		 

		 $("#print_div").printThis({ 

				debug: false, 

				importCSS:false,

				printContainer: true,    

				pageTitle: "List of registered vendors", 

				loadCSS: ["https://ddpdoo.gov.in/vendor/resource/bootstrap/css/bootstrap.min.css","https://ddpdoo.gov.in/vendor/resource/css/form.css"],

				removeInline: false,        

				printDelay: 150,            

				header: null,             

				formValues: true,

				callback: function(){

							$(this).addhtml($("#print_foot").html());

							//$(this).css('width','100%');

							//$(this).css('word-break','break-all');

							$(this).addcss([ {"style" : "width","value":"100%" },{"style" : "word-break","value":"break-all"},{"style":"font-size","value":"9px" }]);

							$(this).delhtml('pag_nav');

							//$("#pag_nav").hide();

						}          

			}); 

		};

	});

</script>



                        <!-- content ends -->

                    </div>

                    <!--/#content.col-md-0-->

                </div>

                <!--/fluid-row-->

            </div>

            <!--/.fluid-container-->



            <!--Footer-->

            <div class="container-fluid">

                <footer class="row footer-panel">

        <p class="col-md-9 col-sm-9 col-xs-12 copyright">Copyright &copy; 2025 - OFB</p>



      

    </footer>            </div>



            <!-- external javascript -->

        </main>



        <!-- library for cookie management -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.cookie.js"></script>

        <!-- calender plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/moment/min/moment.min.js"></script>

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/fullcalendar/dist/fullcalendar.min.js"></script>

        <!-- data table plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.dataTables.min.js"></script>



        <!-- select or dropdown enhancer -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/chosen/chosen.jquery.min.js"></script>

        <!-- plugin for gallery image view -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/colorbox/jquery.colorbox-min.js"></script>

        <!-- notification plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.noty.js"></script>

        <!-- library for making tables responsive -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/responsive-tables/responsive-tables.js"></script>

        <!-- tour plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/bower_components/bootstrap-tour/build/js/bootstrap-tour.min.js"></script>

        <!-- star rating plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.raty.min.js"></script>

        <!-- for iOS style toggle switch -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.iphone.toggle.js"></script>

        <!-- autogrowing textarea plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.autogrow-textarea.js"></script>

        <!-- multiple file upload plugin -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.uploadify-3.1.min.js"></script>

        <!-- history.js for cross-browser state change on ajax -->

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/jquery.history.js"></script>

        <script src="https://ddpdoo.gov.in/vendor/resource/bootstrap/js/validator.js"></script>

        <script src="https://ddpdoo.gov.in/vendor/resource/jquery/jquery-ui.min.js"></script>

        <script  src="https://ddpdoo.gov.in/vendor/resource/js/bootbox.min.js"></script>
        <script  src="https://ddpdoo.gov.in/vendor/resource/js/custom.js"></script>
        <script>

            $(function () {

                $(document).on("keydown", ':input[type="number"]', function (event) {

                    var keyCode = event.keyCode;



                    if (keyCode == 69) event.preventDefault();

                });

            });

        </script>

        <script src="https://ddpdoo.gov.in/vendor/resource/admin/js/charisma.js"></script>

    </body>

</html>





<script>

function fixPaginationAccessibility() {

    const paginationNav = document.querySelector("#pag_nav nav");

    const pagination = document.querySelector("#pag_nav ul.pagination");



    if (paginationNav) {

        paginationNav.setAttribute("aria-label", "pagination");

    }



    if (!pagination) return;



    pagination.querySelectorAll("li").forEach(li => {

        const a = li.querySelector("a");

        if (!a) return;



        const text = a.textContent.trim().toLowerCase();



        if (li.getAttribute("aria-current") === "true") {

            li.setAttribute("aria-current", "page");

            a.setAttribute("aria-label", `page ${a.textContent.trim()}`);

        } else if (text === "next") {

            a.setAttribute("aria-label", "next page");

        } else if (text === "last") {

            a.setAttribute("aria-label", "previous page");

        } else if (!isNaN(text)) {

            a.setAttribute("aria-label", `page ${text}`);

        }

    });

}



// Initial run

fixPaginationAccessibility();



// Observe changes in #pag_nav (where pagination lives)

const observerTarget = document.getElementById("pag_nav");



if (observerTarget) {

    const observer = new MutationObserver(() => {

        fixPaginationAccessibility();

    });



    observer.observe(observerTarget, {

        childList: true,

        subtree: true

    });

}





    observer.observe(observerTarget, {

        childList: true,

        subtree: true

    });

}

</script>





