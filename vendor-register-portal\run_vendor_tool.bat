@echo off
REM ============================================================================
REM Vendor Registration Portal API Tool - Batch Runner
REM ============================================================================
REM This batch file provides an easy way to run the Vendor API Tool
REM Author: AI Assistant
REM Date: 2025-07-02
REM ============================================================================

setlocal enabledelayedexpansion

REM Set the script directory
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM Colors for output (if supported)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set RESET=[0m

echo.
echo %BLUE%============================================================================%RESET%
echo %BLUE%    VENDOR REGISTRATION PORTAL API TOOL%RESET%
echo %BLUE%    Extract vendor data and generate Excel reports%RESET%
echo %BLUE%============================================================================%RESET%
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%Error: Python is not installed or not in PATH%RESET%
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

echo %GREEN%✓ Python found%RESET%

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo %RED%Error: pip is not available%RESET%
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo %GREEN%✓ pip found%RESET%

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo %RED%Error: requirements.txt not found%RESET%
    echo Please ensure you're running this from the correct directory
    pause
    exit /b 1
)

REM Install/update dependencies
echo.
echo %YELLOW%Installing/updating dependencies...%RESET%
pip install -r requirements.txt
if errorlevel 1 (
    echo %RED%Error: Failed to install dependencies%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Dependencies installed%RESET%

REM Display menu
:MENU
echo.
echo %BLUE%Select an option:%RESET%
echo 1. Run Interactive UI (Recommended for beginners)
echo 2. Run Command Line Tool with parameters
echo 3. Run Command Line Tool with configuration file
echo 4. View help and examples
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto INTERACTIVE
if "%choice%"=="2" goto CMDLINE
if "%choice%"=="3" goto CONFIG
if "%choice%"=="4" goto HELP
if "%choice%"=="5" goto EXIT

echo %RED%Invalid choice. Please select 1-5.%RESET%
goto MENU

:INTERACTIVE
echo.
echo %YELLOW%Starting Interactive UI...%RESET%
python interactive_ui.py
if errorlevel 1 (
    echo %RED%Error occurred while running interactive UI%RESET%
    pause
)
goto MENU

:CMDLINE
echo.
echo %YELLOW%Running Command Line Tool...%RESET%
echo.
echo Enter search parameters (leave blank to skip):
set /p factory_id="Factory ID: "
set /p location="Location: "
set /p industry_category="Industry Category (L/M/S): "
set /p product="Product: "
set /p used_for="Used For (R/C/I): "
set /p vendor="Vendor: "
set /p output_file="Output filename (default: vendor_report.xlsx): "

if "%output_file%"=="" set output_file=vendor_report.xlsx

set cmd_args=--output "%output_file%"
if not "%factory_id%"=="" set cmd_args=!cmd_args! --factory-id "%factory_id%"
if not "%location%"=="" set cmd_args=!cmd_args! --location "%location%"
if not "%industry_category%"=="" set cmd_args=!cmd_args! --industry-category "%industry_category%"
if not "%product%"=="" set cmd_args=!cmd_args! --product "%product%"
if not "%used_for%"=="" set cmd_args=!cmd_args! --used-for "%used_for%"
if not "%vendor%"=="" set cmd_args=!cmd_args! --vendor "%vendor%"

echo.
echo %YELLOW%Executing: python vendor_api_tool.py !cmd_args!%RESET%
python vendor_api_tool.py !cmd_args!
if errorlevel 1 (
    echo %RED%Error occurred while running the tool%RESET%
    pause
)
goto MENU

:CONFIG
echo.
echo %YELLOW%Running with configuration file...%RESET%
set /p config_file="Configuration file path (default: config_template.json): "
if "%config_file%"=="" set config_file=config_template.json

if not exist "%config_file%" (
    echo %RED%Error: Configuration file "%config_file%" not found%RESET%
    pause
    goto MENU
)

python vendor_api_tool.py --config "%config_file%"
if errorlevel 1 (
    echo %RED%Error occurred while running the tool%RESET%
    pause
)
goto MENU

:HELP
echo.
echo %BLUE%============================================================================%RESET%
echo %BLUE%    HELP AND EXAMPLES%RESET%
echo %BLUE%============================================================================%RESET%
echo.
echo %YELLOW%Command Line Usage:%RESET%
echo   python vendor_api_tool.py [options]
echo.
echo %YELLOW%Options:%RESET%
echo   --factory-id ID          Factory ID to search for
echo   --location LOCATION      Location to search for
echo   --industry-category CAT  Industry category (L/M/S)
echo   --product PRODUCT        Product to search for
echo   --used-for TYPE          Used for parameter (R/C/I)
echo   --vendor VENDOR          Vendor name to search for
echo   --output FILENAME        Output Excel file name
echo   --config CONFIG_FILE     Load configuration from JSON file
echo.
echo %YELLOW%Examples:%RESET%
echo   1. Basic search:
echo      python vendor_api_tool.py --location "Rajkot" --product "Cylinder"
echo.
echo   2. Using configuration file:
echo      python vendor_api_tool.py --config config_template.json
echo.
echo   3. Interactive mode:
echo      python interactive_ui.py
echo.
echo %YELLOW%Configuration File Format (JSON):%RESET%
echo   {
echo     "search_params": {
echo       "factory_id": "20",
echo       "location": "Rajkot",
echo       "industry_category": "L",
echo       "product": "Cylinder",
echo       "used_for": "R",
echo       "vendor": "cee"
echo     }
echo   }
echo.
pause
goto MENU

:EXIT
echo.
echo %GREEN%Thank you for using the Vendor API Tool!%RESET%
echo.
pause
exit /b 0
