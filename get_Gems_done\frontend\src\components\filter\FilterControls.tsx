// src/components/filter/FilterControls.tsx
"use client";

import React, { useEffect, useRef, useState } from "react";
import { fetchApi, ApiError, stopTask } from "@/lib/api";
import { Bid } from "@/components/bids/BidList";
import LoadingBar from "@/components/ui/LoadingBar";
import ProgressBar from "@/components/ui/ProgressBar";
import StatusBadge from "@/components/ui/StatusBadge";
import { StopCircle } from "lucide-react"; // Import Bid interface

// Must match the enum in your backend (app/api/v1/endpoints/bids.py)
enum FilterType {
	CUSTOM = "custom",
}

interface ApiInitiateResponse {
	// For scrape AND download initiation
	message: string;
	task_id?: string;
	details?: Record<string, unknown>;
	server_save_subdirectory?: string; // From our download endpoint
}

interface BackendTaskInfo {
	task_id: string;
	status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED"; // Match backend TaskStatus enum
	message: string;
	details?: Record<string, unknown>;
	progress_stage?: string | null;
	progress_current?: number;
	progress_total?: number;
	result_count?: number | null;
	error_message?: string | null;
	result_detail?: Record<string, unknown> | null;
	zip_filename?: string | null;
}

// Must match the request body schema for /bids/advanced-filter
interface AdvancedFilterApiRequest {
	filter_type: FilterType;
	custom_keywords?: string[];
	// Add other potential pre-filters if your backend endpoint supports them
	// ministry_filter?: string;
	// organization_filter?: string;
}

interface FilterControlsProps {
	filterApiUrl: string;
	onFilterResults: (bids: Bid[] | null) => void;
	setIsFiltering: (isFiltering: boolean) => void;
	onClearFilter: () => void;
	currentFilteredBids: Bid[] | null; // Receive the currently filtered bids
	bulkDownloadApiUrl: string; // API endpoint to trigger bulk download
	statusApiUrl: string; // For polling
	customKeywordsInput: string; // Shared keywords state
	setCustomKeywordsInput: (keywords: string) => void; // Shared keywords setter
	// Lifted zip download state
	isZipDownloading: boolean;
	setIsZipDownloading: (isDownloading: boolean) => void;
	zipDownloadProgress: {
		current: number;
		total: number;
		count: number;
		stage: string;
		message: string;
	} | null;
	setZipDownloadProgress: (
		progress: {
			current: number;
			total: number;
			count: number;
			stage: string;
			message: string;
		} | null
	) => void;
	activeZipDownloadTaskId: string | null;
	setActiveZipDownloadTaskId: (taskId: string | null) => void;
	// Mutual exclusion
	isAnyProcessRunning: boolean;
}

const FilterControls: React.FC<FilterControlsProps> = ({
	filterApiUrl,
	onFilterResults,
	setIsFiltering,
	onClearFilter,
	currentFilteredBids, // Use this prop
	bulkDownloadApiUrl, // Make sure this is passed from HomePage
	statusApiUrl, // For polling
	customKeywordsInput, // Shared keywords state
	setCustomKeywordsInput, // Shared keywords setter
	// Lifted zip download state
	isZipDownloading,
	setIsZipDownloading,
	zipDownloadProgress,
	setZipDownloadProgress,
	activeZipDownloadTaskId,
	setActiveZipDownloadTaskId,
	// Mutual exclusion
	isAnyProcessRunning,
}) => {
	const [localIsLoading, setLocalIsLoading] = useState(false); // For filter buttons
	const [filterError, setFilterError] = useState<string | null>(null);
	const [downloadInitiationMessage, setDownloadInitiationMessage] = useState<
		string | null
	>(null);
	const [generatedZipFilename, setGeneratedZipFilename] = useState<
		string | null
	>(null);
	const downloadIntervalIdRef = useRef<NodeJS.Timeout | null>(null);

	const parseKeywordsFromInput = (input: string): string[] => {
		return input
			.split(/[\n,]+/)
			.map((kw) => kw.trim())
			.filter((kw) => kw.length > 0);
	};

	const handleFilter = async () => {
		setIsFiltering(true);
		setLocalIsLoading(true);
		setFilterError(null);

		const finalKeywords = parseKeywordsFromInput(customKeywordsInput);

		if (finalKeywords.length === 0) {
			setFilterError("Please enter keywords for filtering.");
			setIsFiltering(false);
			setLocalIsLoading(false);
			return;
		}

		const requestBody: AdvancedFilterApiRequest = {
			filter_type: FilterType.CUSTOM,
			custom_keywords: finalKeywords,
		};

		try {
			const filterSkip = 0;
			const filterLimit = 200;
			const url = `${filterApiUrl}?skip=${filterSkip}&limit=${filterLimit}`;
			console.log(`FilterControls: POSTing to ${url} with body:`, requestBody);

			const filteredBids = await fetchApi<Bid[]>(url, {
				method: "POST",
				body: JSON.stringify(requestBody),
			});
			onFilterResults(filteredBids || []);
		} catch (err) {
			console.error("Filter API Error:", err);
			let errMsg = "An unknown error occurred during filtering.";
			if (err instanceof ApiError)
				errMsg = `Filter Error: ${err.message} ${
					err.details ? `(${JSON.stringify(err.details)})` : ""
				}`;
			else if (err instanceof Error) errMsg = `Filter Error: ${err.message}`;
			setFilterError(errMsg);
			onFilterResults([]);
		} finally {
			setIsFiltering(false);
			setLocalIsLoading(false);
		}
	};

	const handleDownloadFiltered = async () => {
		if (!currentFilteredBids || currentFilteredBids.length === 0) {
			setDownloadInitiationMessage("No bids currently filtered to download.");
			return;
		}
		setIsZipDownloading(true);
		setDownloadInitiationMessage(
			"Initiating download of filtered bids on server..."
		);
		setGeneratedZipFilename(null); // Clear previous zip filename
		setZipDownloadProgress(null); // Clear previous progress
		if (downloadIntervalIdRef.current)
			clearInterval(downloadIntervalIdRef.current);

		const bidNumbersToDownload = currentFilteredBids.map(
			(bid) => bid.bid_number
		);
		const prefix = `filtered_bids_${new Date().toISOString().slice(0, 10)}`;

		try {
			const response = await fetchApi<ApiInitiateResponse>(bulkDownloadApiUrl, {
				method: "POST",
				body: JSON.stringify({
					bid_numbers: bidNumbersToDownload,
					target_subdirectory_name: prefix, // Backend uses this as a prefix
				}),
			});

			if (response.task_id) {
				setActiveZipDownloadTaskId(response.task_id);
				setDownloadInitiationMessage(
					`${response.message} (Task ID: ${response.task_id}). Polling for archive readiness...`
				);
				// Start polling for this download/archive task
				downloadIntervalIdRef.current = setInterval(
					() => pollDownloadArchiveStatus(response.task_id!),
					5000 // Poll every 5 seconds
				);
				pollDownloadArchiveStatus(response.task_id); // Initial poll
			} else {
				setDownloadInitiationMessage(
					"Error: Backend did not return a task ID for download process."
				);
				setIsZipDownloading(false); // Only set to false if no task_id
			}
		} catch (err) {
			console.error("Bulk Download API Error:", err);
			let errMsg = "An unknown error occurred during download initiation.";
			if (err instanceof ApiError)
				errMsg = `Download Error: ${err.message} ${
					err.details ? `(${JSON.stringify(err.details)})` : ""
				}`;
			else if (err instanceof Error) errMsg = `Download Error: ${err.message}`;
			setDownloadInitiationMessage(errMsg); // Use downloadInitiationMessage for download feedback
			setIsZipDownloading(false); // Only set to false on error
		}
	};

	const handleStopDownload = async () => {
		if (!activeZipDownloadTaskId) return;

		try {
			// Call the API to stop the task
			await stopTask(activeZipDownloadTaskId);

			// Update the UI to show the task as stopped
			setDownloadInitiationMessage("Download process stopped by user request");
			setIsZipDownloading(false);
			setZipDownloadProgress(null); // Clear progress

			// Clear the polling interval
			if (downloadIntervalIdRef.current) {
				clearInterval(downloadIntervalIdRef.current);
				downloadIntervalIdRef.current = null;
			}

			setActiveZipDownloadTaskId(null);
		} catch (error) {
			console.error("Failed to stop download task:", error);
			setDownloadInitiationMessage(
				"Failed to stop the download process. Please try again."
			);
		}
	};

	const pollDownloadArchiveStatus = async (backendTaskId: string) => {
		console.log(
			`Polling download/archive status for Task ID: ${backendTaskId}`
		);
		try {
			const taskStatus = await fetchApi<BackendTaskInfo>(
				`${statusApiUrl}/${backendTaskId}`
			);

			// Update progress information
			if (
				taskStatus.progress_current !== undefined &&
				taskStatus.progress_total !== undefined
			) {
				setZipDownloadProgress({
					current: taskStatus.progress_current,
					total: taskStatus.progress_total,
					count: taskStatus.result_count || 0,
					stage: taskStatus.progress_stage || "Processing",
					message: taskStatus.message,
				});
			}

			setDownloadInitiationMessage(
				`${taskStatus.message} (Status: ${taskStatus.status})`
			);

			if (taskStatus.status === "COMPLETED") {
				if (downloadIntervalIdRef.current)
					clearInterval(downloadIntervalIdRef.current);
				setIsZipDownloading(false);
				setZipDownloadProgress(null);
				if (taskStatus.zip_filename) {
					setGeneratedZipFilename(taskStatus.zip_filename);
					setDownloadInitiationMessage(
						`Archive '${taskStatus.zip_filename}' is ready! Click the link below.`
					);
				} else {
					setDownloadInitiationMessage(
						"Archiving completed, but no ZIP filename provided by backend."
					);
				}
				setActiveZipDownloadTaskId(null);
			} else if (taskStatus.status === "FAILED") {
				if (downloadIntervalIdRef.current)
					clearInterval(downloadIntervalIdRef.current);
				setIsZipDownloading(false);
				setZipDownloadProgress(null);
				setDownloadInitiationMessage(
					`Archiving Failed: ${taskStatus.error_message || taskStatus.message}`
				);
				setActiveZipDownloadTaskId(null);
			}
			// If still PENDING or RUNNING, polling continues
		} catch (err) {
			console.error("Error polling download task status:", err);
			setDownloadInitiationMessage(
				"Error fetching download status. Polling will continue."
			);
			// Optionally stop polling after several errors
		}
	};

	useEffect(() => {
		return () => {
			if (downloadIntervalIdRef.current) {
				clearInterval(downloadIntervalIdRef.current);
			}
		};
	}, []);

	return (
		<div className="space-y-6">
			{/* Filter Buttons - Using direct styling instead of utility classes */}
			<div>
				{/* Custom Keywords Section */}
				<div className="bg-gray-50 p-5 rounded-md">
					<label
						htmlFor="customKeywords"
						className="block text-base font-medium text-gray-700 mb-4"
						style={{ marginBottom: "8px" }}
					>
						Keywords you want to filter by (comma or newline separated):
					</label>
					<textarea
						id="customKeywords"
						rows={8}
						value={customKeywordsInput}
						onChange={(e) => setCustomKeywordsInput(e.target.value)}
						className="input w-full box-border mb-4"
						placeholder="Enter keywords here . . . "
					/>
					<div className="flex justify-end">
						<button
							onClick={handleFilter}
							disabled={
								isAnyProcessRunning ||
								localIsLoading ||
								!customKeywordsInput.trim()
							}
							style={{
								padding: "10px 16px",
								marginTop: "16px",
								marginBottom: "16px",
								backgroundColor:
									isAnyProcessRunning && !localIsLoading
										? "#9ca3af"
										: "#1f2937",
								color: "white",
								borderRadius: "6px",
								fontWeight: 500,
								cursor:
									isAnyProcessRunning && !localIsLoading
										? "not-allowed"
										: "pointer",
								border: "none",
							}}
							title={
								isAnyProcessRunning && !localIsLoading
									? "Another process is currently running. Please wait for it to complete."
									: ""
							}
						>
							Filter
						</button>
					</div>
				</div>

				{/* Regular Keywords Button */}
				<div className="mb-6">
					{/* <button
						onClick={() => handleFilter(FilterType.REGULAR)}
						disabled={localIsLoading}
						style={{
							width: "100%",
							padding: "14px",
							marginBottom: "10px",
							backgroundColor: "#1f2937",
							color: "white",
							borderRadius: "6px",
							fontWeight: 500,
							cursor: "pointer",
							border: "none",
						}}
					>
						Filter for daily flow
					</button> */}
				</div>

				{/* Drawing Numbers Button */}
				<div className="mb-6">
					{/* <button
						onClick={handleDrawingNumberFilterClick}
						disabled={localIsLoading}
						style={{
							width: "100%",
							padding: "14px",
							marginBottom: "10px",
							backgroundColor: "#1f2937",
							color: "white",
							borderRadius: "6px",
							fontWeight: 500,
							cursor: "pointer",
							border: "none",
						}}
					>
						load some drawing numbers
					</button> */}
				</div>

				{/* Show All Button */}
				<div className="mb-6">
					<button
						onClick={onClearFilter}
						disabled={isAnyProcessRunning || localIsLoading}
						style={{
							width: "100%",
							padding: "14px",
							marginBottom: "14px",
							backgroundColor:
								isAnyProcessRunning && !localIsLoading ? "#e5e7eb" : "#f3f4f6",
							color:
								isAnyProcessRunning && !localIsLoading ? "#9ca3af" : "#1f2937",
							borderRadius: "6px",
							fontWeight: 500,
							cursor:
								isAnyProcessRunning && !localIsLoading
									? "not-allowed"
									: "pointer",
							border: "1px solid #e5e7eb",
						}}
						title={
							isAnyProcessRunning && !localIsLoading
								? "Another process is currently running. Please wait for it to complete."
								: ""
						}
					>
						Show All Stored Bids
					</button>
				</div>
			</div>

			{/* Download Filtered Bids Section */}
			<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
				<h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
					Download Actions
				</h4>
				{isZipDownloading && activeZipDownloadTaskId ? (
					<div className="flex gap-2">
						<button
							onClick={handleStopDownload}
							className="btn btn-danger flex items-center justify-center gap-1 bg-red-600 hover:bg-red-700 text-white"
						>
							<StopCircle size={16} />
							Stop Download
						</button>
						<div className="flex-1 text-sm flex items-center justify-center bg-gray-100 rounded-md">
							Preparing archive...
						</div>
					</div>
				) : (
					<button
						onClick={handleDownloadFiltered}
						disabled={isAnyProcessRunning || localIsLoading}
						className={`btn btn-primary w-full ${
							isAnyProcessRunning && !isZipDownloading
								? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
								: "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"
						}`}
						title={
							isAnyProcessRunning && !isZipDownloading
								? "Another process is currently running. Please wait for it to complete."
								: currentFilteredBids && currentFilteredBids.length > 0
								? `Download documents for ${currentFilteredBids.length} filtered bid(s)`
								: "No filtered bids to download. Please apply a filter first."
						}
					>
						{isZipDownloading
							? "Preparing Archive..."
							: currentFilteredBids && currentFilteredBids.length > 0
							? `Download Documents for ${currentFilteredBids.length} Filtered Bid(s)`
							: "Download Filtered Bids (No Filter Applied)"}
					</button>
				)}
			</div>

			{/* Download Status and Link */}
			{downloadInitiationMessage && (
				<div
					className={`mt-3 p-3 rounded-md border text-sm ${
						downloadInitiationMessage.toLowerCase().includes("error") ||
						downloadInitiationMessage.toLowerCase().includes("failed")
							? "bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700"
							: generatedZipFilename
							? "bg-green-50 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700"
							: "bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700"
					}`}
				>
					<div className="flex justify-between items-center mb-2">
						<p>{downloadInitiationMessage}</p>
						{isZipDownloading && activeZipDownloadTaskId && (
							<button
								onClick={handleStopDownload}
								className="ml-2 px-2 py-1 bg-red-600 hover:bg-red-700 text-white rounded-md flex items-center text-xs"
							>
								<StopCircle size={14} className="mr-1" />
								Stop
							</button>
						)}
					</div>
					{generatedZipFilename && (
						<a
							// Construct the download link for the ZIP file
							// Assumes your backend is on same origin or proxied via /api/v1
							href={`/api/v1/bids/download-archive/${generatedZipFilename}`}
							download // Suggests download to browser
							className="mt-2 inline-block font-semibold text-indigo-600 dark:text-indigo-400 hover:underline"
						>
							Click here to download {generatedZipFilename}
						</a>
					)}
				</div>
			)}

			{/* Status Messages */}
			{localIsLoading && (
				<div className="bg-blue-50 text-blue-700 p-3 rounded-md border border-blue-200">
					<div className="flex items-center gap-2 mb-2">
						<StatusBadge status="running" text="Filtering" />
						<span>Applying filter...</span>
					</div>
					<LoadingBar height={3} variant="primary" />
				</div>
			)}

			{isZipDownloading && (
				<div className="bg-green-50 text-green-700 p-3 rounded-md border border-green-200">
					<div className="flex items-center gap-2 mb-2">
						<StatusBadge status="running" text="Downloading" />
						<span>
							{zipDownloadProgress
								? `${zipDownloadProgress.stage}: ${zipDownloadProgress.current}/${zipDownloadProgress.total} (Downloaded: ${zipDownloadProgress.count})`
								: "Preparing archive for download..."}
						</span>
					</div>
					{zipDownloadProgress ? (
						<ProgressBar
							current={zipDownloadProgress.current}
							total={zipDownloadProgress.total}
							height={6}
							variant="processing"
							label={`${zipDownloadProgress.stage} Progress`}
							showDetailed={true}
						/>
					) : (
						<LoadingBar height={3} variant="success" />
					)}
				</div>
			)}

			{filterError && (
				<div className="bg-red-50 text-red-700 p-3 rounded-md border border-red-200">
					<div className="flex items-center gap-2">
						<StatusBadge status="failed" text="Error" />
						<span>{filterError}</span>
					</div>
				</div>
			)}
		</div>
	);
};

export default FilterControls;
