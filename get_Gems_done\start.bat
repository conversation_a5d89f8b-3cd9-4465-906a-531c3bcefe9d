@echo off
REM Script to start both backend and frontend development servers

ECHO Starting Backend FastAPI Server...
REM Navigate to the backend directory
cd backend

REM Activate virtual environment if you use one (adjust path as needed)
REM This assumes your venv is named '.venv' inside the backend folder.
IF EXIST .venv\Scripts\activate.bat (
    ECHO Activating backend virtual environment...
    CALL .venv\Scripts\activate.bat
) ELSE (
    ECHO Backend virtual environment not found at .venv\Scripts\activate.bat.
    ECHO Make sure Python and Uvicorn are in your system PATH if not using a venv.
)

REM Start Uvicorn server in a new command prompt window
REM The "start" command opens a new window so this script can continue
REM Adjust app.main:app and port as needed
start "Backend FastAPI" cmd /k "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-level debug"

REM Give the backend a moment to start (optional)
timeout /t 3 > nul

ECHO Starting Frontend Next.js Development Server...
REM Navigate back to project root and then to frontend directory
cd ..
cd frontend

REM Activate frontend virtual environment if it uses one (less common for Node.js)
REM IF EXIST venv\Scripts\activate.bat (
REM     ECHO Activating frontend virtual environment...
REM     CALL venv\Scripts\activate.bat
REM )

REM Start Next.js dev server in another new command prompt window
REM Adjust dev script name if necessary (e.g., yarn dev)
start "Frontend Next.js" cmd /k "npm run dev"

ECHO Both services are starting in separate windows.
ECHO You can close this script window.

pause