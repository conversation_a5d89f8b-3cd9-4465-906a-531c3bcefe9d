// src/app/page.tsx
"use client";

import React, { useEffect, useRef, useState } from "react";
import ScrapeControls from "@/components/scrape/ScrapeControls";
import BidList, { Bid } from "@/components/bids/BidList";
import FilterControls from "@/components/filter/FilterControls";
import ScrapeSourceFilters from "@/components/filter/ScrapeSourceFilters";
import ExcelExportControls from "@/components/export/ExcelExportControls";
import LoadingBar from "@/components/ui/LoadingBar";
import StatusBadge from "@/components/ui/StatusBadge";
import ProcessLogBar from "@/components/ui/ProcessLogBar";

import { titleFont } from "@/lib/fonts";
import { fetchApi, ApiError, stopTask } from "@/lib/api";
import { useLoading } from "@/contexts/LoadingContext";

interface ApiInitiateResponse {
	message: string;
	task_id?: string;
	details?: Record<string, unknown>;
}

interface DeleteApiResponse {
	// Expected from our delete endpoint
	message: string;
	deleted_count: number;
}

interface BackendTaskInfo {
	task_id: string;
	status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED";
	message: string;
	details?: Record<string, unknown>;
	progress_stage?: string | null;
	progress_current?: number;
	progress_total?: number;
	result_count?: number | null;
	error_message?: string | null;
	result_detail?: Record<string, unknown> | null; // Could hold { zip_filename, individual_pdf_dir } for download tasks
	// Or { accepted_dir_rel, rejected_dir_rel, processed_results } for PDF processing
}

export default function HomePage() {
	const { loadingState, setLoading, clearLoading, setStatus } = useLoading();
	const [displayedBids, setDisplayedBids] = useState<Bid[] | null>(null);
	const [isFilteringGlobal, setIsFilteringGlobal] = useState(false);
	const [activeFilter, setActiveFilter] = useState<string>("all");
	const [customKeywordsInput, setCustomKeywordsInput] = useState("");

	const [dailyWorkflowStatusMessage, setDailyWorkflowStatusMessage] = useState<
		string | null
	>(null);
	const [isDailyWorkflowRunning, setIsDailyWorkflowRunning] = useState(false);
	const [activeDailyWorkflowTaskId, setActiveDailyWorkflowTaskId] = useState<
		string | null
	>(null);
	const dailyWorkflowPollIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const [
		dailyWorkflowCompletedSuccessfully,
		setDailyWorkflowCompletedSuccessfully,
	] = useState(false);
	const [lastProcessedBidNumbers, setLastProcessedBidNumbers] = useState<
		string[]
	>([]);

	const [isDeletingExpired, setIsDeletingExpired] = useState(false);
	const [deleteExpiredMessage, setDeleteExpiredMessage] = useState<
		string | null
	>(null);

	// Zip download state (lifted from FilterControls)
	const [isZipDownloading, setIsZipDownloading] = useState(false);
	const [zipDownloadProgress, setZipDownloadProgress] = useState<{
		current: number;
		total: number;
		count: number;
		stage: string;
		message: string;
	} | null>(null);
	const [activeZipDownloadTaskId, setActiveZipDownloadTaskId] = useState<
		string | null
	>(null);

	// Global busy state to prevent multiple processes
	const isAnyProcessRunning =
		isDailyWorkflowRunning ||
		isZipDownloading ||
		isDeletingExpired ||
		isFilteringGlobal;

	// Stop function for zip download
	const handleStopZipDownload = async () => {
		if (!activeZipDownloadTaskId) return;

		try {
			// Call the stop API using the correct stopTask function
			await stopTask(activeZipDownloadTaskId);

			// Clear the state
			setIsZipDownloading(false);
			setZipDownloadProgress(null);
			setActiveZipDownloadTaskId(null);
		} catch (error) {
			console.error("Failed to stop zip download:", error);
		}
	};

	// Stop function for daily workflow (PDF processing)
	const handleStopDailyWorkflow = async () => {
		if (!activeDailyWorkflowTaskId) return;

		try {
			// Call the stop API
			await fetchApi(
				`/api/v1/scrape-status/${activeDailyWorkflowTaskId}/stop`,
				{
					method: "POST",
				}
			);

			// Clear the state
			setIsDailyWorkflowRunning(false);
			setActiveDailyWorkflowTaskId(null);
			if (dailyWorkflowPollIntervalRef.current) {
				clearInterval(dailyWorkflowPollIntervalRef.current);
				dailyWorkflowPollIntervalRef.current = null;
			}
			clearLoading();
		} catch (error) {
			console.error("Failed to stop daily workflow:", error);
		}
	};

	const handleDeleteExpiredBids = async () => {
		if (
			!confirm(
				"Are you sure you want to delete all bids whose end date has passed? This action cannot be undone."
			)
		) {
			return;
		}
		setIsDeletingExpired(true);
		setDeleteExpiredMessage("Deleting expired bids...");
		setLoading({
			message: "Deleting expired bids...",
			status: "running",
			operation: "delete_expired",
		});
		try {
			const response = await fetchApi<DeleteApiResponse>(
				"/api/v1/bids/delete-expired",
				{
					method: "POST",
				}
			);
			setDeleteExpiredMessage(
				`${response.message} Deleted: ${response.deleted_count} bid(s).`
			);

			// Always reset to show all bids after deletion, regardless of current filter state
			setActiveFilter("all");
			clearAllFiltersAndWorkflowDisplay();

			// Force a refresh of the BidList component by temporarily setting a key
			// This will cause the BidList to remount and fetch fresh data
			setDisplayedBids(null);
		} catch (err) {
			const msg =
				err instanceof ApiError
					? `${err.message} (${err.details || ""})`
					: err instanceof Error
					? err.message
					: "Unknown error";
			setDeleteExpiredMessage(`Error deleting expired bids: ${msg}`);
			setStatus("failed", `Error deleting expired bids: ${msg}`);
		} finally {
			setIsDeletingExpired(false);
			clearLoading();
		}
	};

	const handleTriggerDailyWorkflow = async () => {
		if (isDailyWorkflowRunning) return;

		// Check if there are filtered bids to process
		if (!displayedBids || displayedBids.length === 0) {
			alert(
				"Please filter some bids first before running the daily workflow. The workflow will process only the currently filtered bids."
			);
			return;
		}

		setIsDailyWorkflowRunning(true);
		setDailyWorkflowStatusMessage("Initiating Daily Workflow...");
		setActiveDailyWorkflowTaskId(null);
		setDailyWorkflowCompletedSuccessfully(false); // Reset completion flag
		setLoading({
			message: "Initiating PDF Analysis workflow...",
			status: "running",
			operation: "daily_workflow",
		});
		if (dailyWorkflowPollIntervalRef.current)
			clearInterval(dailyWorkflowPollIntervalRef.current);

		try {
			// Extract bid numbers from filtered bids
			const bidNumbers = displayedBids.map((bid) => bid.bid_number);

			// Store the bid numbers that are being processed
			setLastProcessedBidNumbers(bidNumbers);

			const response = await fetchApi<ApiInitiateResponse>(
				"/api/v1/workflows/trigger-daily",
				{
					method: "POST",
					body: JSON.stringify({
						bid_numbers: bidNumbers,
					}),
				}
			);

			if (response.task_id) {
				setActiveDailyWorkflowTaskId(response.task_id);
				setDailyWorkflowStatusMessage(
					`${response.message} Task ID: ${response.task_id}. Polling for updates...`
				);

				// Start polling
				dailyWorkflowPollIntervalRef.current = setInterval(
					() => pollDailyWorkflowStatus(response.task_id!),
					5000 // Poll every 5 seconds
				);
				pollDailyWorkflowStatus(response.task_id); // Initial immediate poll
			} else {
				setDailyWorkflowStatusMessage(
					"Error: Backend did not return a task ID for Daily Workflow."
				);
				setIsDailyWorkflowRunning(false);
			}
		} catch (err) {
			const msg =
				err instanceof ApiError
					? `${err.message} ${err.details ? JSON.stringify(err.details) : ""}`
					: err instanceof Error
					? err.message
					: "Unknown error";
			setDailyWorkflowStatusMessage(`Error triggering Daily Workflow: ${msg}`);
			setIsDailyWorkflowRunning(false);
			setStatus("failed", `Error triggering Daily Workflow: ${msg}`);
		}
	};

	const handleShowAcceptedFromWorkflow = async () => {
		setIsFilteringGlobal(true);
		setDisplayedBids(null);

		try {
			if (lastProcessedBidNumbers.length === 0) {
				console.warn("No processed bid numbers found");
				setDisplayedBids([]);
				return;
			}

			// Fetch all accepted bids
			const url = `/api/v1/bids?pdf_analysis_status=PDF_ACCEPTED`;
			const allAcceptedBids = await fetchApi<Bid[]>(url);

			// Filter to only show accepted bids that were in the processed set
			const acceptedFromProcessedSet = (allAcceptedBids || []).filter((bid) =>
				lastProcessedBidNumbers.includes(bid.bid_number)
			);

			setDisplayedBids(acceptedFromProcessedSet);
			setDailyWorkflowCompletedSuccessfully(false); // Hide the "Show Accepted" button after showing them
		} catch (err) {
			console.error("Error fetching accepted workflow bids:", err);
			setDisplayedBids([]);
		} finally {
			setIsFilteringGlobal(false);
		}
	};

	const clearAllFiltersAndWorkflowDisplay = () => {
		setDisplayedBids(null);
		setIsFilteringGlobal(false);
		setDailyWorkflowCompletedSuccessfully(false); // Ensure "Show Accepted" button hides
		setLastProcessedBidNumbers([]); // Clear processed bid numbers
		// setWorkflowStatusMessage(null); // Optionally clear workflow message too
	};

	// Callback to refresh bid section when scraping completes
	const handleScrapeComplete = () => {
		console.log("Scraping completed, refreshing bid section...");
		// If we're currently showing all bids (no filter), refresh the BidList
		if (displayedBids === null) {
			// Force BidList to re-fetch by setting a temporary empty array and then back to null
			setDisplayedBids([]);
			setTimeout(() => setDisplayedBids(null), 100);
		}
		// If we're showing filtered bids, we could optionally re-apply the filter
		// For now, we'll just show a notification that new bids are available
		else {
			alert(
				"Scraping completed! New bids may be available. Consider clearing filters to see all bids."
			);
		}
	};

	// New handlers for scrape source filtering
	const handleShowLatestScraped = async () => {
		setIsFilteringGlobal(true);
		setActiveFilter("latest");
		try {
			const latestBids = await fetchApi<Bid[]>("/api/v1/bids/latest-session");
			setDisplayedBids(latestBids || []);
		} catch (error) {
			console.error("Failed to fetch latest scraped bids:", error);
			setDisplayedBids([]);
		} finally {
			setIsFilteringGlobal(false);
		}
	};

	const handleShowAllFromLatestSources = async () => {
		setIsFilteringGlobal(true);
		setActiveFilter("latest-sources");
		try {
			const allFromLatestSources = await fetchApi<Bid[]>(
				"/api/v1/bids/all-from-latest-sources"
			);
			setDisplayedBids(allFromLatestSources || []);
		} catch (error) {
			console.error("Failed to fetch all bids from latest sources:", error);
			setDisplayedBids([]);
		} finally {
			setIsFilteringGlobal(false);
		}
	};

	const handleShowAllBids = () => {
		setActiveFilter("all");
		clearAllFiltersAndWorkflowDisplay();
	};

	const handleShowBySource = async (sourceType: string, details: any) => {
		setIsFilteringGlobal(true);
		setActiveFilter("source");
		try {
			// For now, we'll filter by source type. You could extend this to filter by specific details
			const sourceBids = await fetchApi<Bid[]>(
				`/api/v1/bids/by-source/${sourceType}`
			);
			setDisplayedBids(sourceBids || []);
		} catch (error) {
			console.error("Failed to fetch bids by source:", error);
			setDisplayedBids([]);
		} finally {
			setIsFilteringGlobal(false);
		}
	};

	const handleShowByMultipleSources = async (selectedSources: any[]) => {
		setIsFilteringGlobal(true);
		setActiveFilter("multiple-sources");
		try {
			// Fetch bids for each selected source and combine them
			const allBids: Bid[] = [];
			for (const source of selectedSources) {
				const sourceBids = await fetchApi<Bid[]>(
					`/api/v1/bids/by-source/${source.source_type}`
				);
				allBids.push(...(sourceBids || []));
			}

			// Remove duplicates based on bid ID
			const uniqueBids = allBids.filter(
				(bid, index, self) => index === self.findIndex((b) => b.id === bid.id)
			);

			setDisplayedBids(uniqueBids);
		} catch (error) {
			console.error("Failed to fetch bids by multiple sources:", error);
			setDisplayedBids([]);
		} finally {
			setIsFilteringGlobal(false);
		}
	};

	// Cleanup polling interval on component unmount
	useEffect(() => {
		return () => {
			if (dailyWorkflowPollIntervalRef.current) {
				clearInterval(dailyWorkflowPollIntervalRef.current);
			}
		};
	}, []);

	const pollDailyWorkflowStatus = async (taskId: string) => {
		if (!taskId) return;
		console.log(`Polling Daily Workflow status for Task ID: ${taskId}`);
		try {
			// Make sure statusApiUrl is correct if it's a prop, or hardcode path
			const taskStatus = await fetchApi<BackendTaskInfo>(
				`/api/v1/scrape-status/${taskId}`
			);

			setDailyWorkflowStatusMessage(
				`Backend (Daily Workflow): ${taskStatus.message} (Status: ${taskStatus.status})`
			);

			// Update global loading state with progress if available
			if (taskStatus.progress_current && taskStatus.progress_total) {
				const progress =
					(taskStatus.progress_current / taskStatus.progress_total) * 100;
				setLoading({
					message: `PDF Analysis: ${taskStatus.message}`,
					status: "running",
					progress: progress,
					details: `${taskStatus.progress_current}/${taskStatus.progress_total}`,
					operation: "daily_workflow",
				});
			}

			if (taskStatus.status === "COMPLETED" || taskStatus.status === "FAILED") {
				if (dailyWorkflowPollIntervalRef.current) {
					clearInterval(dailyWorkflowPollIntervalRef.current);
					dailyWorkflowPollIntervalRef.current = null;
				}
				setIsDailyWorkflowRunning(false);
				setActiveDailyWorkflowTaskId(null);
				console.log(
					`Daily Workflow Task ${taskId} finished with status: ${taskStatus.status}.`
				);

				if (taskStatus.status === "FAILED") {
					setDailyWorkflowStatusMessage(
						`Daily Workflow Failed: ${
							taskStatus.error_message || taskStatus.message
						}`
					);
					setDailyWorkflowCompletedSuccessfully(false);
					setStatus(
						"failed",
						`Daily Workflow Failed: ${
							taskStatus.error_message || taskStatus.message
						}`
					);
				} else {
					// COMPLETED
					setDailyWorkflowStatusMessage(
						`Daily Workflow Completed: ${
							taskStatus.message
						}. PDF statuses updated for ${taskStatus.result_count || 0} bids.`
					);
					setDailyWorkflowCompletedSuccessfully(true); // Set flag to show "Show Accepted Bids" button
					setStatus(
						"completed",
						`PDF Analysis completed. Processed ${
							taskStatus.result_count || 0
						} bids.`
					);
				}
				clearLoading();
			}
		} catch (err) {
			console.error("Error polling daily workflow status:", err);
			setDailyWorkflowStatusMessage(
				"Error fetching daily workflow status. Polling might have stopped."
			);
			// Optionally stop polling after too many errors by clearing interval here
			// if (dailyWorkflowPollIntervalRef.current) {
			//     clearInterval(dailyWorkflowPollIntervalRef.current);
			//     dailyWorkflowPollIntervalRef.current = null;
			// }
			// setIsDailyWorkflowRunning(false); // Consider if polling failure means workflow isn't "running"
		}
	};

	return (
		<div className="min-h-screen container-custom relative py-8 px-4">
			{/* Main Content Container */}
			{/* <div className="container-custom  container-bg "> */}
			{/* Top Controls Section - Side by Side Layout */}
			<div className="flex flex-row-2 md:flex-row gap-8">
				{/* Quick Scrape Targets */}
				<div className="card max-w-7xl mx-auto md:w-[42%]">
					<h2
						className={`text-xl ${titleFont.className} font-semibold text-gray-800 mb-6`}
					>
						Quick Scrape Targets
					</h2>
					<ScrapeControls
						ministryScrapeApiUrl="/api/v1/scrape-and-save/ministry-organization" // For *triggering* ministry scrape
						locationScrapeApiUrl="/api/v1/scrape-and-save/location" // For *triggering* location scrape
						statusApiUrl="/api/v1/scrape-status" // For polling *any* task status
						gemOrganizationsApiUrl="/api/v1/gem-data/gem-data/organizations"
						gemCitiesApiUrl="/api/v1/gem-data/gem-data/cities"
						onScrapeComplete={handleScrapeComplete} // Callback to refresh bid section when scraping completes
						isAnyProcessRunning={isAnyProcessRunning} // Disable when other processes are running
					/>
				</div>

				{/* Filter Stored Bids */}
				<div className="card w-[50%]  mx-auto md:w-[42%]">
					<h2
						className={`text-xl ${titleFont.className} font-semibold text-gray-800 mb-6`}
					>
						Filter Stored Bids
					</h2>
					<FilterControls
						filterApiUrl="/api/v1/bids/advanced-filter"
						onFilterResults={setDisplayedBids}
						setIsFiltering={setIsFilteringGlobal}
						onClearFilter={() => {
							setDisplayedBids(null);
							setIsFilteringGlobal(false);
							clearAllFiltersAndWorkflowDisplay();
						}}
						currentFilteredBids={displayedBids}
						bulkDownloadApiUrl="/api/v1/bids/initiate-download" // Pass the download URL
						statusApiUrl="/api/v1/scrape-status"
						customKeywordsInput={customKeywordsInput}
						setCustomKeywordsInput={setCustomKeywordsInput}
						// Zip download state handlers
						isZipDownloading={isZipDownloading}
						setIsZipDownloading={setIsZipDownloading}
						zipDownloadProgress={zipDownloadProgress}
						setZipDownloadProgress={setZipDownloadProgress}
						activeZipDownloadTaskId={activeZipDownloadTaskId}
						setActiveZipDownloadTaskId={setActiveZipDownloadTaskId}
						// Mutual exclusion
						isAnyProcessRunning={isAnyProcessRunning}
					/>

					<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
						<h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
							Automated Workflows
						</h4>
						<button
							onClick={handleTriggerDailyWorkflow}
							disabled={
								isAnyProcessRunning ||
								!displayedBids ||
								displayedBids.length === 0
							}
							className="btn btn-primary w-full bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600"
							title={
								isAnyProcessRunning && !isDailyWorkflowRunning
									? "Another process is currently running. Please wait for it to complete."
									: !displayedBids || displayedBids.length === 0
									? "Please filter some bids first"
									: ""
							}
						>
							{isDailyWorkflowRunning
								? "PDF Analysis Running..."
								: `Run PDF Analysis on ${
										displayedBids?.length || 0
								  } Filtered Bid(s)`}
						</button>
						<p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
							Analyzes PDFs for the currently filtered bids. Filter bids first
							using the search box above.
						</p>
						{dailyWorkflowStatusMessage && (
							<div
								className={`mt-3 text-sm p-3 rounded border ${
									dailyWorkflowStatusMessage.toLowerCase().includes("error") ||
									dailyWorkflowStatusMessage.toLowerCase().includes("failed")
										? "bg-red-50 dark:bg-red-800 text-red-700 dark:text-red-200 border-red-200 dark:border-red-700"
										: dailyWorkflowStatusMessage
												.toLowerCase()
												.includes("completed")
										? "bg-green-50 dark:bg-green-800 text-green-700 dark:text-green-200 border-green-200 dark:border-green-700"
										: "bg-blue-50 dark:bg-blue-800 text-blue-700 dark:text-blue-200 border-blue-200 dark:border-blue-700"
								}`}
							>
								<div className="flex items-center gap-2 mb-2">
									<StatusBadge
										status={
											dailyWorkflowStatusMessage
												.toLowerCase()
												.includes("error") ||
											dailyWorkflowStatusMessage
												.toLowerCase()
												.includes("failed")
												? "failed"
												: dailyWorkflowStatusMessage
														.toLowerCase()
														.includes("completed")
												? "completed"
												: "running"
										}
										text={
											dailyWorkflowStatusMessage
												.toLowerCase()
												.includes("error") ||
											dailyWorkflowStatusMessage
												.toLowerCase()
												.includes("failed")
												? "Error"
												: dailyWorkflowStatusMessage
														.toLowerCase()
														.includes("completed")
												? "Completed"
												: "Processing"
										}
									/>
									<span>PDF Analysis</span>
								</div>
								<p>{dailyWorkflowStatusMessage}</p>
								{isDailyWorkflowRunning && (
									<div className="mt-2">
										<LoadingBar height={3} variant="primary" pulse={true} />
									</div>
								)}
							</div>
						)}

						{dailyWorkflowCompletedSuccessfully && !isDailyWorkflowRunning && (
							<button
								onClick={handleShowAcceptedFromWorkflow}
								disabled={isFilteringGlobal}
								className="btn btn-secondary w-full mt-3 bg-sky-500 hover:bg-sky-600 text-white"
							>
								{isFilteringGlobal
									? "Loading Accepted..."
									: "Show Accepted Bids from Last Workflow"}
							</button>
						)}

						{/* Excel Export Section */}
						<ExcelExportControls
							currentFilteredBids={displayedBids}
							customKeywordsInput={customKeywordsInput}
							excelExportApiUrl="/api/v1/bids/export-excel"
						/>
					</div>
				</div>
			</div>

			{/* Delete Expired Bids Section */}
			<button
				onClick={handleDeleteExpiredBids}
				disabled={isDeletingExpired}
				className="btn btn-primary w-full bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white"
			>
				{isDeletingExpired
					? "Deleting..."
					: "Delete Expired Bids (End Date Passed)"}
			</button>
			{deleteExpiredMessage && (
				<div
					className={`mt-2 text-sm p-3 rounded border ${
						deleteExpiredMessage.toLowerCase().includes("error")
							? "bg-red-50 dark:bg-red-800 text-red-700 dark:text-red-200 border-red-200 dark:border-red-700"
							: "bg-green-50 dark:bg-green-800 text-green-700 dark:text-green-200 border-green-200 dark:border-green-700"
					}`}
				>
					<div className="flex items-center gap-2 mb-2">
						<StatusBadge
							status={
								deleteExpiredMessage.toLowerCase().includes("error")
									? "failed"
									: "completed"
							}
							text={
								deleteExpiredMessage.toLowerCase().includes("error")
									? "Error"
									: "Completed"
							}
						/>
						<span>Delete Operation</span>
					</div>
					<p>{deleteExpiredMessage}</p>
				</div>
			)}

			{isDeletingExpired && (
				<div className="mt-2 bg-yellow-50 text-yellow-700 p-3 rounded border border-yellow-200">
					<div className="flex items-center gap-2 mb-2">
						<StatusBadge status="running" text="Deleting" />
						<span>Removing expired bids...</span>
					</div>
					<LoadingBar height={3} variant="warning" />
				</div>
			)}

			{/* Scrape Source Filters */}
			<ScrapeSourceFilters
				onShowLatestScraped={handleShowLatestScraped}
				onShowAllFromLatestSources={handleShowAllFromLatestSources}
				onShowAllBids={handleShowAllBids}
				onShowBySource={handleShowBySource}
				onShowByMultipleSources={handleShowByMultipleSources}
				isLoading={isFilteringGlobal}
				activeFilter={activeFilter}
			/>

			{/* Bids Display Section */}
			<div className="card max-w-7xl mx-auto">
				<section>
					<div className="flex flex-col sm:flex-row justify-between sm:items-center mb-8 gap-4">
						<h2
							className={`text-2xl ${titleFont.className} font-semibold text-gray-800`}
						>
							{displayedBids ? "Filtered Bids" : "Stored Bids"}
						</h2>
						{isFilteringGlobal && (
							<p className="text-sm text-gray-600">Applying filter...</p>
						)}
					</div>
					<BidList
						bidsApiUrl="/api/v1/bids"
						downloadApiUrl="/api/v1/bids/download/pdf"
						bulkDownloadApiUrl="/api/v1/bids/initiate-download" // For FilterControls
						pdfCheckApiUrl="/api/v1/bids/check-downloaded-pdfs" // For PdfProcessingControls
						initialBids={displayedBids}
						key={
							displayedBids
								? JSON.stringify(displayedBids[0]?.id || "filtered")
								: "all"
						} // More robust key
					/>
				</section>
			</div>
			{/* </div> */}

			{/* Global Process Log Bar */}
			<ProcessLogBar
				isVisible={
					loadingState.isLoading ||
					isDailyWorkflowRunning ||
					isDeletingExpired ||
					isFilteringGlobal ||
					isZipDownloading
				}
				message={
					zipDownloadProgress?.message ||
					loadingState.message ||
					(isDailyWorkflowRunning ? "Running PDF Analysis..." : "") ||
					(isDeletingExpired ? "Deleting expired bids..." : "") ||
					(isFilteringGlobal ? "Applying filter..." : "") ||
					(isZipDownloading ? "Downloading documents..." : "")
				}
				status={
					loadingState.status === "failed"
						? "failed"
						: loadingState.status === "completed"
						? "completed"
						: isDailyWorkflowRunning ||
						  isDeletingExpired ||
						  isFilteringGlobal ||
						  isZipDownloading
						? "running"
						: "idle"
				}
				progress={
					zipDownloadProgress
						? (zipDownloadProgress.current / zipDownloadProgress.total) * 100
						: loadingState.progress
				}
				details={
					zipDownloadProgress
						? `${zipDownloadProgress.current}/${zipDownloadProgress.total} (Downloaded: ${zipDownloadProgress.count})`
						: loadingState.details
				}
				showProgress={true}
				// Stop button functionality
				showStopButton={isDailyWorkflowRunning || isZipDownloading}
				onStop={
					isDailyWorkflowRunning
						? handleStopDailyWorkflow
						: isZipDownloading
						? handleStopZipDownload
						: undefined
				}
				stopButtonText={
					isDailyWorkflowRunning
						? "Stop PDF Analysis"
						: isZipDownloading
						? "Stop Download"
						: undefined
				}
			/>
		</div>
	);
}
