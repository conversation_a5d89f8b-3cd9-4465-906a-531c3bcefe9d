# app/api/v1/endpoints/batch_pdf_analysis.py
"""
New API endpoints for optimized batch PDF processing with Gemini AI.
These endpoints handle large-scale PDF analysis with intelligent caching and progress tracking.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import uuid
import os
import asyncio
from pathlib import Path

from app.db.database import get_db
from app.services.pdf_processor_service import (
    analyze_batch_optimized,
    BatchProcessingConfig,
    BidCriteria,
    DEFAULT_CRITERIA,
    BatchProcessingResult
)
from app.tasks import (
    TaskInfo, TaskStatus, TASK_STATUS_STORE, 
    update_task_status, register_background_task, 
    unregister_background_task
)
from app.core.config import settings

router = APIRouter()

# Request/Response Models
class BatchAnalysisRequest(BaseModel):
    """Request model for batch PDF analysis"""
    bid_numbers: List[str] = Field(..., description="List of bid numbers to analyze")
    criteria: Optional[Dict[str, Any]] = Field(None, description="Custom analysis criteria")
    config: Optional[Dict[str, Any]] = Field(None, description="Batch processing configuration")
    
class BatchAnalysisConfig(BaseModel):
    """Configuration for batch processing"""
    batch_size: int = Field(15, ge=1, le=50, description="PDFs per batch")
    max_concurrent_batches: int = Field(4, ge=1, le=10, description="Maximum concurrent batches")
    timeout_seconds: int = Field(180, ge=30, le=600, description="Timeout per PDF")
    
class BatchAnalysisResponse(BaseModel):
    """Response model for batch analysis initiation"""
    message: str
    task_id: str
    total_pdfs: int
    estimated_time_minutes: float
    estimated_cost: float

class BatchAnalysisStatus(BaseModel):
    """Status model for batch analysis progress"""
    task_id: str
    status: str
    progress_percent: float
    current_batch: int
    total_batches: int
    processed_pdfs: int
    total_pdfs: int
    successful: int
    failed: int
    estimated_cost: float
    elapsed_time: float

class BatchAnalysisResult(BaseModel):
    """Final result model for batch analysis"""
    task_id: str
    total_processed: int
    successful: int
    failed: int
    processing_time: float
    cost_estimate: float
    results_summary: Dict[str, Any]

def create_bid_criteria_from_dict(criteria_dict: Dict[str, Any]) -> BidCriteria:
    """Create BidCriteria object from dictionary"""
    if not criteria_dict:
        return DEFAULT_CRITERIA
    
    return BidCriteria(
        emd_required=criteria_dict.get("emd_required", True),
        emd_zero_acceptable=criteria_dict.get("emd_zero_acceptable", False),
        check_contractor_restrictions=criteria_dict.get("check_contractor_restrictions", True),
        check_facility_requirements=criteria_dict.get("check_facility_requirements", True),
        check_certification_requirements=criteria_dict.get("check_certification_requirements", True),
        check_experience_requirements=criteria_dict.get("check_experience_requirements", True),
        restricted_organizations=criteria_dict.get("restricted_organizations", ["MPF", "OFB", "AVNL", "HVF"])
    )

def create_batch_config_from_dict(config_dict: Dict[str, Any]) -> BatchProcessingConfig:
    """Create BatchProcessingConfig object from dictionary"""
    if not config_dict:
        return BatchProcessingConfig()
    
    return BatchProcessingConfig(
        batch_size=config_dict.get("batch_size", 15),
        max_concurrent_batches=config_dict.get("max_concurrent_batches", 4),
        cache_ttl_minutes=config_dict.get("cache_ttl_minutes", 60),
        max_retries=config_dict.get("max_retries", 2),
        timeout_seconds=config_dict.get("timeout_seconds", 180)
    )

async def run_batch_analysis_task(
    task_id: str,
    pdf_paths: List[str],
    criteria: BidCriteria,
    config: BatchProcessingConfig
):
    """Background task for batch PDF analysis"""
    try:
        register_background_task(task_id)
        
        # Progress tracking
        def progress_callback(batch_id: int, current: int, total: int, pdf_path: str):
            # Calculate overall progress
            # This is a simplified progress calculation
            overall_progress = (batch_id * config.batch_size + current) / len(pdf_paths) * 100
            
            update_task_status(
                task_id,
                status=TaskStatus.RUNNING,
                message=f"Processing batch {batch_id}, PDF {current}/{total}",
                stage=f"Batch {batch_id}",
                current=batch_id * config.batch_size + current,
                total=len(pdf_paths)
            )
        
        # Run the batch analysis
        result = await analyze_batch_optimized(
            pdf_paths,
            criteria,
            config,
            progress_callback,
            task_id
        )
        
        # Update final status
        update_task_status(
            task_id,
            status=TaskStatus.COMPLETED,
            message=f"Batch analysis completed: {result.successful} accepted, {result.failed} rejected",
            stage="Completed",
            current=result.total_processed,
            total=result.total_processed,
            result_dtl={
                "total_processed": result.total_processed,
                "successful": result.successful,
                "failed": result.failed,
                "processing_time": result.processing_time,
                "cost_estimate": result.cost_estimate,
                "batch_details": result.batch_details
            }
        )
        
    except Exception as e:
        update_task_status(
            task_id,
            status=TaskStatus.FAILED,
            message=f"Batch analysis failed: {str(e)}",
            stage="Error"
        )
    finally:
        unregister_background_task(task_id)

@router.post(
    "/batch-analyze",
    response_model=BatchAnalysisResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Initiate batch PDF analysis for multiple bids"
)
async def initiate_batch_analysis(
    request: BatchAnalysisRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Initiate batch analysis of PDFs for multiple bids.
    
    This endpoint efficiently processes large numbers of PDFs using:
    - Intelligent caching to reduce API costs
    - Concurrent batch processing for speed
    - Progress tracking and cancellation support
    """
    
    if not request.bid_numbers:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="bid_numbers list cannot be empty"
        )
    
    # Find PDF files for the given bid numbers
    pdf_paths = []
    downloads_dir = Path(settings.DOWNLOADS_BASE_DIR)
    
    for bid_number in request.bid_numbers:
        # Look for PDFs in the downloads directory
        for pdf_file in downloads_dir.rglob(f"*{bid_number}*.pdf"):
            pdf_paths.append(str(pdf_file))
    
    if not pdf_paths:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No PDF files found for the specified bid numbers"
        )
    
    # Create processing configuration
    criteria = create_bid_criteria_from_dict(request.criteria)
    config = create_batch_config_from_dict(request.config)
    
    # Estimate processing time and cost
    estimated_time_minutes = (len(pdf_paths) * 3.5) / 60  # Rough estimate
    estimated_cost = len(pdf_paths) * 0.003  # With caching
    
    # Create task
    task_id = str(uuid.uuid4())
    
    # Initialize task status
    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message="Batch PDF analysis initiated",
        details={
            "total_pdfs": len(pdf_paths),
            "batch_size": config.batch_size,
            "max_concurrent_batches": config.max_concurrent_batches,
            "estimated_time_minutes": estimated_time_minutes,
            "estimated_cost": estimated_cost
        }
    )
    
    # Start background task
    background_tasks.add_task(
        run_batch_analysis_task,
        task_id,
        pdf_paths,
        criteria,
        config
    )
    
    return BatchAnalysisResponse(
        message="Batch PDF analysis initiated",
        task_id=task_id,
        total_pdfs=len(pdf_paths),
        estimated_time_minutes=estimated_time_minutes,
        estimated_cost=estimated_cost
    )

@router.get(
    "/batch-analyze/{task_id}/status",
    response_model=BatchAnalysisStatus,
    summary="Get batch analysis progress status"
)
async def get_batch_analysis_status(task_id: str):
    """Get the current status and progress of a batch analysis task"""
    
    if task_id not in TASK_STATUS_STORE:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    task_info = TASK_STATUS_STORE[task_id]
    
    # Calculate progress metrics
    current = getattr(task_info, 'current', 0)
    total = getattr(task_info, 'total', 1)
    progress_percent = (current / total) * 100 if total > 0 else 0
    
    # Extract details from task info
    details = task_info.details or {}
    
    return BatchAnalysisStatus(
        task_id=task_id,
        status=task_info.status.value,
        progress_percent=progress_percent,
        current_batch=current // details.get("batch_size", 15),
        total_batches=(total + details.get("batch_size", 15) - 1) // details.get("batch_size", 15),
        processed_pdfs=current,
        total_pdfs=total,
        successful=0,  # Would need to track this in task details
        failed=0,      # Would need to track this in task details
        estimated_cost=details.get("estimated_cost", 0.0),
        elapsed_time=0.0  # Would need to track start time
    )

@router.get(
    "/batch-analyze/{task_id}/results",
    response_model=BatchAnalysisResult,
    summary="Get batch analysis final results"
)
async def get_batch_analysis_results(task_id: str):
    """Get the final results of a completed batch analysis task"""
    
    if task_id not in TASK_STATUS_STORE:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    task_info = TASK_STATUS_STORE[task_id]
    
    if task_info.status != TaskStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Task is not completed. Current status: {task_info.status.value}"
        )
    
    # Extract results from task details
    result_details = getattr(task_info, 'result_dtl', {})
    
    return BatchAnalysisResult(
        task_id=task_id,
        total_processed=result_details.get("total_processed", 0),
        successful=result_details.get("successful", 0),
        failed=result_details.get("failed", 0),
        processing_time=result_details.get("processing_time", 0.0),
        cost_estimate=result_details.get("cost_estimate", 0.0),
        results_summary=result_details.get("batch_details", [])
    )
