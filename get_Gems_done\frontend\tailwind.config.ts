import type { Config } from "tailwindcss";

const config: Config = {
	content: [
		"./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	// Dark mode removed as we're using light theme only
	theme: {
		extend: {
			// You can define theme-specific colors here if needed later
			// Example:
			// colors: {
			//   primary: {
			//     light: '#3b82f6', // Your light theme primary
			//     dark: '#60a5fa',  // Your dark theme primary
			//   },
			// },
		},
	},
	plugins: [],
};
export default config;
