# 🎯 GEM Bid Highlighter v2.0

A Chrome extension that highlights bids on the GEM (Government e-Marketplace) website based on Excel exports from your bid processing system.

## ✨ Features

### 🔄 **Automatic Page Navigation Detection**
- Detects hash-based pagination (`#page-1`, `#page-2`, etc.)
- Auto-highlights bids when navigating between pages
- Works with GEM's dynamic content loading

### 📊 **Excel Export Support**
- **Filtered Bids Export**: Highlights bids that match your filtering criteria
- **PDF Processed Export**: Shows processing status (Accepted/Rejected/Pending)
- Automatic export type detection based on column headers

### 🎨 **Smart Highlighting**
- **Individual bid highlighting** (not whole page)
- **Color-coded status indicators**:
  - 🟢 Green: Accepted/Filtered bids
  - 🔴 Red: Rejected bids
  - 🟡 Yellow: Pending/Not in database
- **Accurate bid counting** (~10 bids per page)

### 🚀 **Clean User Experience**
- Drag & drop Excel file upload
- Real-time statistics display
- Auto-enabling highlighting
- Clear status indicators

## 🛠️ Installation

1. **Download the extension files**
2. **Open Chrome** and go to `chrome://extensions/`
3. **Enable Developer mode** (toggle in top-right)
4. **Click "Load unpacked"** and select the extension folder
5. **Pin the extension** to your toolbar for easy access

## 📖 Usage

### 1. **Upload Excel File**
- Click the extension icon in your toolbar
- Drag & drop or click to select your Excel file (.xlsx, .xls)
- Extension automatically detects export type

### 2. **Browse GEM Website**
- Navigate to `https://gem.gov.in` or `https://bidplus.gem.gov.in`
- Go to search results or bid listings
- Bids will highlight automatically

### 3. **Navigate Between Pages**
- Click pagination buttons (Page 1, Page 2, etc.)
- Extension detects hash changes (`#page-1` → `#page-2`)
- Bids re-highlight automatically after 2 seconds

### 4. **View Statistics**
- Check the status panel (top-right corner of page)
- View bid counts and export type information
- Monitor highlighting status in popup

## 📋 Excel File Requirements

### **Column Headers** (case-insensitive):
- **Required**: `Bid Number` or `BidNumber` or `Bid_Number`
- **Optional**: `Items`, `Ministry`, `Department`, `Organization`
- **PDF Status**: `PDF Status` or `Status` (for PDF processed exports)

### **Export Types**:

#### **Filtered Bids Export**
```
Bid Number | Items | Ministry
GEM/2024/B/123 | Auto Parts | Defence
GEM/2024/B/124 | Electronics | Railways
```

#### **PDF Processed Export**
```
Bid Number | Items | PDF Status | Ministry
GEM/2024/B/123 | Auto Parts | ACCEPTED | Defence
GEM/2024/B/124 | Electronics | REJECTED | Railways
GEM/2024/B/125 | Machinery | PENDING | Health
```

## 🎨 Highlighting Colors

| Status | Color | Description |
|--------|-------|-------------|
| 🟢 **Accepted** | Green | PDF processing accepted |
| 🟢 **Filtered** | Green | Matches filter criteria |
| 🔴 **Rejected** | Red | PDF processing rejected |
| 🟡 **Pending** | Yellow | Awaiting PDF processing |
| 🟡 **Not in DB** | Yellow | Bid found on page but not in Excel |

## 🔧 Technical Details

### **Supported URLs**:
- `https://gem.gov.in/*`
- `https://*.gem.gov.in/*`
- `https://bidplus.gem.gov.in/*`

### **Navigation Detection**:
- Hash changes: `#page-1`, `#page-2`, `#page-3`
- URL changes: Full page navigation
- Dynamic content: AJAX/SPA updates

### **Bid Detection Patterns**:
- `GEM/YYYY/X/NNNNN` (Standard GEM format)
- Long numeric IDs (10+ digits)
- Alphanumeric patterns (XX123456)
- Year/letter/number patterns (2024/ABC/123)

## 🐛 Troubleshooting

### **Extension not highlighting bids?**
1. Check if you're on a GEM website
2. Ensure Excel file is uploaded correctly
3. Verify bid number column exists in Excel
4. Check browser console for error messages

### **Navigation not working?**
1. Ensure you're using pagination (not direct URL changes)
2. Check if URL contains `#page-X` pattern
3. Wait 2-3 seconds for content to load
4. Try manual highlighting with popup button

### **Wrong bid count?**
1. Extension counts only highlighted bids on current page
2. GEM typically shows ~10 bids per page
3. Check if bid detection patterns match your data

## 📝 Console Logs

Enable browser console (F12) to see detailed logs:

```
GEM Bid Highlighter v2.0: Content script loaded
GEM Bid Highlighter: Loaded 150 bids from storage
GEM Bid Highlighter: Hash changed from #page-1 to #page-2
GEM Bid Highlighter: Detected page navigation, will re-highlight
GEM Bid Highlighter: Found 12 potential bid elements
GEM Bid Highlighter: Highlighted 8 bids
```

## 🔄 Version History

### **v2.0.0** - Clean & Functional
- Complete rewrite with lessons learned
- Fixed hash navigation detection
- Improved Excel processing
- Better error handling
- Clean, modern UI

### **v1.x** - Previous versions
- Initial implementation
- Various bug fixes and improvements

## 🤝 Support

If you encounter issues:
1. Check the troubleshooting section
2. Enable browser console and check for errors
3. Verify your Excel file format
4. Test on different GEM pages

## 📄 License

This extension is for internal use and bid processing automation.

---

**🎯 GEM Bid Highlighter v2.0 - Making bid review efficient and accurate!**
