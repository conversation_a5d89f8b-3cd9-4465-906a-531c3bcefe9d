#!/usr/bin/env python3
"""
Debug Script for Table Extraction Issues
========================================

This script helps debug table extraction issues by testing the API connection
and table extraction with detailed logging.

Author: AI Assistant
Date: 2025-07-02
"""

import logging
import sys
from vendor_api_tool import VendorAPITool, HTMLTableExtractor, ExcelReportGenerator

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_extraction.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_basic_connection():
    """Test basic API connection."""
    print("🔍 Testing Basic API Connection...")
    try:
        api_tool = VendorAPITool()
        
        # Test connection verification
        if api_tool.verify_connection():
            print("✅ Connection verified successfully")
        else:
            print("❌ Connection verification failed")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_csrf_token():
    """Test CSRF token extraction."""
    print("\n🔍 Testing CSRF Token Extraction...")
    try:
        api_tool = VendorAPITool()
        csrf_token = api_tool.get_csrf_token()
        
        if csrf_token:
            print(f"✅ CSRF token obtained: {csrf_token[:20]}...")
            return True
        else:
            print("❌ Failed to obtain CSRF token")
            return False
    except Exception as e:
        print(f"❌ CSRF token test failed: {e}")
        return False

def test_simple_request():
    """Test a simple API request without filters."""
    print("\n🔍 Testing Simple API Request...")
    try:
        api_tool = VendorAPITool()
        
        # Try GET request first
        print("Attempting GET request...")
        html_content = api_tool.get_paginated_data()
        
        if html_content:
            print(f"✅ GET request successful. Content length: {len(html_content)}")
            return html_content
        else:
            print("❌ GET request failed, trying POST request...")
            
            # Try POST request with minimal parameters
            search_params = {}
            html_content = api_tool.make_search_request(search_params)
            
            if html_content:
                print(f"✅ POST request successful. Content length: {len(html_content)}")
                return html_content
            else:
                print("❌ Both GET and POST requests failed")
                return None
                
    except Exception as e:
        print(f"❌ Simple request test failed: {e}")
        return None

def test_table_extraction(html_content):
    """Test table extraction from HTML content."""
    print("\n🔍 Testing Table Extraction...")
    try:
        if not html_content:
            print("❌ No HTML content to extract from")
            return False
            
        extractor = HTMLTableExtractor()
        table_data = extractor.extract_main_table(html_content)
        
        if table_data:
            print(f"✅ Table extraction successful. Rows: {len(table_data)}")
            if table_data:
                print(f"   First row: {table_data[0][:3]}..." if len(table_data[0]) > 3 else f"   First row: {table_data[0]}")
            return True
        else:
            print("❌ Table extraction returned no data")
            return False
            
    except Exception as e:
        print(f"❌ Table extraction test failed: {e}")
        return False

def analyze_html_content(html_content):
    """Analyze HTML content to understand structure."""
    print("\n🔍 Analyzing HTML Content Structure...")
    try:
        if not html_content:
            print("❌ No HTML content to analyze")
            return
            
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Basic statistics
        print(f"📊 HTML Analysis:")
        print(f"   Total length: {len(html_content)} characters")
        print(f"   Number of tables: {len(soup.find_all('table'))}")
        print(f"   Number of forms: {len(soup.find_all('form'))}")
        print(f"   Number of divs: {len(soup.find_all('div'))}")
        
        # Check for common content indicators
        if "no data" in html_content.lower():
            print("⚠️  Content contains 'no data' message")
        if "no records" in html_content.lower():
            print("⚠️  Content contains 'no records' message")
        if "error" in html_content.lower():
            print("⚠️  Content may contain error messages")
            
        # Analyze tables
        tables = soup.find_all('table')
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            print(f"   Table {i+1}: {len(rows)} rows")
            if rows:
                first_row_cells = rows[0].find_all(['td', 'th'])
                print(f"      First row has {len(first_row_cells)} cells")
                
    except Exception as e:
        print(f"❌ HTML analysis failed: {e}")

def run_debug_tests():
    """Run all debug tests."""
    print("=" * 70)
    print("🐛 VENDOR API TOOL - DEBUG EXTRACTION TESTS")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Basic connection
    if test_basic_connection():
        tests_passed += 1
    
    # Test 2: CSRF token
    if test_csrf_token():
        tests_passed += 1
    
    # Test 3: Simple request
    html_content = test_simple_request()
    if html_content:
        tests_passed += 1
        
        # Analyze the HTML content
        analyze_html_content(html_content)
        
        # Test 4: Table extraction
        if test_table_extraction(html_content):
            tests_passed += 1
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 DEBUG TEST RESULTS")
    print("=" * 70)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The issue may be with specific search parameters.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        
    print("=" * 70)

if __name__ == "__main__":
    run_debug_tests()
