"use client";

import { useState, useEffect } from "react";
import { fetchApi } from "@/lib/api";

interface ScrapeSource {
	source_type: "ministry_organization" | "location";
	details: {
		ministry?: string;
		organization?: string;
		state?: string;
		city?: string;
	};
}

interface ScrapeSourceFiltersProps {
	onShowLatestScraped: () => void;
	onShowAllFromLatestSources: () => void;
	onShowAllBids: () => void;
	onShowBySource: (sourceType: string, details: any) => void;
	onShowByMultipleSources: (selectedSources: ScrapeSource[]) => void; // New prop for multiple selection
	isLoading: boolean;
	activeFilter?: string; // Track which filter is currently active
}

export default function ScrapeSourceFilters({
	onShowLatestScraped,
	onShowAllFromLatestSources,
	onShowAllBids,
	onShowBySource,
	onShowByMultipleSources,
	isLoading,
	activeFilter,
}: ScrapeSourceFiltersProps) {
	const [scrapeSources, setScrapeSources] = useState<ScrapeSource[]>([]);
	const [isLoadingSources, setIsLoadingSources] = useState(false);
	const [selectedSources, setSelectedSources] = useState<ScrapeSource[]>([]);

	useEffect(() => {
		fetchScrapeSources();
	}, []);

	const fetchScrapeSources = async () => {
		setIsLoadingSources(true);
		try {
			const sources = await fetchApi<ScrapeSource[]>(
				"/api/v1/bids/scrape-sources"
			);
			setScrapeSources(sources);
		} catch (error) {
			console.error("Failed to fetch scrape sources:", error);
		} finally {
			setIsLoadingSources(false);
		}
	};

	const formatSourceLabel = (source: ScrapeSource) => {
		if (source.source_type === "ministry_organization") {
			return `${source.details.ministry || "Unknown Ministry"}${
				source.details.organization ? ` - ${source.details.organization}` : ""
			}`;
		} else {
			return `${source.details.state || "Unknown State"}${
				source.details.city && source.details.city !== "All Cities"
					? ` - ${source.details.city}`
					: ""
			}`;
		}
	};

	const handleSourceToggle = (source: ScrapeSource) => {
		const isSelected = selectedSources.some(
			(s) => JSON.stringify(s) === JSON.stringify(source)
		);

		let newSelectedSources: ScrapeSource[];
		if (isSelected) {
			// Remove from selection
			newSelectedSources = selectedSources.filter(
				(s) => JSON.stringify(s) !== JSON.stringify(source)
			);
		} else {
			// Add to selection
			newSelectedSources = [...selectedSources, source];
		}

		setSelectedSources(newSelectedSources);

		// Apply filter with selected sources
		if (newSelectedSources.length > 0) {
			onShowByMultipleSources(newSelectedSources);
		} else {
			// If no sources selected, show all bids
			onShowAllBids();
		}
	};

	const isSourceSelected = (source: ScrapeSource) => {
		return selectedSources.some(
			(s) => JSON.stringify(s) === JSON.stringify(source)
		);
	};

	return (
		<div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-lg dark:shadow-black/20 mb-6">
			<h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
				Show Bids
			</h3>

			<div className="flex flex-wrap gap-2 mb-4">
				<button
					onClick={onShowAllBids}
					disabled={isLoading}
					className={`btn btn-primary px-4 py-2 rounded-lg text-sm ${
						activeFilter === "all"
							? "bg-blue-800 text-white"
							: "bg-blue-600 hover:bg-blue-700 text-white"
					}`}
					style={{ marginRight: "10px" }}
				>
					All Bids
				</button>

				<button
					onClick={onShowLatestScraped}
					disabled={isLoading}
					className={`btn btn-secondary border-none px-4 py-2 rounded-lg text-sm ${
						activeFilter === "latest"
							? "bg-green-800 text-white"
							: "bg-green-600 hover:bg-green-700 text-white"
					}`}
					style={{ marginRight: "10px" }}
				>
					Latest Scraped
				</button>

				<button
					onClick={onShowAllFromLatestSources}
					disabled={isLoading}
					className={`btn btn-accent border-none px-4 py-2 rounded-lg text-sm ${
						activeFilter === "latest-sources"
							? "bg-indigo-800 text-white"
							: "bg-indigo-600 hover:bg-indigo-700 text-white"
					}`}
					style={{ marginRight: "10px" }}
				>
					All from Latest Sources
				</button>
			</div>

			{/* {isLoadingSources ? (
				<div className="text-sm text-gray-600 dark:text-gray-400">
					Loading scrape sources...
				</div>
			) : scrapeSources.length > 0 ? (
				<div>
					<h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Filter by Source:
					</h4>
					<div className="flex flex-wrap gap-2">
						{scrapeSources.map((source, index) => {
							const isSelected = isSourceSelected(source);
							return (
								<button
									key={index}
									onClick={() => handleSourceToggle(source)}
									disabled={isLoading}
									className={`btn text-xs border-none px-3 py-1 rounded-lg transition-all ${
										isSelected
											? source.source_type === "ministry_organization"
												? "bg-purple-700 text-white shadow-lg"
												: "bg-orange-700 text-white shadow-lg"
											: source.source_type === "ministry_organization"
											? "bg-purple-100 hover:bg-purple-200 text-purple-800 dark:bg-purple-800 dark:text-purple-100"
											: "bg-orange-100 hover:bg-orange-200 text-orange-800 dark:bg-orange-800 dark:text-orange-100"
									}`}
									style={{ marginRight: "10px", marginBottom: "10px" }}
								>
									{formatSourceLabel(source)}
									{isSelected && " ✓"}
								</button>
							);
						})}
					</div>
				</div>
			) : (
				<div className="text-sm text-gray-500 dark:text-gray-400">
					No scrape sources available. Scrape some bids first to see source
					filters.
				</div>
			)} */}
		</div>
	);
}
