# app/services/filter_service.py (New File)
from typing import List, Optional, Set
from app.schemas import bid_schemas # Assuming BidInDB or similar structure passed in

def filter_bids_by_keywords_in_items(
    bids: List[bid_schemas.BidInDB], # Or List[db_models.Bid] if passing SA models
    keywords: List[str]
) -> List[bid_schemas.BidInDB]: # Or List[db_models.Bid]
    """
    Filters a list of bid objects based on whether their 'items' field
    contains any of the provided keywords (case-insensitive).
    """
    if not keywords:
        return bids # No keywords, return all bids passed in
    
    filtered_bids = []
    lower_keywords = [kw.lower().strip() for kw in keywords if kw.strip()] # Lowercase and strip whitespace

    if not lower_keywords: # If all keywords were just whitespace
        return bids

    for bid in bids:
        # Ensure bid.items exists and is a string
        items_field_value = getattr(bid, 'items', None) # Works for Pydantic and SA models
        
        if items_field_value and isinstance(items_field_value, str):
            item_text_lower = items_field_value.lower()
            for kw in lower_keywords:
                if kw in item_text_lower:
                    filtered_bids.append(bid)
                    break # Found a keyword in this bid's items, move to next bid
    
    return filtered_bids


def filter_bids_by_keywords(
    bids: List[bid_schemas.BidInDB], # Or whatever schema your stored bids are
    keywords: List[str],
    fields_to_check: List[str] = ["items"] # Default to checking 'items' field
) -> List[bid_schemas.BidInDB]:
    if not keywords:
        return bids # No keywords, return all
    
    filtered_bids = []
    # Prepare keywords for case-insensitive matching
    lower_keywords = [kw.lower() for kw in keywords]

    for bid in bids:
        found_keyword = False
        for field_name in fields_to_check:
            field_value = getattr(bid, field_name, None) # Get field like bid.items
            if field_value and isinstance(field_value, str):
                for kw in lower_keywords:
                    if kw in field_value.lower():
                        filtered_bids.append(bid)
                        found_keyword = True
                        break # Found a keyword in this field for this bid
            if found_keyword:
                break # Move to next bid
        # Add logic here if you need to check raw_api_doc for keywords in other places
    return filtered_bids

def filter_bids_by_drawing_numbers(
    bids: List[bid_schemas.BidInDB],
    drawing_numbers: List[str],
    # Assume drawing number might be in raw_api_doc or a specific parsed field
    drawing_number_field_in_raw: Optional[str] = "drawing_details.drawing_no" # Example path
) -> List[bid_schemas.BidInDB]:
    if not drawing_numbers:
        return bids
    
    filtered_bids = []
    drawing_numbers_set = {dn.lower() for dn in drawing_numbers} # For efficient lookup

    for bid in bids:
        # Option 1: If you parse drawing_no into a top-level field in BidInDB
        # bid_drawing_no = getattr(bid, "drawing_number_parsed_field", None)
        # if bid_drawing_no and isinstance(bid_drawing_no, str) and bid_drawing_no.lower() in drawing_numbers_set:
        #     filtered_bids.append(bid)
        #     continue

        # Option 2: Check within raw_api_doc (more flexible but complex)
        if bid.raw_api_doc and drawing_number_field_in_raw:
            # Helper to navigate nested dicts, e.g., using a path like "drawing_details.drawing_no"
            # For simplicity, let's assume it's a direct key or you have a helper
            # This part needs to be robust based on actual raw_api_doc structure
            try:
                # Example: if raw_api_doc = {"drawing_details": {"drawing_no": "XYZ123"}}
                path_keys = drawing_number_field_in_raw.split('.')
                current_val = bid.raw_api_doc
                for key in path_keys:
                    if isinstance(current_val, dict) and key in current_val:
                        current_val = current_val[key]
                    else:
                        current_val = None
                        break
                
                if current_val and isinstance(current_val, str) and current_val.lower() in drawing_numbers_set:
                    filtered_bids.append(bid)
            except Exception: # Broad exception for path navigation issues
                pass 
    return filtered_bids