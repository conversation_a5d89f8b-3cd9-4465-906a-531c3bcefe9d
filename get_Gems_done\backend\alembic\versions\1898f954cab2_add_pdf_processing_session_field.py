"""add_pdf_processing_session_field

Revision ID: 1898f954cab2
Revises: 30abb5f2c386
Create Date: 2025-05-29 19:45:59.206528

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1898f954cab2'
down_revision: Union[str, None] = '30abb5f2c386'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bids', sa.Column('pdf_processing_session', sa.String(), nullable=True))
    op.create_index(op.f('ix_bids_pdf_processing_session'), 'bids', ['pdf_processing_session'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bids_pdf_processing_session'), table_name='bids')
    op.drop_column('bids', 'pdf_processing_session')
    # ### end Alembic commands ###
