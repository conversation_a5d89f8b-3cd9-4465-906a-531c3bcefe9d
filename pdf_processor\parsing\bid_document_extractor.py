import os
import re
import sys
import json
import argparse
import fitz  # PyMuPDF

def extract_text_with_pymupdf(pdf_path):
    """Extract text from a PDF file using PyMuPDF with layout preservation."""
    try:
        doc = fitz.open(pdf_path)
        plain_text = ""
        structured_text = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Extract plain text
            page_text = page.get_text()
            plain_text += page_text + "\n\n"
            
            # Extract structured text with layout information
            blocks = page.get_text("dict")["blocks"]
            page_structured = []
            
            for block in blocks:
                if block["type"] == 0:  # Text block
                    block_text = ""
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]
                        block_text += line_text + "\n"
                    
                    page_structured.append({
                        "type": "text",
                        "text": block_text,
                        "bbox": block["bbox"],  # [x0, y0, x1, y1]
                    })
                elif block["type"] == 1:  # Image block
                    page_structured.append({
                        "type": "image",
                        "bbox": block["bbox"],
                    })
            
            structured_text.append({
                "page": page_num + 1,
                "blocks": page_structured
            })
        
        doc.close()
        return {
            "plain_text": plain_text,
            "structured_text": structured_text
        }
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {str(e)}")
        return {
            "plain_text": "",
            "structured_text": []
        }

def extract_tables_with_pymupdf(pdf_path):
    """Extract tables from a PDF file using PyMuPDF."""
    try:
        doc = fitz.open(pdf_path)
        tables = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find tables on the page
            tab = page.find_tables()
            if tab.tables:
                for table in tab.tables:
                    # Convert table to a list of lists
                    rows = []
                    for row_cells in table.extract():
                        # Handle None values in cells
                        processed_cells = []
                        for cell in row_cells:
                            if cell is None:
                                processed_cells.append("")
                            else:
                                processed_cells.append(str(cell).strip())
                        rows.append(processed_cells)
                    
                    tables.append({
                        "page": page_num + 1,
                        "rows": rows,
                        "bbox": table.bbox  # [x0, y0, x1, y1]
                    })
        
        doc.close()
        return tables
    except Exception as e:
        print(f"Error extracting tables from {pdf_path}: {str(e)}")
        return []

def extract_emd_details(text, tables):
    """Extract EMD (Earnest Money Deposit) details from text and tables."""
    emd_details = {}
    
    # Look for EMD section in text
    emd_patterns = [
        r'EMD\s+(?:Amount|Details)[\s:]+([^\n]+)',
        r'Earnest\s+Money\s+Deposit[\s:]+([^\n]+)',
        r'EMD\s+(?:Amount|Details)[\s:]+Rs\.?\s*([\d,\.]+)',
        r'EMD\s+(?:Amount|Details)[\s:]+INR\s*([\d,\.]+)'
    ]
    
    for pattern in emd_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            emd_details["EMD_Amount_Text"] = match.group(1).strip()
            break
    
    # Look for EMD table
    emd_table = None
    for table in tables:
        # Convert table to text for easier searching
        table_text = ""
        for row in table["rows"]:
            table_text += " ".join(row) + "\n"
        
        # Check if this table contains EMD information
        if re.search(r'EMD|Earnest\s+Money|Bid\s+Security', table_text, re.IGNORECASE):
            emd_table = table
            break
    
    if emd_table:
        emd_details["EMD_Table"] = emd_table["rows"]
        
        # Try to extract specific EMD details from the table
        for row in emd_table["rows"]:
            row_text = " ".join(row).lower()
            
            # Look for amount
            if "amount" in row_text or "value" in row_text:
                for cell in row:
                    # Look for currency patterns
                    amount_match = re.search(r'(?:Rs\.?|INR)\s*([\d,\.]+)', cell, re.IGNORECASE)
                    if amount_match:
                        emd_details["EMD_Amount"] = amount_match.group(1).strip()
                    elif re.search(r'\d+[,\d]*(\.\d+)?', cell):
                        emd_details["EMD_Amount"] = cell.strip()
            
            # Look for percentage
            if "percentage" in row_text or "%" in row_text:
                for cell in row:
                    percentage_match = re.search(r'(\d+(\.\d+)?)\s*%', cell)
                    if percentage_match:
                        emd_details["EMD_Percentage"] = percentage_match.group(1).strip() + "%"
            
            # Look for exemption
            if "exempt" in row_text or "waive" in row_text:
                emd_details["EMD_Exemption"] = "Yes"
                emd_details["EMD_Exemption_Details"] = row_text
    
    # If no EMD details found in tables, try to extract from text
    if not emd_table:
        # Look for EMD amount
        amount_match = re.search(r'EMD\s+(?:Amount|Details)[\s:]+(?:Rs\.?|INR)\s*([\d,\.]+)', text, re.IGNORECASE)
        if amount_match:
            emd_details["EMD_Amount"] = amount_match.group(1).strip()
        
        # Look for EMD percentage
        percentage_match = re.search(r'EMD\s+Percentage[\s:]+(\d+(\.\d+)?)\s*%', text, re.IGNORECASE)
        if percentage_match:
            emd_details["EMD_Percentage"] = percentage_match.group(1).strip() + "%"
        
        # Look for EMD exemption
        exemption_match = re.search(r'EMD\s+Exemption[\s:]+([^\n]+)', text, re.IGNORECASE)
        if exemption_match:
            emd_details["EMD_Exemption"] = "Yes"
            emd_details["EMD_Exemption_Details"] = exemption_match.group(1).strip()
    
    return emd_details

def extract_bid_terms_and_conditions(text):
    """Extract Buyer Added Bid Specific Terms and Conditions."""
    terms_and_conditions = {}
    
    # Look for the section header
    section_patterns = [
        r'(Buyer\s+Added\s+Bid\s+Specific\s+Terms\s+and\s+Conditions)',
        r'(Additional\s+Terms\s+and\s+Conditions)',
        r'(Special\s+Terms\s+and\s+Conditions)'
    ]
    
    section_start = None
    section_end = None
    section_title = None
    
    for pattern in section_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            section_title = match.group(1)
            section_start = match.start()
            break
    
    if section_start is not None:
        # Look for the next section header or end of document
        next_section_patterns = [
            r'Disclaimer',
            r'This\s+Bid\s+is\s+governed\s+by',
            r'---+\s*Thank\s+You\s*---+',
            r'General\s+Terms\s+and\s+Conditions',
            r'Standard\s+Terms\s+and\s+Conditions'
        ]
        
        for pattern in next_section_patterns:
            match = re.search(pattern, text[section_start:], re.IGNORECASE)
            if match:
                section_end = section_start + match.start()
                break
        
        if section_end is None:
            section_end = len(text)
        
        # Extract the section content
        section_content = text[section_start:section_end].strip()
        
        # Remove the section header from the content
        if section_title:
            section_content = section_content.replace(section_title, "", 1).strip()
        
        # Split into individual terms
        terms_list = []
        
        # Try to split by numbered items
        numbered_terms = re.findall(r'(?:\d+\.\s*|\(\d+\)\s*)([^\n]+(?:\n(?!\d+\.\s*|\(\d+\)\s*)[^\n]+)*)', section_content)
        
        if numbered_terms:
            terms_list = numbered_terms
        else:
            # Try to split by bullet points
            bullet_terms = re.findall(r'(?:•|\*|\-)\s*([^\n]+(?:\n(?!•|\*|\-)[^\n]+)*)', section_content)
            if bullet_terms:
                terms_list = bullet_terms
            else:
                # Split by newlines as a fallback
                terms_list = [line.strip() for line in section_content.split('\n') if line.strip()]
        
        terms_and_conditions["section_title"] = section_title
        terms_and_conditions["terms_list"] = terms_list
        terms_and_conditions["full_content"] = section_content
    
    return terms_and_conditions

def process_bid_document(pdf_path, output_dir=None):
    """Process a bid document PDF to extract EMD details and bid terms."""
    print(f"Processing bid document: {pdf_path}")
    
    # Extract text and tables
    text_result = extract_text_with_pymupdf(pdf_path)
    tables = extract_tables_with_pymupdf(pdf_path)
    
    if not text_result["plain_text"]:
        print("No text extracted from PDF.")
        return {
            "success": False,
            "error": "No text extracted"
        }
    
    # Extract EMD details
    emd_details = extract_emd_details(text_result["plain_text"], tables)
    
    # Extract bid terms and conditions
    bid_terms = extract_bid_terms_and_conditions(text_result["plain_text"])
    
    # Prepare results
    results = {
        "pdf_path": pdf_path,
        "emd_details": emd_details,
        "bid_terms_and_conditions": bid_terms
    }
    
    # Save results if output directory is specified
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        
        pdf_name = os.path.basename(pdf_path)
        base_name = os.path.splitext(pdf_name)[0]
        
        # Create a subdirectory for this PDF
        pdf_dir = os.path.join(output_dir, base_name)
        os.makedirs(pdf_dir, exist_ok=True)
        
        # Save extracted text
        with open(os.path.join(pdf_dir, f"{base_name}_text.txt"), "w", encoding="utf-8") as f:
            f.write(text_result["plain_text"])
        
        # Save EMD details
        with open(os.path.join(pdf_dir, f"{base_name}_emd_details.json"), "w", encoding="utf-8") as f:
            json.dump(emd_details, f, indent=2)
        
        # Save bid terms and conditions
        with open(os.path.join(pdf_dir, f"{base_name}_bid_terms.json"), "w", encoding="utf-8") as f:
            json.dump(bid_terms, f, indent=2)
        
        # Save all results
        with open(os.path.join(pdf_dir, f"{base_name}_results.json"), "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2)
        
        # Save EMD details in human-readable format
        with open(os.path.join(pdf_dir, f"{base_name}_emd_details.txt"), "w", encoding="utf-8") as f:
            f.write("EMD (Earnest Money Deposit) Details:\n\n")
            
            if "EMD_Amount" in emd_details:
                f.write(f"Amount: {emd_details['EMD_Amount']}\n")
            
            if "EMD_Percentage" in emd_details:
                f.write(f"Percentage: {emd_details['EMD_Percentage']}\n")
            
            if "EMD_Exemption" in emd_details:
                f.write(f"Exemption: {emd_details['EMD_Exemption']}\n")
                if "EMD_Exemption_Details" in emd_details:
                    f.write(f"Exemption Details: {emd_details['EMD_Exemption_Details']}\n")
            
            if "EMD_Table" in emd_details:
                f.write("\nEMD Table:\n")
                for row in emd_details["EMD_Table"]:
                    f.write("| " + " | ".join(row) + " |\n")
        
        # Save bid terms in human-readable format
        with open(os.path.join(pdf_dir, f"{base_name}_bid_terms.txt"), "w", encoding="utf-8") as f:
            if "section_title" in bid_terms:
                f.write(f"{bid_terms['section_title']}\n")
                f.write("=" * len(bid_terms['section_title']) + "\n\n")
                
                if "terms_list" in bid_terms and bid_terms["terms_list"]:
                    for i, term in enumerate(bid_terms["terms_list"]):
                        f.write(f"{i+1}. {term}\n\n")
                elif "full_content" in bid_terms:
                    f.write(bid_terms["full_content"])
            else:
                f.write("No bid-specific terms and conditions found.")
    
    # Print results
    print("\n--- Results ---")
    
    print("\nEMD Details:")
    if emd_details:
        if "EMD_Amount" in emd_details:
            print(f"Amount: {emd_details['EMD_Amount']}")
        
        if "EMD_Percentage" in emd_details:
            print(f"Percentage: {emd_details['EMD_Percentage']}")
        
        if "EMD_Exemption" in emd_details:
            print(f"Exemption: {emd_details['EMD_Exemption']}")
            if "EMD_Exemption_Details" in emd_details:
                print(f"Exemption Details: {emd_details['EMD_Exemption_Details']}")
    else:
        print("No EMD details found.")
    
    print("\nBid Terms and Conditions:")
    if "section_title" in bid_terms:
        print(f"Section: {bid_terms['section_title']}")
        
        if "terms_list" in bid_terms and bid_terms["terms_list"]:
            print(f"Found {len(bid_terms['terms_list'])} terms")
            for i, term in enumerate(bid_terms["terms_list"][:3]):  # Show first 3 terms
                print(f"{i+1}. {term}")
            
            if len(bid_terms["terms_list"]) > 3:
                print(f"... and {len(bid_terms['terms_list']) - 3} more terms")
    else:
        print("No bid-specific terms and conditions found.")
    
    if output_dir:
        print(f"\nResults saved to: {pdf_dir}")
    
    return {
        "success": True,
        "pdf_path": pdf_path,
        "has_emd_details": bool(emd_details),
        "has_bid_terms": "section_title" in bid_terms,
        "emd_details": emd_details,
        "bid_terms_and_conditions": bid_terms
    }

def main():
    parser = argparse.ArgumentParser(description="Extract EMD details and bid terms from PDF documents")
    parser.add_argument("--pdf", help="Path to the PDF file")
    parser.add_argument("--output_dir", default="bid_results", help="Directory to save results")
    args = parser.parse_args()
    
    # Get PDF path
    if args.pdf:
        pdf_path = args.pdf
    elif len(sys.argv) > 1 and sys.argv[1].endswith(".pdf"):
        pdf_path = sys.argv[1]
    else:
        pdf_path = r"../bids/NILAY 2/GEM-2025-B-6060979.pdf"
    
    # Check if file exists
    if not os.path.exists(pdf_path):
        print(f"File not found: {pdf_path}")
        return
    
    # Process PDF
    process_bid_document(pdf_path, args.output_dir)

if __name__ == "__main__":
    main()
