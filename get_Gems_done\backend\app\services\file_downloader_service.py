# app/services/file_downloader_service.py (New File)
import requests
import os
import shutil # For saving streamed content
from urllib.parse import urlparse
import re
from typing import Optional

from app.core.config import settings # If you add DOWNLOADS_BASE_DIR to settings

# Configure a base download directory in your .env and app/core/config.py
# E.g., DOWNLOADS_BASE_DIR = "downloaded_bids"
DEFAULT_DOWNLOAD_PATH = getattr(settings, "DOWNLOADS_BASE_DIR", "downloaded_bids")
os.makedirs(DEFAULT_DOWNLOAD_PATH, exist_ok=True) # Ensure base directory exists

def sanitize_filename(filename: str) -> str:
    """Basic filename sanitization."""
    filename = re.sub(r'[^\w\s.-]', '', filename) # Remove invalid chars
    filename = re.sub(r'\s+', '_', filename).strip('_.- ') # Replace whitespace, clean edges
    return filename if filename else "downloaded_file"


def download_file(
    session: requests.Session, 
    doc_url: str, 
    target_dir: str, 
    base_filename: str, # e.g., derived from bid_number
    # base_filename_stem: str 
) -> Optional[str]: # Returns path to downloaded file or None
    """Downloads a file from a URL to a target directory."""
    print(f"Downloader: Attempting to download {doc_url}")
    local_filename = ""
    try:
        # Create target directory if it doesn't exist
        os.makedirs(target_dir, exist_ok=True)

        with session.get(doc_url, stream=True, timeout=(15, 60)) as r:
            r.raise_for_status()
            
            # Try to get original filename from Content-Disposition header
            content_disposition = r.headers.get('content-disposition')
            original_filename = None
            if content_disposition:
                fname_match = re.findall('filename="?([^"]+)"?', content_disposition)
                if fname_match:
                    original_filename = fname_match[0]
            
            # If no filename from header, try to get from URL path
            if not original_filename:
                parsed_url = urlparse(doc_url)
                original_filename = os.path.basename(parsed_url.path)

            # Sanitize and construct filename
            sane_original_filename = sanitize_filename(original_filename or "document")
            file_extension = os.path.splitext(sane_original_filename)[1]
            if not file_extension and "pdf" in r.headers.get("content-type","").lower(): # Guess extension if missing
                file_extension = ".pdf"
            
            # Use base_filename + original (or part of it) + extension
            local_filename_stem = sanitize_filename(base_filename)
            # Keep original filename short if it's too long
            sane_original_name_part = (sane_original_filename.rsplit('.',1)[0])[:50]


            # Ensure unique filename if multiple docs for one bid_number
            # This simple version just uses a base_filename + original. Needs better uniqueness.
            # For multiple files for one bid, the caller of download_file should vary base_filename
            # or add a counter. Let's assume base_filename itself incorporates a counter or unique doc name part.
            local_filename = f"{local_filename_stem}_{sane_original_name_part}{file_extension}"
            if not file_extension and sane_original_filename == "document": # if still generic
                 local_filename = f"{local_filename_stem}{file_extension or '.dat'}"


            full_path = os.path.join(target_dir, local_filename)
            
            # Ensure unique path to avoid overwriting (if base_filename doesn't ensure uniqueness)
            counter = 1
            temp_full_path = full_path
            while os.path.exists(temp_full_path):
                name, ext = os.path.splitext(full_path)
                temp_full_path = f"{name}_{counter}{ext}"
                counter += 1
            full_path = temp_full_path
            local_filename = os.path.basename(full_path) # update local_filename if counter was added

            with open(full_path, 'wb') as f:
                shutil.copyfileobj(r.raw, f)
            print(f"Downloader: Successfully downloaded to {full_path}")
            return full_path
    except requests.exceptions.RequestException as e:
        print(f"Downloader: Failed to download {doc_url}. Error: {e}")
    except Exception as e:
        print(f"Downloader: Unexpected error during download of {doc_url}: {e}")
        # import traceback; traceback.print_exc()
    return None