# Vendor Registration Portal API Tool

A comprehensive Python automation tool for extracting vendor data from the DDPDOO Government Portal and generating beautifully formatted Word documents with nested table support and PDF download links.

## 🚀 Features

- **API Integration**: Seamlessly connects to the DDPDOO vendor registration portal
- **Smart Data Extraction**: Automatically identifies and extracts main data tables from HTML responses
- **Beautiful Word Documents**: Generates professionally formatted Word documents with:
  - Nested table support preserving HTML structure
  - PDF download links as clickable buttons
  - Custom styling and formatting
  - Auto-sized tables
  - Search parameter summary
  - Data statistics
- **Multiple Interfaces**: 
  - Interactive UI for beginners
  - Command-line interface for automation
  - Configuration file support
- **Robust Error Handling**: Comprehensive logging and error recovery
- **Flexible Search**: Support for all portal search parameters

## 📋 Requirements

- Python 3.7 or higher
- Internet connection
- Windows (for batch file) or any OS (for Python scripts)

## 🛠️ Installation

1. **Clone or download** this repository to your local machine
2. **Navigate** to the `vendor-register-portal` directory
3. **Run the batch file** (Windows) or install dependencies manually:

### Windows (Recommended)
```batch
run_vendor_tool.bat
```

### Manual Installation
```bash
pip install -r requirements.txt
```

## 🎯 Quick Start

### Option 1: Interactive UI (Recommended for Beginners)
```bash
python interactive_ui.py
```

### Option 2: Command Line with Parameters
```bash
python vendor_api_tool.py --location "Rajkot" --product "Cylinder" --output "my_report.xlsx"
```

### Option 3: Configuration File
```bash
python vendor_api_tool.py --config config_template.json
```

## 📖 Usage Guide

### Interactive UI
The interactive interface guides you through:
1. **Configure Search Parameters**: Set location, product, vendor, etc.
2. **Configure Output Settings**: Customize filename and report title
3. **Run Data Extraction**: Execute the API calls and generate reports
4. **Save/Load Configurations**: Reuse your settings

### Command Line Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `--factory-id` | Factory ID to search for | `--factory-id "20"` |
| `--location` | Location to search for | `--location "Rajkot"` |
| `--industry-category` | Industry category (L/M/S) | `--industry-category "L"` |
| `--product` | Product to search for | `--product "Cylinder"` |
| `--used-for` | Usage type (R/C/I) | `--used-for "R"` |
| `--vendor` | Vendor name/keyword | `--vendor "cee"` |
| `--output` | Output Excel filename | `--output "report.xlsx"` |
| `--config` | Configuration file path | `--config "my_config.json"` |

### Industry Categories
- **L**: Large Scale
- **M**: Medium Scale  
- **S**: Small Scale

### Usage Types
- **R**: Residential
- **C**: Commercial
- **I**: Industrial

## 📁 Configuration File Format

Create a JSON file with your search parameters:

```json
{
    "search_params": {
        "factory_id": "20",
        "location": "Rajkot",
        "industry_category": "L",
        "product": "Cylinder",
        "used_for": "R",
        "vendor": "cee"
    },
    "output_settings": {
        "filename": "vendor_report_{timestamp}.xlsx",
        "title": "Custom Vendor Report"
    }
}
```

## 📊 Excel Report Features

The generated Excel reports include:

### Header Section
- **Report Title**: Customizable title
- **Generation Timestamp**: When the report was created
- **Search Parameters**: Summary of search criteria used

### Data Section
- **Professional Styling**: Blue headers with white text
- **Alternating Row Colors**: Gray and white for easy reading
- **Auto-sized Columns**: Columns automatically adjust to content
- **Data Type Detection**: Numbers are properly formatted

### Summary Section
- **Total Records**: Count of data rows
- **Total Columns**: Number of data fields
- **Generation Statistics**: Processing information

## 🔧 Advanced Usage

### Batch Processing
Create multiple configuration files and process them in sequence:

```bash
for config in config1.json config2.json config3.json; do
    python vendor_api_tool.py --config "$config"
done
```

### Custom Output Naming
Use timestamp in filenames:
```bash
python vendor_api_tool.py --location "Mumbai" --output "mumbai_vendors_$(date +%Y%m%d).xlsx"
```

### Logging
All operations are logged to `vendor_api_tool.log` for debugging and audit purposes.

## 🐛 Troubleshooting

### Common Issues

1. **"Python not found"**
   - Install Python from https://python.org
   - Ensure Python is added to PATH

2. **"Module not found"**
   - Run: `pip install -r requirements.txt`
   - Check your Python environment

3. **"No data extracted"**
   - Check your internet connection
   - Verify search parameters are valid
   - Check the log file for detailed errors

4. **"API request failed"**
   - The portal might be temporarily unavailable
   - Check if the portal URL has changed
   - Verify your search parameters

### Debug Mode
Enable detailed logging by modifying the logging level in the script:
```python
logging.basicConfig(level=logging.DEBUG)
```

## 📝 File Structure

```
vendor-register-portal/
├── vendor_api_tool.py          # Main API tool
├── interactive_ui.py           # Interactive user interface
├── run_vendor_tool.bat         # Windows batch runner
├── requirements.txt            # Python dependencies
├── config_template.json        # Configuration template
├── README.md                   # This documentation
├── link.txt                    # API endpoint information
└── vendor_api_tool.log         # Generated log file
```

## 🤝 Contributing

Feel free to submit issues, feature requests, or improvements to this tool.

## 📄 License

This tool is provided as-is for educational and automation purposes.

## 🆘 Support

For support or questions:
1. Check the troubleshooting section
2. Review the log files
3. Ensure all requirements are met
4. Verify the portal is accessible

---

**Happy Data Extracting! 🎉**
