// next.config.mjs (or next.config.js)
/** @type {import('next').NextConfig} */
const nextConfig = {
	reactStrictMode: true,
	// Add output configuration for static files
	output: "standalone",
	// Configure static asset handling
	images: {
		unoptimized: true,
	},
	// Temporarily disable ESLint during build for testing
	eslint: {
		ignoreDuringBuilds: true,
	},
	// Configure API rewrites
	async rewrites() {
		return [
			{
				source: "/api/v1/:path*", // Match any route starting with /api/v1/
				destination: "http://localhost:8000/api/v1/:path*", // Proxy to your backend
			},
			// Add fallback for version.json
			{
				source: "/_app/version.json",
				destination: "/api/version",
			},
		];
	},
};
export default nextConfig;
