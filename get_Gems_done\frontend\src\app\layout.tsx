// src/app/layout.tsx
"use client";

import React from "react";
import { Montserrat } from "next/font/google"; // Using more stylish fonts
import "./globals.css";
import Header from "@/components/layout/Header";
import { LoadingProvider } from "@/contexts/LoadingContext";
// import Footer from "@/components/layout/Footer";
// ThemeProvider removed as we're using light theme only

const font = Montserrat({
	subsets: ["latin"],
	weight: ["300", "400", "500", "600", "700"], // Include weights you'll use
});

// Metadata export removed as it can't be used with "use client" directive
// We'll set the title directly in the head element

export default function RootLayout({
	children,
}: Readonly<{ children: React.ReactNode }>) {
	return (
		<html lang="en">
			<head>
				<meta
					name="description"
					content="Scrape, filter, and manage GeM bids"
				/>
			</head>
			<body
				className={`${font.className} bg-gray-50 text-gray-900 flex flex-col min-h-screen`}
			>
				<LoadingProvider>
					<Header />
					<main className="flex-grow py-6">{children}</main>
					{/* <Footer /> */}
				</LoadingProvider>
			</body>
		</html>
	);
}
