@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--background: #ffffff;
	--foreground: #171717;
}

body {
	background: var(--background);
	color: var(--foreground);
}

.container-bg {
	width: 100%;
	height: 100%;
	--color: #e1e1e1;
	background-color: #f3f3f3;
	background-image: linear-gradient(
			0deg,
			transparent 24%,
			var(--color) 25%,
			var(--color) 26%,
			transparent 27%,
			transparent 74%,
			var(--color) 75%,
			var(--color) 76%,
			transparent 77%,
			transparent
		),
		linear-gradient(
			90deg,
			transparent 24%,
			var(--color) 25%,
			var(--color) 26%,
			transparent 27%,
			transparent 74%,
			var(--color) 75%,
			var(--color) 76%,
			transparent 77%,
			transparent
		);
	background-size: 55px 55px;
}

/* Custom container with better padding */
.container-custom {
	width: 100%;
	max-width: 1280px;
	margin-left: auto;
	margin-right: auto;
	padding: 1.5rem;
}

/* Card styling */
.card {
	background: white;
	border-radius: 0.5rem;
	padding: 1.5rem;
	border: 1px solid #f0f0f0;
}

/* Button styling */
.btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0.75rem 1.5rem;
	font-weight: 500;
	border-radius: 0.375rem;
	transition: all 0.2s;
	cursor: pointer;
}

.btn-primary {
	background-color: #1f2937;
	color: white;
}

.btn-primary:hover {
	background-color: #374151;
}

.btn-secondary {
	background-color: #f3f4f6;
	color: #1f2937;
}

.btn-secondary:hover {
	background-color: #e5e7eb;
}

/* Input styling */
.input {
	width: 100%;
	padding: 0.75rem 1rem;
	border: 1px solid #e5e7eb;
	border-radius: 0.375rem;
	background-color: white;
}

.input:focus {
	outline: none;
	border-color: #9ca3af;
	box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.2);
}

/* Section styling */
.section {
	margin-bottom: 2rem;
}

.section-title {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 1rem;
	color: #111827;
}

/* Custom checkbox styling */
.custom-checkbox {
	position: relative;
	display: flex;
	align-items: center;
	user-select: none;
	font-weight: 500;
	padding: 12px 12px 12px 45px; /* Fixed padding to ensure text doesn't overlap */
	margin-bottom: 8px; /* Added vertical spacing between checkboxes */
	border-radius: 6px;
	transition: background-color 0.2s;
	cursor: pointer;
	width: 100%;
}

.custom-checkbox:hover {
	background-color: #f9fafb;
}

.custom-checkbox input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
	height: 0;
	width: 0;
}

.checkmark {
	position: absolute;
	left: 12px; /* Adjusted position */
	top: 50%;
	transform: translateY(-50%);
	height: 22px; /* Slightly larger checkbox */
	width: 22px;
	background-color: #fff;
	border: 2px solid #d1d5db;
	border-radius: 4px;
	transition: all 0.2s;
}

.custom-checkbox:hover input ~ .checkmark {
	border-color: #9ca3af;
}

.custom-checkbox input:checked ~ .checkmark {
	background-color: #1f2937;
	border-color: #1f2937;
}

.checkmark:after {
	content: "";
	position: absolute;
	display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
	display: block;
}

.custom-checkbox .checkmark:after {
	left: 7px; /* Adjusted for larger checkbox */
	top: 3px;
	width: 6px;
	height: 11px;
	border: solid white;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

.checkbox-label {
	margin-left: 8px;
	white-space: normal;
	overflow: visible;
	text-overflow: clip;
	display: block;
	width: calc(100% - 30px);
}

/* Loading Bar Styles */
.loading-bar-container {
	width: 100%;
	height: 4px;
	background-color: #e5e7eb;
	border-radius: 2px;
	overflow: hidden;
	position: relative;
}

.loading-bar {
	height: 100%;
	background: linear-gradient(90deg, #3b82f6, #1d4ed8);
	border-radius: 2px;
	transition: width 0.3s ease;
	position: relative;
}

.loading-bar.indeterminate {
	width: 30%;
	animation: loading-bar-indeterminate 2s infinite;
}

@keyframes loading-bar-indeterminate {
	0% {
		transform: translateX(-100%);
	}
	50% {
		transform: translateX(0%);
	}
	100% {
		transform: translateX(300%);
	}
}

.loading-bar.pulse {
	animation: loading-bar-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-bar-pulse {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
}

/* Progress Bar Styles */
.progress-bar-container {
	width: 100%;
	background-color: #f3f4f6;
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid #e5e7eb;
}

.progress-bar {
	height: 8px;
	background: linear-gradient(90deg, #10b981, #059669);
	border-radius: 8px;
	transition: width 0.5s ease;
	position: relative;
}

.progress-bar.processing {
	background: linear-gradient(90deg, #f59e0b, #d97706);
}

.progress-bar.error {
	background: linear-gradient(90deg, #ef4444, #dc2626);
}

.progress-bar.completed {
	background: linear-gradient(90deg, #10b981, #059669);
}

/* Loading Overlay Styles */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-spinner {
	width: 40px;
	height: 40px;
	border: 4px solid #e5e7eb;
	border-top: 4px solid #3b82f6;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* Process Log Loading Bar */
.process-log-loading {
	position: sticky;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	border-top: 1px solid #e5e7eb;
	padding: 8px 16px;
	box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

/* Mini Loading Indicators */
.mini-loading {
	display: inline-block;
	width: 16px;
	height: 16px;
	border: 2px solid #e5e7eb;
	border-top: 2px solid #3b82f6;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

/* Status Badge Styles */
.status-badge {
	display: inline-flex;
	align-items: center;
	gap: 6px;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 0.75rem;
	font-weight: 500;
}

.status-badge.pending {
	background-color: #fef3c7;
	color: #92400e;
}

.status-badge.running {
	background-color: #dbeafe;
	color: #1e40af;
}

.status-badge.completed {
	background-color: #d1fae5;
	color: #065f46;
}

.status-badge.failed {
	background-color: #fee2e2;
	color: #991b1b;
}
