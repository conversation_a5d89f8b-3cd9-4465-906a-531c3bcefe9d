// src/components/ui/ProcessLogBar.tsx
"use client";

import React from "react";
import LoadingBar from "./LoadingBar";
import StatusBadge from "./StatusBadge";
import { StopCircle } from "lucide-react";

interface ProcessLogBarProps {
	/** Whether the bar is visible */
	isVisible: boolean;
	/** Current operation message */
	message: string;
	/** Progress percentage (0-100) */
	progress?: number;
	/** Current status */
	status: "pending" | "running" | "completed" | "failed" | "idle";
	/** Additional details */
	details?: string;
	/** Whether to show progress bar */
	showProgress?: boolean;
	/** Whether to show stop button */
	showStopButton?: boolean;
	/** Stop button click handler */
	onStop?: () => void;
	/** Stop button text */
	stopButtonText?: string;
}

const ProcessLogBar: React.FC<ProcessLogBarProps> = ({
	isVisible,
	message,
	progress,
	status,
	details,
	showProgress = true,
	showStopButton = false,
	onStop,
	stopButtonText = "Stop",
}) => {
	if (!isVisible) return null;

	return (
		<div className="process-log-loading">
			<div className="flex items-center justify-between mb-2">
				<div className="flex items-center gap-3">
					<StatusBadge status={status} />
					<span className="text-sm font-medium text-gray-700">{message}</span>
				</div>
				<div className="flex items-center gap-3">
					{details && <span className="text-xs text-gray-500">{details}</span>}
					{showStopButton && onStop && (
						<button
							onClick={onStop}
							className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-md flex items-center text-xs font-medium transition-colors"
						>
							<StopCircle size={14} className="mr-1" />
							{stopButtonText}
						</button>
					)}
				</div>
			</div>
			{showProgress && (
				<LoadingBar
					progress={progress}
					height={3}
					variant={
						status === "completed"
							? "success"
							: status === "failed"
							? "error"
							: status === "running"
							? "primary"
							: "primary"
					}
				/>
			)}
		</div>
	);
};

export default ProcessLogBar;
