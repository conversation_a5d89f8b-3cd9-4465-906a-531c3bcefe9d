# PDF Processor

A comprehensive system for extracting and filtering bid documents based on EMD details and buyer added terms.

## Folder Structure

- **parsing/**: Contains modules for extracting data from PDF documents
  - `bid_document_extractor.py`: Extracts EMD details and bid terms from a single PDF
  - `bid_document_batch.py`: Processes multiple PDFs in parallel

- **filtering/**: Contains modules for filtering documents based on specific criteria
  - `bid_document_filter.py`: Filters documents based on disqualifying criteria

- **main.py**: Main workflow script that orchestrates the parsing and filtering process

## Usage

### Complete Process (Parse and Filter)

To process and filter documents in one step:

```bash
python main.py --input_dir "path/to/pdf/directory" --output_dir "processed_bids"
```

Options:
- `--input_dir`: Directory containing PDF files (required)
- `--output_dir`: Directory to save processed data (default: "processed_bids")
- `--filter_output`: Path to save filtering results (default: "{output_dir}/filtering_results.json")
- `--workers`: Number of parallel workers for extraction (default: 4)

### Parse Only

To only parse documents without filtering:

```bash
python main.py --input_dir "path/to/pdf/directory" --output_dir "parsed_data" --parse_only
```

### Filter Only

To only filter previously parsed documents:

```bash
python main.py --input_dir "path/to/pdf/directory" --output_dir "parsed_data" --filter_only
```

## Output

The processing generates:

1. **Parsed Data**: Extracted EMD details and bid terms from each PDF
2. **Filtering Results**: JSON and TXT reports of accepted and rejected documents
3. **Accepted Documents**: A directory containing copies of all accepted documents

## Requirements

```bash
pip install pymupdf
```

## Customizing Filtering Criteria

To customize the filtering criteria, edit the `filtering/bid_document_filter.py` file:

1. **Disqualifying Patterns**: Patterns that cause a document to be rejected
2. **Whitelist Patterns**: Patterns that should not cause rejection
3. **Disqualifying EMD Patterns**: Patterns in EMD details that cause rejection

## Example Workflow

1. **Collect Bid Documents**:
   - Place all bid PDFs in a directory

2. **Process and Filter**:
   ```bash
   python main.py --input_dir "bids" --output_dir "processed_bids"
   ```

3. **Review Results**:
   - Check `processed_bids/filtering_results.txt` for a summary
   - Access accepted documents in the `processed_bids/accepted` directory
