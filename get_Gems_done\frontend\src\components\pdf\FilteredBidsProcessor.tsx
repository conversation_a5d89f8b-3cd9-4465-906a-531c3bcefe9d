import React, { useState } from "react";
import { Play, StopCircle, Search } from "lucide-react";
import { fetchApi } from "@/lib/api";
import ProgressBar from "@/components/ui/ProgressBar";
import LoadingBar from "@/components/ui/LoadingBar";

interface Bid {
	id: number;
	bid_number: string;
	items: string | null;
	ministry_name: string | null;
	department_name: string | null;
	pdf_analysis_status?: string | null;
}

interface ProcessingProgress {
	current: number;
	total: number;
	stage: string;
	message: string;
	count: number; // accepted count
}

interface BackendTaskInfo {
	task_id: string;
	status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED";
	message: string;
	details?: Record<string, unknown>;
	progress_stage?: string | null;
	progress_current?: number;
	progress_total?: number;
	result_count?: number | null;
	error_message?: string | null;
	result_detail?: Record<string, unknown> | null;
}

interface FilteredBidsProcessorProps {
	filteredBids: Bid[] | null;
	processApiUrl: string; // e.g., /api/v1/bids/process-filtered-bids
	statusApiUrl: string; // For polling the processing task
	onProcessingComplete: (acceptedCount: number, sessionName: string) => void;
	onShowAcceptedFromFiltered: () => void; // New prop for showing accepted bids from filtered results
	isAnyProcessRunning: boolean;
}

const FilteredBidsProcessor: React.FC<FilteredBidsProcessorProps> = ({
	filteredBids,
	processApiUrl,
	statusApiUrl,
	onProcessingComplete,
	onShowAcceptedFromFiltered,
	isAnyProcessRunning,
}) => {
	const [isProcessing, setIsProcessing] = useState(false);
	const [processStatusMessage, setProcessStatusMessage] = useState<
		string | null
	>(null);
	const [processError, setProcessError] = useState<string | null>(null);
	const [activeProcessTaskId, setActiveProcessTaskId] = useState<string | null>(
		null
	);
	const [processProgress, setProcessProgress] =
		useState<ProcessingProgress | null>(null);
	const [showAcceptedButton, setShowAcceptedButton] = useState(false);
	const [lastProcessedAcceptedCount, setLastProcessedAcceptedCount] =
		useState(0);

	const startProcessing = async () => {
		if (!filteredBids || filteredBids.length === 0) {
			setProcessError("No filtered bids to process");
			return;
		}

		// Check how many bids are already processed
		const alreadyProcessed = filteredBids.filter(
			(bid) => bid.pdf_analysis_status && bid.pdf_analysis_status !== "PENDING"
		).length;

		const toProcess = filteredBids.length - alreadyProcessed;

		if (toProcess === 0) {
			setProcessError(`All ${filteredBids.length} bids are already processed`);
			return;
		}

		setIsProcessing(true);
		setProcessError(null);
		setProcessStatusMessage(
			`Initiating PDF processing for ${toProcess} bids (${alreadyProcessed} already processed)...`
		);
		setProcessProgress(null);
		setShowAcceptedButton(false); // Reset button state when starting new processing

		try {
			// Extract bid IDs from filtered bids
			const bidIds = filteredBids.map((bid) => bid.id);
			const sessionName = `filtered_session_${Date.now()}`;

			// Start processing
			const response = await fetchApi<{
				task_id: string;
				message: string;
				session_name: string;
			}>(processApiUrl, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					bid_ids: bidIds,
					session_name: sessionName,
				}),
			});

			if (response?.task_id) {
				setActiveProcessTaskId(response.task_id);
				setProcessStatusMessage(response.message);

				// Start polling for status
				pollProcessingStatus(response.task_id, sessionName);
			} else {
				throw new Error("No task ID received from server");
			}
		} catch (error) {
			console.error("Failed to start PDF processing:", error);
			setProcessError(
				error instanceof Error ? error.message : "Failed to start processing"
			);
			setIsProcessing(false);
		}
	};

	const pollProcessingStatus = async (taskId: string, sessionName: string) => {
		const pollInterval = setInterval(async () => {
			try {
				const statusResponse = await fetchApi<BackendTaskInfo>(
					`${statusApiUrl}/${taskId}`
				);

				if (statusResponse) {
					// Update progress if available
					if (
						statusResponse.progress_current !== undefined &&
						statusResponse.progress_total !== undefined
					) {
						setProcessProgress({
							current: statusResponse.progress_current,
							total: statusResponse.progress_total,
							stage: statusResponse.progress_stage || "Processing",
							message: statusResponse.message,
							count: statusResponse.result_count || 0,
						});
					}

					setProcessStatusMessage(statusResponse.message);

					if (statusResponse.status === "COMPLETED") {
						clearInterval(pollInterval);
						setIsProcessing(false);
						setActiveProcessTaskId(null);
						setProcessProgress(null);

						const acceptedCount = statusResponse.result_count || 0;
						setProcessStatusMessage(
							`Processing completed! ${acceptedCount} bids accepted.`
						);

						// Show the "Show Accepted" button only after processing completes
						if (acceptedCount > 0) {
							setShowAcceptedButton(true);
							setLastProcessedAcceptedCount(acceptedCount);
						}

						// Notify parent component
						onProcessingComplete(acceptedCount, sessionName);
					} else if (statusResponse.status === "FAILED") {
						clearInterval(pollInterval);
						setIsProcessing(false);
						setActiveProcessTaskId(null);
						setProcessProgress(null);
						setProcessError(
							statusResponse.error_message || "Processing failed"
						);
					}
				}
			} catch (error) {
				console.error("Error polling processing status:", error);
				// Continue polling unless it's a critical error
			}
		}, 3000); // Poll every 3 seconds

		// Cleanup interval after 10 minutes (safety)
		setTimeout(() => {
			clearInterval(pollInterval);
			if (isProcessing) {
				setProcessError("Processing timeout - please check status manually");
				setIsProcessing(false);
				setActiveProcessTaskId(null);
			}
		}, 600000);
	};

	const handleStopProcessing = async () => {
		if (activeProcessTaskId) {
			try {
				await fetchApi(`/api/v1/bids/task/${activeProcessTaskId}/cancel`, {
					method: "POST",
				});
				setProcessStatusMessage("Processing stopped by user");
			} catch (error) {
				console.error("Failed to stop processing:", error);
				setProcessError("Failed to stop processing");
			}
		}

		setIsProcessing(false);
		setActiveProcessTaskId(null);
		setProcessProgress(null);
	};

	const canProcess =
		filteredBids && filteredBids.length > 0 && !isAnyProcessRunning;

	// Calculate processing stats
	const alreadyProcessedCount = filteredBids
		? filteredBids.filter(
				(bid) =>
					bid.pdf_analysis_status && bid.pdf_analysis_status !== "PENDING"
		  ).length
		: 0;

	const toProcessCount = filteredBids
		? filteredBids.length - alreadyProcessedCount
		: 0;

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<div>
					<h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
						PDF Processing
					</h4>
					<p className="text-xs text-gray-500 dark:text-gray-400">
						{filteredBids
							? toProcessCount > 0
								? `Process ${toProcessCount} bid(s) (${alreadyProcessedCount} already processed)`
								: alreadyProcessedCount > 0
								? `All ${filteredBids.length} bid(s) already processed`
								: `Process ${filteredBids.length} filtered bid(s)`
							: "No filtered bids to process"}
					</p>
				</div>

				<div className="flex gap-2">
					{!isProcessing ? (
						<>
							{toProcessCount > 0 && (
								<button
									onClick={startProcessing}
									disabled={!canProcess}
									className={`btn btn-primary flex items-center gap-2 ${
										!canProcess
											? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
											: "bg-blue-600 hover:bg-blue-700"
									}`}
									title={
										!filteredBids || filteredBids.length === 0
											? "No filtered bids to process"
											: isAnyProcessRunning
											? "Another process is currently running"
											: `Process ${toProcessCount} filtered bid(s)`
									}
								>
									<Play size={16} />
									Process Filtered Bids
								</button>
							)}

							{showAcceptedButton && (
								<button
									onClick={() => {
										onShowAcceptedFromFiltered();
										setShowAcceptedButton(false); // Hide button after clicking
									}}
									disabled={isAnyProcessRunning}
									className="btn btn-secondary flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white"
									title={`Show ${lastProcessedAcceptedCount} accepted bids from recent processing`}
								>
									<Search size={16} />
									Show {lastProcessedAcceptedCount} Accepted Bids
								</button>
							)}
						</>
					) : (
						<button
							onClick={handleStopProcessing}
							className="btn btn-danger flex items-center gap-2 bg-red-600 hover:bg-red-700"
						>
							<StopCircle size={16} />
							Stop Processing
						</button>
					)}
				</div>
			</div>

			{/* Status and Progress */}
			{(isProcessing || processStatusMessage || processError) && (
				<div className="space-y-2">
					<div className="flex items-center gap-2 text-sm">
						{isProcessing && (
							<div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
						)}
						<span className={processError ? "text-red-600" : "text-gray-700"}>
							{processError || processStatusMessage}
						</span>
					</div>

					{isProcessing && processProgress && (
						<ProgressBar
							current={processProgress.current}
							total={processProgress.total}
							height={6}
							variant="processing"
							label={`${processProgress.stage}: ${processProgress.current}/${processProgress.total} (Accepted: ${processProgress.count})`}
							showDetailed={true}
						/>
					)}

					{isProcessing && !processProgress && (
						<LoadingBar
							height={3}
							variant={processError ? "error" : "primary"}
							pulse={true}
						/>
					)}
				</div>
			)}
		</div>
	);
};

export default FilteredBidsProcessor;
