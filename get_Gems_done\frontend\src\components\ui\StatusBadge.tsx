// src/components/ui/StatusBadge.tsx
"use client";

import React from "react";

interface StatusBadgeProps {
	/** Status type */
	status: "pending" | "running" | "completed" | "failed" | "idle";
	/** Custom text to display */
	text?: string;
	/** Whether to show loading indicator for running status */
	showLoader?: boolean;
	/** Custom className */
	className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
	status,
	text,
	showLoader = true,
	className = "",
}) => {
	const getStatusText = () => {
		if (text) return text;
		switch (status) {
			case "pending":
				return "Pending";
			case "running":
				return "Running";
			case "completed":
				return "Completed";
			case "failed":
				return "Failed";
			case "idle":
				return "Idle";
			default:
				return "Unknown";
		}
	};

	const getStatusClasses = () => {
		switch (status) {
			case "pending":
				return "status-badge pending";
			case "running":
				return "status-badge running";
			case "completed":
				return "status-badge completed";
			case "failed":
				return "status-badge failed";
			case "idle":
				return "status-badge pending";
			default:
				return "status-badge";
		}
	};

	return (
		<span className={`${getStatusClasses()} ${className}`}>
			{status === "running" && showLoader && (
				<span className="mini-loading" />
			)}
			{getStatusText()}
		</span>
	);
};

export default StatusBadge;
