# app/config/criteria_config.py
"""
Centralized PDF Processing Criteria Configuration

This file contains all the criteria used for accepting or rejecting bid PDFs.
Modify these patterns to adjust the acceptance/rejection logic.
"""

from dataclasses import dataclass
from typing import List
import re

@dataclass
class BidCriteria:
    """Configuration for bid document analysis criteria"""
    emd_required: bool = True
    emd_zero_acceptable: bool = False
    check_facility_requirements: bool = True

# =============================================================================
# REJECTION CRITERIA - PDFs will be REJECTED if these patterns are found
# =============================================================================

# Organization-Specific Registration Requirements (VERY RESTRICTIVE)
ORGANIZATION_RESTRICTION_PATTERNS = [
    # Exact phrases that restrict participation to specific organizations
    r'seller/bidder\s+should\s+be\s+established\s+&\s+registered\s+vendors\s+for',
    r'(established|registered)\s+vendors?\s+(for|with)\s+.{0,50}(mpf|ofb|avnl|machine\s+tool)',
    r'only\s+(established|registered)\s+vendors\s+.{0,50}(mpf|ofb|avnl)',
    r'registered\s+with\s+(mpf|ofb|avnl|machine\s+tool)',
    r'vendors?\s+(should|must)\s+be\s+(established|registered)\s+.{0,50}(mpf|ofb|avnl)',
    r'(only|existing)\s+registered\s+vendors\s+.{0,50}(mpf|ofb|avnl)',
    r'sister\s+factories\s+of\s+(avnl|ofb)',
]

# Mandatory In-House Facility Requirements (VERY RESTRICTIVE)
FACILITY_RESTRICTION_PATTERNS = [
    # Exact phrases that mandate specific in-house facilities
    r'in\s*house\s+(forging|casting|heat\s+treatment)\s+facility\s+is\s+(mandatory|required|essential)',
    r'(mandatory|required|essential)\s+in\s*house\s+(forging|casting|heat\s+treatment)',
    r'(must|should)\s+have\s+in\s*house\s+(forging|casting|heat\s+treatment)',
    r'in\s+house\s+facility\s+for\s+forging\s+is\s+mandatory',
    r'in\s+house\s+facility\s+for\s+casting\s+is\s+mandatory',
    r'in\s+house\s+facility\s+for\s+heat\s+treatment\s+is\s+mandatory',
]

# Removed experience and certification requirements as requested

# EMD (Earnest Money Deposit) Issues
EMD_RESTRICTION_PATTERNS = [
    # Problematic EMD requirements
    r'emd\s+exemption\s+not\s+allowed',
    r'section\s+wise\s+emd\s+(mandatory|required)',
    r'emd\s+required\s+for\s+selected\s+sections?\s+only',
    r'different\s+emd\s+for\s+different\s+sections?',

    # EMD not required / No EMD patterns (tabular and text formats)
    r'emd\s+(detail\s+)?(requirement|required)\s*:?\s*(no|not\s+required|nil|zero|0)',
    r'required\s*/?\s*आवश्यकता\s*:?\s*(no|नहीं)',
    r'earnest\s+money\s+(deposit\s+)?(required\s*:?\s*)?(no|not\s+required|nil)',
    r'emd\s+(amount\s*:?\s*)?(nil|zero|0|not\s+applicable|na|n/a)',
    r'emd\s+(status\s*:?\s*)?(not\s+required|no|nil)',
    r'(emd|earnest\s+money)\s+(is\s+)?(not\s+required|exempted|waived)',

    # Zero EMD amount patterns
    r'emd\s+(amount\s*:?\s*)?rs\.?\s*0',
    r'emd\s+(amount\s*:?\s*)?₹\s*0',
    r'emd\s+(amount\s*:?\s*)?inr\s*0',

    # Table format patterns (common in government tenders) - Multi-line support
    r'emd\s+detail\s*/?\s*ईएमड\s+ववरण\s*[\s\n]*required\s*/?\s*आवश्यकता\s*[\s\n]*no',
    r'emd\s+detail\s*[\s\n]*required\s*/?\s*आवश्यकता\s*[\s\n]*no',
    r'earnest\s+money\s+deposit\s*[\s\n]*required\s*:?\s*no',

    # Multi-line tabular format (lines can be separated)
    r'emd\s+detail[^\n]*\n[^\n]*required[^\n]*\n[^\n]*no',
    r'ईएमड\s+ववरण[^\n]*\n[^\n]*आवश्यकता[^\n]*\n[^\n]*no',

    # Learned pattern (accuracy: 0.951)
    r'registered\s+vendors\s+for\s+the\s+same\s+range\s+of\s+products/technology\s+in\s+sister\s+fact\
ories\s+of\s+avnl/ofb',
    # Learned pattern (accuracy: 0.951)
    r'established\s+\&\s+registered\s+vendors\s+at\s+mpf',
    # Learned pattern (accuracy: 0.951)
    r'registered\s+sources\s+of\s+forging\s+items\s+at\s+mpf',
    # Learned pattern (accuracy: 0.927)
    r'in\s+house\s+facility\s+for\s+forging\s+is\s+mandatory',
    # Learned pattern (accuracy: 0.927)
    r'forging\s+is\s+mandatory',
    # Learned pattern (accuracy: 0.927)
    r'facility\s+for\s+forging\s+is\s+mandatory',
    # Learned pattern (accuracy: 0.951)
    r'established\s+\&\s+registered\s+vendors\s+for\s+the\s+same\s+range\s+of\s+prod\
ucts/technology\s+in\s+machine\s+tool',
    # Learned pattern (accuracy: 0.951)
    r'bidder\s+should\s+be\s+established\s+\&\s+registered\s+vendors',
    # Learned pattern (accuracy: 0.951)
    r'sister\s+factories\s+of\s+a\
vnl/ofb',

    # Learned pattern (accuracy: 0.902)
    r'in\s+house\s+facility\s+for\s+forging\s+is\s+mandatory',
    # Learned pattern (accuracy: 0.902)
    r'forging\s+is\s+mandatory',
    # Learned pattern (accuracy: 0.902)
    r'facility\s+for\s+forging\s+is\s+mandatory',
    # Learned pattern (accuracy: 0.878)
    r'registered\s+vendors\s+for\s+the\s+same\s+range\s+of\s+products/technology\s+in\s+sister\s+fact\
ories\s+of\s+avnl/ofb',
    # Learned pattern (accuracy: 0.878)
    r'established\s+\&\s+registered\s+vendors\s+at\s+mpf',
    # Learned pattern (accuracy: 0.878)
    r'registered\s+sources\s+of\s+forging\s+items\s+at\s+mpf',
    # Learned pattern (accuracy: 0.878)
    r'established\s+\&\s+registered\s+vendors\s+for\s+the\s+same\s+range\s+of\s+prod\
ucts/technology\s+in\s+machine\s+tool',
    # Learned pattern (accuracy: 0.878)
    r'bidder\s+should\s+be\s+established\s+\&\s+registered\s+vendors',
    # Learned pattern (accuracy: 0.878)
    r'sister\s+factories\s+of\s+a\
vnl/ofb',
    # Learned pattern (accuracy: 0.854)
    r'emd\s+detail/ईएमड\s+ववरण\
required/आवlयकता\
no',
    # Learned pattern (accuracy: 0.854)
    r'emd\s+detail/ईएमड\s+ववरण\
required/आवmयकता\
no',
    # Learned pattern (accuracy: 0.854)
    r'emd\s+detail/ईएमड\s+ववरण\
required/आवkयकता\
no',
]

# =============================================================================
# ACCEPTANCE CRITERIA - These patterns are ACCEPTABLE and should NOT cause rejection
# =============================================================================

# Standard Government Procurement Terms (ALWAYS ACCEPTABLE)
ACCEPTABLE_STANDARD_PATTERNS = [
    # Document submission requirements
    r'submit\s+(copy\s+of\s+)?(pan\s+card|gst\s+registration|certificates?)',
    r'provide\s+(technical\s+specifications|product\s+catalogues)',
    r'bidder\s+shall\s+submit\s+.{0,50}(documents?|certificates?)',
    
    # Basic manufacturer requirements
    r'bidder\s+should\s+be\s+manufacturer\s+\(not\s+trader\)',
    r'offers?\s+from\s+traders?\s*\/?\s*stockiest\s+shall\s+not',
    r'traders?\s+and\s+stockiest\s+are\s+not\s+allowed',
    
    # Standard quality and compliance
    r'quality\s+requirements\s+as\s+per\s+industry\s+standards?',
    r'compliance\s+with\s+laws\s+and\s+regulations?',
    r'environmental\s+and\s+safety\s+requirements?',
    
    # Standard commercial terms
    r'gst\s+reimbursement\s+will\s+be\s+as\s+per\s+actuals?',
    r'payment\s+terms?\s+and\s+conditions?',
    r'delivery\s+schedule\s+requirements?',
    r'warranty\s+and\s+service\s+terms?',
    
    # Government preferences (acceptable)
    r'(msme|startup|women\s+entrepreneur)\s+preferences?',
    r'make\s+in\s+india\s+requirements?',
    r'local\s+content\s+requirements?',

    # Learned pattern (accuracy: 0.878)
]

# Informational Mentions (NOT RESTRICTIVE)
INFORMATIONAL_PATTERNS = [
    # Organization names mentioned for information, not restriction
    r'(mpf|ofb|avnl|hvf)\s+specifications?\s+to\s+be\s+followed',
    r'as\s+per\s+(mpf|ofb|avnl|hvf)\s+standards?',
    r'this\s+tender\s+is\s+for\s+(mpf|ofb|avnl|hvf)\s+requirements?',
    r'(iso|nabl|dgqa)\s+(accredited\s+labs?\s+)?preferred',
    r'(iso|nabl|dgqa)\s+approval\s+may\s+be\s+required',
    r'submit\s+(iso|nabl|dgqa)\s+certification\s+if\s+available',
]

# =============================================================================
# CRITERIA CONFIGURATION
# =============================================================================

# Single criteria configuration (strict approach)
DEFAULT_CRITERIA = BidCriteria(
    emd_required=True,
    emd_zero_acceptable=False,
    check_facility_requirements=True
)

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_all_restriction_patterns():
    """Get all restriction patterns combined"""
    return ORGANIZATION_RESTRICTION_PATTERNS + FACILITY_RESTRICTION_PATTERNS + EMD_RESTRICTION_PATTERNS

def get_all_acceptable_patterns():
    """Get all acceptable patterns combined"""
    return ACCEPTABLE_STANDARD_PATTERNS + INFORMATIONAL_PATTERNS

def is_restrictive_clause(text: str, criteria: BidCriteria = None) -> tuple[bool, str]:
    """
    Check if a text contains restrictive clauses.

    Returns:
        tuple: (is_restrictive, reason)
    """
    text_lower = text.lower()

    # First check if it matches acceptable patterns (whitelist)
    for pattern in get_all_acceptable_patterns():
        if re.search(pattern, text_lower, re.IGNORECASE):
            return False, "Matches acceptable pattern"

    # Check for restrictive patterns
    patterns_to_check = get_all_restriction_patterns()

    # Check for restrictive patterns
    for pattern in patterns_to_check:
        if re.search(pattern, text_lower, re.IGNORECASE):
            return True, f"Matches restrictive pattern: {pattern}"

    return False, "No restrictive patterns found"

# =============================================================================
# CONFIGURATION SUMMARY
# =============================================================================

def print_criteria_summary(criteria: BidCriteria = DEFAULT_CRITERIA):
    """Print a summary of the current criteria configuration"""
    print("=" * 60)
    print("PDF PROCESSING CRITERIA CONFIGURATION")
    print("=" * 60)
    print(f"EMD Required: {criteria.emd_required}")
    print(f"EMD Zero Acceptable: {criteria.emd_zero_acceptable}")
    print(f"Check Facility Requirements: {criteria.check_facility_requirements}")
    print()
    print("REJECTION PATTERNS:")
    print(f"- Organization Restrictions: {len(ORGANIZATION_RESTRICTION_PATTERNS)} patterns")
    print(f"- Facility Requirements: {len(FACILITY_RESTRICTION_PATTERNS)} patterns")
    print(f"- EMD Issues: {len(EMD_RESTRICTION_PATTERNS)} patterns")
    print()
    print("ACCEPTANCE PATTERNS:")
    print(f"- Standard Terms: {len(ACCEPTABLE_STANDARD_PATTERNS)} patterns")
    print(f"- Informational Mentions: {len(INFORMATIONAL_PATTERNS)} patterns")
    print("=" * 60)

if __name__ == "__main__":
    # Print default criteria summary
    print_criteria_summary(DEFAULT_CRITERIA)
