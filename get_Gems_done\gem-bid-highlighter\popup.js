class GEMBidHighlighter {
    constructor() {
        this.bidData = new Map();
        this.exportType = null;
        this.currentTab = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStoredData();
        this.checkCurrentTab();
        this.updateUI();
    }

    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e.target.files[0]));

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            if (e.dataTransfer.files.length > 0) {
                this.handleFileUpload(e.dataTransfer.files[0]);
            }
        });

        // Clear file
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearData();
        });

        // Highlight button
        document.getElementById('highlightBtn').addEventListener('click', () => {
            this.highlightCurrentPage();
        });

        // Clear highlights
        document.getElementById('clearHighlights').addEventListener('click', () => {
            this.clearHighlights();
        });
    }

    async handleFileUpload(file) {
        if (!file) return;

        if (!file.name.match(/\.(xlsx|xls)$/i)) {
            this.showMessage('Please select a valid Excel file (.xlsx, .xls)', 'error');
            return;
        }

        this.showMessage('Processing Excel file...', 'info');

        try {
            const data = await this.readExcelFile(file);
            this.processExcelData(data, file.name);
            
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileInfo').style.display = 'flex';
            document.getElementById('uploadArea').style.display = 'none';
            
            this.updateUI();
            this.enableAutoHighlighting();
            this.showMessage(`Successfully loaded ${this.bidData.size} bids`, 'success');
        } catch (error) {
            console.error('Error processing file:', error);
            this.showMessage(`Error: ${error.message}`, 'error');
        }
    }

    readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    resolve(jsonData);
                } catch (error) {
                    reject(new Error('Failed to read Excel file. Please ensure it\'s a valid Excel file.'));
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    processExcelData(data, fileName) {
        if (!data || data.length < 2) {
            throw new Error('Excel file appears to be empty or has no data rows');
        }

        const headers = data[0].map(h => String(h || '').trim());
        console.log('Headers found:', headers);

        // Find required columns
        const bidNumberIndex = this.findColumnIndex(headers, ['bid number', 'bidnumber', 'bid_number']);
        const itemsIndex = this.findColumnIndex(headers, ['items', 'item', 'description']);
        const ministryIndex = this.findColumnIndex(headers, ['ministry', 'department', 'organization']);
        const pdfStatusIndex = this.findColumnIndex(headers, ['pdf status', 'pdf_status', 'pdfstatus', 'status']);

        if (bidNumberIndex === -1) {
            throw new Error(`Bid Number column not found. Available columns: ${headers.join(', ')}`);
        }

        // Determine export type
        this.exportType = pdfStatusIndex !== -1 ? 'pdf_processed' : 'filtered';
        console.log('Detected export type:', this.exportType);

        this.bidData.clear();

        for (let i = 1; i < data.length; i++) {
            const row = data[i];
            if (!row || row.length < headers.length) continue;

            const bidNumber = String(row[bidNumberIndex] || '').trim();
            if (!bidNumber) continue;

            const items = itemsIndex !== -1 ? String(row[itemsIndex] || '') : '';
            const ministry = ministryIndex !== -1 ? String(row[ministryIndex] || '') : '';
            
            let status = 'PRESENT'; // Default for filtered export
            if (this.exportType === 'pdf_processed' && pdfStatusIndex !== -1) {
                status = String(row[pdfStatusIndex] || 'PENDING').toUpperCase();
            }

            this.bidData.set(bidNumber, {
                bidNumber,
                items,
                ministry,
                status,
                exportType: this.exportType
            });
        }

        this.saveData();
    }

    findColumnIndex(headers, possibleNames) {
        for (const name of possibleNames) {
            const index = headers.findIndex(h => h.toLowerCase().includes(name.toLowerCase()));
            if (index !== -1) return index;
        }
        return -1;
    }

    async saveData() {
        const data = {
            bidData: Array.from(this.bidData.entries()),
            exportType: this.exportType,
            lastUpdated: new Date().toISOString()
        };
        
        await chrome.storage.local.set({ gemBidData: data });
    }

    async loadStoredData() {
        try {
            const result = await chrome.storage.local.get(['gemBidData']);
            if (result.gemBidData && result.gemBidData.bidData) {
                this.bidData = new Map(result.gemBidData.bidData);
                this.exportType = result.gemBidData.exportType || 'filtered';

                if (this.bidData.size > 0) {
                    document.getElementById('fileName').textContent = `Stored data (${this.bidData.size} bids)`;
                    document.getElementById('fileInfo').style.display = 'flex';
                    document.getElementById('uploadArea').style.display = 'none';
                    this.enableAutoHighlighting();
                }
            }
        } catch (error) {
            console.error('Error loading stored data:', error);
        }
    }

    async checkCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('Error checking current tab:', error);
        }
    }

    async enableAutoHighlighting() {
        if (this.bidData.size === 0) return;

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab && tab.url && tab.url.includes('gem.gov.in')) {
                await chrome.tabs.sendMessage(tab.id, {
                    action: 'enableAutoHighlight',
                    bidData: Array.from(this.bidData.entries()),
                    exportType: this.exportType
                });
                console.log('Auto-highlighting enabled');
            }
        } catch (error) {
            console.log('Could not enable auto-highlighting:', error.message);
        }
    }

    async highlightCurrentPage() {
        if (!this.currentTab || !this.currentTab.url.includes('gem.gov.in')) {
            this.showMessage('Please navigate to a GEM website page first', 'error');
            return;
        }

        if (this.bidData.size === 0) {
            this.showMessage('Please upload an Excel file first', 'error');
            return;
        }

        try {
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'highlightBids',
                bidData: Array.from(this.bidData.entries()),
                exportType: this.exportType
            });
            this.showMessage('Highlighting bids on current page...', 'success');
        } catch (error) {
            console.error('Error highlighting bids:', error);
            this.showMessage('Error highlighting bids. Make sure you are on a GEM page.', 'error');
        }
    }

    async clearHighlights() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.sendMessage(tab.id, { action: 'clearHighlights' });
            this.showMessage('Cleared all highlights', 'info');
        } catch (error) {
            console.error('Error clearing highlights:', error);
        }
    }

    clearData() {
        this.bidData.clear();
        this.exportType = null;
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').style.display = 'none';
        document.getElementById('uploadArea').style.display = 'block';
        this.updateUI();
        this.saveData();
        this.showMessage('Data cleared', 'info');
    }

    updateUI() {
        const hasData = this.bidData.size > 0;
        const isOnGemSite = this.currentTab && this.currentTab.url && this.currentTab.url.includes('gem.gov.in');

        // Update export type
        if (hasData) {
            document.getElementById('exportType').style.display = 'block';
            const typeBadge = document.getElementById('typeBadge');
            const typeDescription = document.getElementById('typeDescription');
            
            if (this.exportType === 'pdf_processed') {
                typeBadge.textContent = 'PDF Processed Export';
                typeBadge.className = 'type-badge pdf-processed';
                typeDescription.textContent = 'Contains bids with PDF processing status (Accepted/Rejected/Pending)';
            } else {
                typeBadge.textContent = 'Filtered Bids Export';
                typeBadge.className = 'type-badge filtered';
                typeDescription.textContent = 'Contains only filtered bids that match your criteria';
            }
        } else {
            document.getElementById('exportType').style.display = 'none';
        }

        // Update statistics
        if (hasData) {
            this.updateStatistics();
            document.getElementById('stats').style.display = 'block';
        } else {
            document.getElementById('stats').style.display = 'none';
        }

        // Update status
        const pageStatus = document.getElementById('pageStatus');
        const highlightStatus = document.getElementById('highlightStatus');
        
        if (isOnGemSite) {
            pageStatus.textContent = 'GEM Website ✅';
            pageStatus.className = 'status-value gem-site';
        } else {
            pageStatus.textContent = 'Not on GEM ❌';
            pageStatus.className = 'status-value not-gem-site';
        }

        if (hasData) {
            highlightStatus.textContent = 'Auto-Enabled ✅';
            highlightStatus.className = 'status-value active';
        } else {
            highlightStatus.textContent = 'No Data ❌';
            highlightStatus.className = 'status-value inactive';
        }

        // Update buttons
        document.getElementById('highlightBtn').disabled = !hasData || !isOnGemSite;
    }

    updateStatistics() {
        const counts = this.calculateCounts();
        
        document.getElementById('totalBids').textContent = counts.total;
        
        if (this.exportType === 'pdf_processed') {
            document.getElementById('acceptedStat').style.display = 'flex';
            document.getElementById('rejectedStat').style.display = 'flex';
            document.getElementById('pendingStat').style.display = 'flex';
            
            document.getElementById('acceptedBids').textContent = counts.accepted;
            document.getElementById('rejectedBids').textContent = counts.rejected;
            document.getElementById('pendingBids').textContent = counts.pending;
        } else {
            document.getElementById('acceptedStat').style.display = 'none';
            document.getElementById('rejectedStat').style.display = 'none';
            document.getElementById('pendingStat').style.display = 'none';
        }
    }

    calculateCounts() {
        let accepted = 0, rejected = 0, pending = 0;
        
        for (const bid of this.bidData.values()) {
            switch (bid.status) {
                case 'ACCEPTED':
                    accepted++;
                    break;
                case 'REJECTED':
                    rejected++;
                    break;
                case 'PENDING':
                    pending++;
                    break;
            }
        }
        
        return {
            total: this.bidData.size,
            accepted,
            rejected,
            pending
        };
    }

    showMessage(text, type = 'info') {
        const messagesContainer = document.getElementById('messages');
        const message = document.createElement('div');
        message.className = `message ${type}`;
        message.textContent = text;
        
        messagesContainer.appendChild(message);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 5000);
        
        // Keep only last 3 messages
        const messages = messagesContainer.querySelectorAll('.message');
        if (messages.length > 3) {
            messagesContainer.removeChild(messages[0]);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GEMBidHighlighter();
});
