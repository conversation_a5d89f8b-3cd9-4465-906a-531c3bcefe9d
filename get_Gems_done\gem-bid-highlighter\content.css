/* GEM Bid Highlighter v2.0 - Content Styles */

/* Base highlighting styles */
.gem-bid-highlighted {
    position: relative !important;
    z-index: 10 !important;
    transition: all 0.3s ease !important;
}

/* Hover effects for highlighted bids */
.gem-bid-highlighted:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Status badge styles */
.gem-status-badge {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    white-space: nowrap !important;
    user-select: none !important;
}

/* Animation for newly highlighted bids */
@keyframes gemHighlightPulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    50% { 
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

@keyframes gemHighlightPulseRed {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% { 
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

@keyframes gemHighlightPulseYellow {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    }
    50% { 
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
    }
}

/* Apply animations based on status */
.gem-bid-highlighted[data-gem-highlight-type="in-excel"] {
    animation: gemHighlightPulse 0.8s ease-in-out !important;
}

.gem-bid-highlighted[data-gem-highlight-type="not-in-excel"] {
    animation: gemHighlightPulseYellow 0.8s ease-in-out !important;
}

/* Status panel styles */
.gem-status-panel {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* Ensure highlighted elements are visible above other content */
.gem-bid-highlighted {
    isolation: isolate !important;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .gem-status-panel > div {
        position: fixed !important;
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        width: auto !important;
        min-width: auto !important;
        max-width: none !important;
    }
    
    .gem-status-badge {
        font-size: 9px !important;
        padding: 1px 4px !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .gem-status-panel > div {
        background: #1a1a1a !important;
        border-color: #333 !important;
        color: #fff !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gem-bid-highlighted {
        border-width: 3px !important;
    }
    
    .gem-status-badge {
        border: 1px solid #000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .gem-bid-highlighted {
        animation: none !important;
        transition: none !important;
    }
    
    .gem-bid-highlighted:hover {
        transform: none !important;
    }
}

/* Print styles - hide highlights when printing */
@media print {
    .gem-bid-highlighted {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }
    
    .gem-bid-indicator,
    .gem-status-panel {
        display: none !important;
    }
}

/* Focus styles for accessibility */
.gem-status-badge:focus {
    outline: 2px solid #0066cc !important;
    outline-offset: 2px !important;
}

/* Ensure text remains readable */
.gem-bid-highlighted * {
    color: inherit !important;
}

/* Prevent layout shifts */
.gem-bid-highlighted {
    box-sizing: border-box !important;
}

/* Smooth transitions for status changes */
.gem-bid-indicator {
    transition: all 0.2s ease !important;
}

/* Loading state for dynamic content */
.gem-loading {
    position: relative !important;
    overflow: hidden !important;
}

.gem-loading::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent) !important;
    animation: gemLoading 1.5s infinite !important;
}

@keyframes gemLoading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Ensure proper stacking context */
.gem-bid-highlighted {
    contain: layout style !important;
}
