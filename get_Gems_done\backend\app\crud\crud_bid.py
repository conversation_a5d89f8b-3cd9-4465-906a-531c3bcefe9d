# app/crud/crud_bid.py
import json
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date, timezone
from app.db.models import PDFAnalysisStatusEnum, ScrapeSourceTypeEnum

from app.db import models  # Corrected import path if models.py is in app/db/
from app.schemas import bid_schemas # Corrected import path if bid_schemas.py is in app/schemas/


def get_bid_by_internal_id(db: Session, bid_id_internal: str) -> Optional[models.Bid]:
    """Retrieves a single bid by its internal GeM ID."""
    return db.query(models.Bid).filter(models.Bid.bid_id_internal == bid_id_internal).first()

def get_bid_by_bid_number(db: Session, bid_number: str) -> Optional[models.Bid]:
    """
    Retrieves a single bid from the database by its bid_number.
    Returns the Bid model instance if found, else None.
    """
    return db.query(models.Bid).filter(models.Bid.bid_number == bid_number).first()

def get_bid_by_id(db: Session, bid_id: int) -> Optional[models.Bid]:
    """
    Retrieves a single bid from the database by its primary key ID.
    Returns the Bid model instance if found, else None.
    """
    return db.query(models.Bid).filter(models.Bid.id == bid_id).first()

def _create_db_bid_from_schema(bid_schema: bid_schemas.BidCreate) -> models.Bid:
    """Helper to map Pydantic schema to SQLAlchemy model, converting types."""
    return models.Bid(
        bid_id_internal=bid_schema.bid_id_internal,
        bid_number=bid_schema.bid_number,
        items=bid_schema.items,
        quantity=bid_schema.quantity,
        ministry_name=bid_schema.ministry_name,
        department_name=bid_schema.department_name,
        start_date=bid_schema.start_date,
        end_date=bid_schema.end_date,
        # --- Convert HttpUrl to string here ---
        bid_details_url=str(bid_schema.bid_details_url) if bid_schema.bid_details_url else None,
        # New scraping session tracking fields
        scrape_session_id=bid_schema.scrape_session_id,
        scrape_source_type=bid_schema.scrape_source_type,
        scrape_source_details=bid_schema.scrape_source_details
        # raw_api_doc would be bid_schema.raw_api_doc if you were keeping it
    )

def create_bid(db: Session, bid: bid_schemas.BidCreate) -> models.Bid:
    """Creates a new bid in the database."""
    db_bid = _create_db_bid_from_schema(bid) # Use helper function
    db.add(db_bid)
    db.commit()
    db.refresh(db_bid)
    return db_bid

def bulk_create_bids(db: Session, bids_to_create: List[bid_schemas.BidCreate]) -> List[models.Bid]:
    """Creates multiple bids, skipping duplicates, converting types correctly."""
    newly_created_bids_models = []
    created_count = 0
    skipped_count = 0

    bid_numbers_in_batch = {b.bid_number for b in bids_to_create}
    # Fetch existing bids for this batch in one query for efficiency
    existing_bids = db.query(models.Bid.bid_number).filter(models.Bid.bid_number.in_(bid_numbers_in_batch)).all()
    existing_bid_numbers = {row.bid_number for row in existing_bids}

    for bid_in in bids_to_create:
        if bid_in.bid_number in existing_bid_numbers:
            skipped_count += 1
            continue # Skip creating a new one

        # Create new bid using the helper for type conversion
        db_bid = _create_db_bid_from_schema(bid_in)
        db.add(db_bid)
        newly_created_bids_models.append(db_bid)
        created_count +=1
        # Add the newly checked bid number to prevent duplicate adds *within the same batch*
        # though the initial query largely handles this.
        existing_bid_numbers.add(bid_in.bid_number)

    if newly_created_bids_models:
        try:
            # We add all objects first, then commit once.
            # The db.add() stages them. commit writes them.
            # No need to call commit inside the loop.
            db.commit()
            for db_bid in newly_created_bids_models:
                db.refresh(db_bid)
        except Exception as e:
            print(f"Error during bulk commit: {e}")
            db.rollback() # Rollback transaction on error
            raise # Re-raise the exception to be caught by background task handler

    print(f"Bulk Create: Added {created_count} new bids. Skipped {skipped_count} duplicates.")
    return newly_created_bids_models


def get_bids(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    ministry: Optional[str] = None,
    organization: Optional[str] = None,
    pdf_status: Optional[PDFAnalysisStatusEnum] = None,
    pdf_processing_session: Optional[str] = None
    # Add other filter parameters as needed (e.g., city, date range)
) -> List[models.Bid]:
    """
    Retrieves a list of bids from the database with optional filters and pagination.
    """
    query = db.query(models.Bid)

    if ministry:
        # Using 'ilike' for case-insensitive partial match
        query = query.filter(models.Bid.ministry_name.ilike(f"%{ministry}%"))

    if organization:
        query = query.filter(models.Bid.organization_name.ilike(f"%{organization}%"))
        # Note: Your Bid model doesn't have organization_name directly.
        # It has department_name. If organization is part of department_name or ministry_name,
        # or if you add an organization_name field to your Bid model, adjust accordingly.
        # For now, I'll assume you might add it or filter by department.
        # query = query.filter(models.Bid.department_name.ilike(f"%{organization}%")) # Example

    if pdf_status: # <<<< ADDED THIS BLOCK
        query = query.filter(models.Bid.pdf_analysis_status == pdf_status)

    if pdf_processing_session:
        query = query.filter(models.Bid.pdf_processing_session == pdf_processing_session)

        return query.order_by(models.Bid.end_date.desc().nulls_last(), models.Bid.id.desc()).offset(skip).limit(limit).all()


    return query.order_by(models.Bid.scraped_at.desc(), models.Bid.id.desc()).offset(skip).limit(limit).all()

def get_bid_by_internal_id(db: Session, bid_id_internal: str) -> Optional[models.Bid]:
    """
    Retrieves a single bid from the database by its internal GeM ID (bid_id_internal).
    Returns the Bid model instance if found, else None.
    """
    # Ensure bid_id_internal field is indexed in your model for performance
    return db.query(models.Bid).filter(models.Bid.bid_id_internal == bid_id_internal).first()

def delete_expired_bids(db: Session) -> int:
    """
    Deletes bids from the database where the end_date is before today.
    Compares only the date part, ignoring the time.
    Returns the number of bids deleted.
    """
    # Get the date for today (without time)
    # Use timezone-aware comparison if your DB stores timezone info,
    # otherwise compare naive datetime objects.
    # Assuming DB stores naive UTC or local times that can be compared directly to naive today_date.
    # If DB is timezone-aware (e.g., TIMESTAMPTZ in PostgreSQL), use timezone.utc
    # from datetime import timezone
    # today_start = datetime.combine(date.today(), time.min).replace(tzinfo=timezone.utc)

    # Simpler approach for SQLite or naive datetimes: Compare date part only
    today_date = date.today()

    # Find bids where the date part of end_date is less than today's date
    # Need to ensure end_date is actually a DateTime column in the DB
    # SQLAlchemy's comparison operators work directly on DateTime columns
    expired_bids_query = db.query(models.Bid).filter(models.Bid.end_date < today_date)

    # Get the count before deleting (optional, but good for logging)
    count = expired_bids_query.count()

    if count > 0:
        print(f"CRUD: Found {count} expired bids (end_date before {today_date}). Deleting...")
        # Perform the delete operation
        expired_bids_query.delete(synchronize_session=False) # Efficient delete without loading objects
        db.commit() # Commit the deletion
        print(f"CRUD: Successfully deleted {count} expired bids.")
    else:
        print("CRUD: No expired bids found to delete.")

    return count

def update_bid_pdf_analysis_status(
    db: Session,
    bid_id: int, # Use the primary key 'id' for updating
    status: PDFAnalysisStatusEnum,
    reasons: Optional[List[str]] = None
) -> Optional[models.Bid]:
    db_bid = db.query(models.Bid).filter(models.Bid.id == bid_id).first()
    if db_bid:
        db_bid.pdf_analysis_status = status
        db_bid.pdf_analysis_reasons = json.dumps(reasons) if reasons else None # Store reasons as JSON string
        db_bid.pdf_last_analysed_at = datetime.now(timezone.utc) # Use timezone-aware datetime
        db.commit()
        db.refresh(db_bid)
        return db_bid
    return None

def update_bid_pdf_analysis(
    db: Session,
    bid_id: int,
    status: PDFAnalysisStatusEnum,
    reasons: str,
    processing_session: Optional[str] = None
) -> Optional[models.Bid]:
    """
    Update bid PDF analysis with string reasons (for compatibility with new processing)
    """
    db_bid = db.query(models.Bid).filter(models.Bid.id == bid_id).first()
    if db_bid:
        db_bid.pdf_analysis_status = status
        db_bid.pdf_analysis_reasons = reasons
        db_bid.pdf_last_analysed_at = datetime.now(timezone.utc)
        if processing_session:
            db_bid.pdf_processing_session = processing_session
        db.commit()
        db.refresh(db_bid)
        return db_bid
    return None

def get_bids_by_session(
    db: Session,
    session_id: str
) -> List[models.Bid]:
    """Get all bids from a specific scraping session"""
    return db.query(models.Bid).filter(
        models.Bid.scrape_session_id == session_id
    ).order_by(models.Bid.scraped_at.desc()).all()

def get_bids_by_source_type(
    db: Session,
    source_type: ScrapeSourceTypeEnum
) -> List[models.Bid]:
    """Get all bids from a specific source type (ministry_organization or location)"""
    return db.query(models.Bid).filter(
        models.Bid.scrape_source_type == source_type
    ).order_by(models.Bid.scraped_at.desc()).all()

def get_latest_scraped_bids(
    db: Session,
    hours_threshold: int = 1
) -> List[models.Bid]:
    """
    Get all bids from recent scraping sessions within the last X hours.
    This ensures we get all bids from a complete scraping queue (multiple tasks).
    """
    # Calculate the timestamp for X hours ago
    from datetime import datetime, timedelta
    recent_time_threshold = datetime.utcnow() - timedelta(hours=hours_threshold)

    # Get all bids scraped within the last X hours
    recent_bids = db.query(models.Bid).filter(
        models.Bid.scraped_at >= recent_time_threshold
    ).order_by(models.Bid.scraped_at.desc()).all()

    if not recent_bids:
        # Fallback: If no bids in the last X hours, get the most recent session
        latest_session = db.query(models.Bid.scrape_session_id).filter(
            models.Bid.scrape_session_id.isnot(None)
        ).order_by(models.Bid.scraped_at.desc()).first()

        if not latest_session:
            return []

        # Then get all bids from that session
        return db.query(models.Bid).filter(
            models.Bid.scrape_session_id == latest_session.scrape_session_id
        ).order_by(models.Bid.scraped_at.desc()).all()

    return recent_bids

def get_unique_scrape_sources(db: Session):
    """Get unique scrape sources for filtering buttons"""
    # Get unique combinations of source_type and source_details
    sources = db.query(
        models.Bid.scrape_source_type,
        models.Bid.scrape_source_details
    ).filter(
        models.Bid.scrape_source_type.isnot(None)
    ).distinct().all()

    result = []
    for source_type, source_details in sources:
        if source_details:
            try:
                details = json.loads(source_details)
                result.append({
                    "source_type": source_type,
                    "details": details
                })
            except json.JSONDecodeError:
                continue

    return result

def get_all_bids_from_latest_scrape_sources(
    db: Session,
    hours_threshold: int = 1
) -> List[models.Bid]:
    """
    Get ALL bids (new + old) from the same sources as the recent scraping sessions.
    Uses time-based approach to get sources from recent scraping sessions.
    """
    from datetime import datetime, timedelta
    recent_time_threshold = datetime.utcnow() - timedelta(hours=hours_threshold)

    # Get all source details from recent scraping sessions
    recent_session_sources = db.query(
        models.Bid.scrape_source_type,
        models.Bid.scrape_source_details
    ).filter(
        models.Bid.scraped_at >= recent_time_threshold,
        models.Bid.scrape_source_type.isnot(None)
    ).distinct().all()

    if not recent_session_sources:
        # Fallback: Get sources from the most recent session
        latest_session = db.query(models.Bid.scrape_session_id).filter(
            models.Bid.scrape_session_id.isnot(None)
        ).order_by(models.Bid.scraped_at.desc()).first()

        if not latest_session:
            return []

        recent_session_sources = db.query(
            models.Bid.scrape_source_type,
            models.Bid.scrape_source_details
        ).filter(
            models.Bid.scrape_session_id == latest_session.scrape_session_id
        ).distinct().all()

    if not recent_session_sources:
        return []

    # Get all bids that match any of the source combinations from recent sessions
    all_matching_bids = []
    for source_type, source_details in recent_session_sources:
        if source_type and source_details:
            matching_bids = db.query(models.Bid).filter(
                models.Bid.scrape_source_type == source_type,
                models.Bid.scrape_source_details == source_details
            ).all()
            all_matching_bids.extend(matching_bids)

    # Remove duplicates and sort
    seen_ids = set()
    unique_bids = []
    for bid in all_matching_bids:
        if bid.id not in seen_ids:
            unique_bids.append(bid)
            seen_ids.add(bid.id)

    # Sort by scraped_at desc - NO PAGINATION, RETURN ALL
    unique_bids.sort(key=lambda x: x.scraped_at, reverse=True)
    return unique_bids