// src/components/ui/ProgressBar.tsx
"use client";

import React from "react";

interface ProgressBarProps {
	/** Current progress value */
	current: number;
	/** Total/maximum value */
	total: number;
	/** Progress bar height in pixels */
	height?: number;
	/** Custom className for styling */
	className?: string;
	/** Color variant based on status */
	variant?: "processing" | "completed" | "error" | "default";
	/** Whether to show progress text */
	showProgress?: boolean;
	/** Custom label to display above the bar */
	label?: string;
	/** Whether to show detailed progress (current/total) */
	showDetailed?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
	current,
	total,
	height = 8,
	className = "",
	variant = "default",
	showProgress = true,
	label,
	showDetailed = false,
}) => {
	const percentage = total > 0 ? Math.min((current / total) * 100, 100) : 0;
	const safePercentage = Math.max(percentage, 0);

	const getVariantClasses = () => {
		switch (variant) {
			case "processing":
				return "progress-bar processing";
			case "completed":
				return "progress-bar completed";
			case "error":
				return "progress-bar error";
			default:
				return "progress-bar";
		}
	};

	return (
		<div className={`w-full ${className}`}>
			{(label || showProgress) && (
				<div className="flex justify-between items-center mb-2">
					{label && <span className="text-sm font-medium text-gray-700">{label}</span>}
					{showProgress && (
						<span className="text-sm text-gray-500">
							{showDetailed 
								? `${current}/${total} (${Math.round(safePercentage)}%)`
								: `${Math.round(safePercentage)}%`
							}
						</span>
					)}
				</div>
			)}
			<div className="progress-bar-container" style={{ height: `${height}px` }}>
				<div
					className={getVariantClasses()}
					style={{ width: `${safePercentage}%` }}
				/>
			</div>
		</div>
	);
};

export default ProgressBar;
