# app/services/excel_exporter_service.py
import pandas as pd
from typing import List, Optional
from io import BytesIO

from app.schemas import bid_schemas

def generate_filtered_bids_excel(
    filtered_bids_from_db: List[bid_schemas.BidInDB],
    existing_excel_bytes: Optional[bytes] = None
) -> BytesIO:
    """
    Generates Excel with only filtered bids for extension highlighting.
    Extension will highlight these bids GREEN on GEM website.
    """

    excel_columns = [
        "Bid Number", "Items", "Quantity", "Ministry", "Department",
        "End Date"
    ]

    current_data_for_df = []
    for bid in filtered_bids_from_db:
        current_data_for_df.append({
            "Bid Number": str(bid.bid_number), # Ensure string
            "Items": bid.items,
            "Quantity": bid.quantity, # Keep as number if it is, or handle conversion if mixed
            "Ministry": bid.ministry_name,
            "Department": bid.department_name,
            "End Date": bid.end_date.strftime('%Y-%m-%d %H:%M:%S') if bid.end_date else None
        })

    current_df = pd.DataFrame(current_data_for_df)
    # Explicitly set dtypes for current_df for columns we know might cause issues
    if not current_df.empty:
        current_df["Bid Number"] = current_df["Bid Number"].astype(str)
        # If Quantity can be mixed, you might need more complex handling or astype(object)
        # For now, assume Quantity is mostly numeric or None

    final_df = pd.DataFrame(columns=excel_columns) # Initialize with defined columns

    if existing_excel_bytes:
        print("Excel Service: Updating existing Excel file.")
        try:
            # Read existing excel, explicitly setting dtype for key columns to string
            # This helps prevent Pandas from inferring them as int/float if they look numeric
            dtype_spec = {
                "Bid Number": str,
                # Add other columns if they are problematic, e.g., "Quantity": str if it can be non-numeric
            }
            existing_df = pd.read_excel(BytesIO(existing_excel_bytes), sheet_name=0, dtype=dtype_spec)

            if "Bid Number" not in existing_df.columns:
                print("Excel Service Warning: 'Bid Number' column not found in uploaded Excel. Creating new Excel from filtered data.")
                final_df = current_df # Use the new data
            else:
                # Set index for both DataFrames using the common key "Bid Number"
                # Ensure it's string type in both before setting as index for reliable join/update
                existing_df["Bid Number"] = existing_df["Bid Number"].astype(str)
                existing_df = existing_df.set_index("Bid Number")

                if not current_df.empty:
                    current_df_indexed = current_df.set_index("Bid Number")

                    # Update existing_df with values from current_df_indexed
                    # This updates rows where "Bid Number" (index) matches
                    existing_df.update(current_df_indexed)

                    # Identify new bids from current_df that are not in existing_df's original index
                    new_bids_to_append_df = current_df[~current_df["Bid Number"].isin(existing_df.index)]
                else:
                    new_bids_to_append_df = pd.DataFrame() # Empty dataframe

                # Combine and reset index
                final_df = pd.concat(
                    [existing_df.reset_index(), new_bids_to_append_df],
                    ignore_index=True
                )

                # Ensure desired column order and include any extra columns from original Excel
                present_cols = [col for col in excel_columns if col in final_df.columns]
                extra_cols_from_existing = [col for col in existing_df.reset_index().columns if col not in excel_columns and col in final_df.columns]
                final_df = final_df[present_cols + extra_cols_from_existing]


        except Exception as e:
            print(f"Excel Service Error: Failed to read or process existing Excel: {e}. Creating a new one from filtered data.")
            if not current_df.empty:
                final_df = current_df.copy() # Use a copy
            # else final_df remains as DataFrame with only headers
    else:
        print("Excel Service: No existing Excel provided. Creating new file from filtered data.")
        if not current_df.empty:
            final_df = current_df.copy()
        # else final_df remains as DataFrame with only headers

    # Ensure the DataFrame is not empty before writing, even if it's just headers
    if final_df.empty and not list(final_df.columns): # if truly empty with no columns
        final_df = pd.DataFrame(columns=excel_columns)
    elif final_df.empty and list(final_df.columns): # if empty but has columns (e.g. only headers)
        pass # It's fine, will write headers
    elif not all(col in final_df.columns for col in excel_columns): # if some default columns are missing
        for col in excel_columns:
            if col not in final_df.columns:
                final_df[col] = None # Add missing columns as None/NaN


    excel_buffer = BytesIO()
    with pd.ExcelWriter(excel_buffer, engine='openpyxl', mode='w') as writer:
        final_df.to_excel(writer, sheet_name="Filtered_Bids", index=False) # Changed sheet name slightly
    excel_buffer.seek(0)
    return excel_buffer


def generate_pdf_processed_excel(
    all_bids_from_db: List[bid_schemas.BidInDB],
    existing_excel_bytes: Optional[bytes] = None
) -> BytesIO:
    """
    Generates Excel with ALL bids and PDF processing status for extension highlighting.
    Extension will highlight: GREEN (accepted), RED (rejected), YELLOW (not in Excel).

    Args:
        all_bids_from_db: List of all bids from database
        existing_excel_bytes: Optional existing Excel file to update

    Returns:
        BytesIO: Excel file buffer
    """

    # Columns for PDF processed export
    excel_columns = [
        "Bid Number", "Items", "Quantity", "Ministry", "Department",
        "End Date", "PDF Status"
    ]

    current_data_for_df = []
    print(f"Excel Service: Processing {len(all_bids_from_db)} bids for PDF processed export")

    for i, bid in enumerate(all_bids_from_db):
        if i % 100 == 0:  # Log progress every 100 bids
            print(f"Excel Service: Processing bid {i+1}/{len(all_bids_from_db)}")

        try:
            # Map PDF analysis status to PDF Status
            pdf_status = "PENDING"
            try:
                if bid.pdf_analysis_status:
                    status_str = str(bid.pdf_analysis_status)
                    if "ACCEPTED" in status_str.upper():
                        pdf_status = "ACCEPTED"
                    elif "REJECTED" in status_str.upper():
                        pdf_status = "REJECTED"
                    # else remains PENDING
            except Exception as e:
                print(f"Excel Service Warning: Error processing PDF status for bid {bid.bid_number}: {e}")
                pdf_status = "PENDING"

            # Safe date formatting
            try:
                end_date_str = bid.end_date.strftime('%Y-%m-%d %H:%M:%S') if bid.end_date else None
            except Exception:
                end_date_str = str(bid.end_date) if bid.end_date else None

            current_data_for_df.append({
                "Bid Number": bid.bid_number,
                "Items": bid.items or "",
                "Quantity": bid.quantity,
                "Ministry": bid.ministry_name or "",
                "Department": bid.department_name or "",
                "End Date": end_date_str,
                "PDF Status": pdf_status
            })

        except Exception as e:
            print(f"Excel Service Error: Failed to process bid {bid.bid_number}: {e}")
            continue  # Skip this bid and continue with the next one

    current_df = pd.DataFrame(current_data_for_df)

    # Handle existing Excel file if provided
    final_df = current_df
    if existing_excel_bytes:
        try:
            print("Excel Service: Updating existing PDF processed Excel file.")
            dtype_spec = {
                "Bid Number": str,
                "PDF Status": str
            }
            existing_df = pd.read_excel(BytesIO(existing_excel_bytes), sheet_name=0, dtype=dtype_spec)

            if "Bid Number" in existing_df.columns:
                # Preserve existing PDF status if available
                existing_df["Bid Number"] = existing_df["Bid Number"].astype(str)
                existing_df = existing_df.set_index("Bid Number")

                if not current_df.empty:
                    current_df_indexed = current_df.set_index("Bid Number")

                    # Preserve existing PDF status for bids that already have it
                    if "PDF Status" in existing_df.columns:
                        for bid_number in current_df_indexed.index:
                            if bid_number in existing_df.index and existing_df.loc[bid_number, "PDF Status"] in ["ACCEPTED", "REJECTED"]:
                                current_df_indexed.loc[bid_number, "PDF Status"] = existing_df.loc[bid_number, "PDF Status"]

                    final_df = current_df_indexed.reset_index()

        except Exception as e:
            print(f"Excel Service Error: Failed to process existing PDF processed Excel: {e}. Creating new one.")
            final_df = current_df

    # Ensure all required columns are present
    for col in excel_columns:
        if col not in final_df.columns:
            final_df[col] = None

    # Reorder columns
    final_df = final_df[excel_columns]

    # Create Excel file
    print(f"Excel Service: Creating Excel file with {len(final_df)} rows")
    excel_buffer = BytesIO()
    with pd.ExcelWriter(excel_buffer, engine='openpyxl', mode='w') as writer:
        final_df.to_excel(writer, sheet_name="PDF_Processed_Bids", index=False)

    excel_buffer.seek(0)
    print("Excel Service: PDF processed Excel file created successfully")
    return excel_buffer






