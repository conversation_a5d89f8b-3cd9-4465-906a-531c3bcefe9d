// src/components/layout/Header.tsx
// "use client" directive removed as it's not needed here

// Link import removed as we no longer need it
import Image from "next/image";

const Header = () => {
	return (
		<header className="bg-white text-gray-900 shadow-sm sticky top-0 z-50">
			<div className="container-custom">
				<div className="py-6 flex justify-center items-center">
					{/* Logo */}
					<div className="flex items-center">
						<Image
							src="/static/logo.png"
							alt="Logo"
							width={180}
							height={90}
							className="h-16 w-auto"
						/>
					</div>
				</div>
			</div>
		</header>
	);
};

export default Header;
