import os
import re
import json
import glob
import argparse
from collections import defaultdict

class BidDocumentFilter:
    """
    Filter bid documents based on specific conditions in EMD details and buyer added terms.
    """

    def __init__(self):
        # Define disqualifying patterns for buyer added terms
        self.disqualifying_term_patterns = [
            # Registered vendor requirements - SPECIFIC to organizations
            r'(established|registered)\s+vendors?\s+for\s+.{0,50}(MPF|OFB|AVNL|Machine\s+Tool)',
            r'vendors?\s+(should|must)\s+be\s+(established|registered)\s+.{0,50}(MPF|OFB|AVNL)',
            r'(only|existing)\s+registered\s+vendors\s+.{0,50}(MPF|OFB|AVNL)',
            r'registered\s+with\s+(MPF|OFB|AVNL|Machine\s+Tool)',

            # In-house facility requirements that are too restrictive
            r'in\s*house\s+(forging|casting|heat\s+treatment)\s+facility\s+is\s+(mandatory|required|essential)',
            r'(mandatory|required|essential)\s+in\s*house\s+(forging|casting|heat\s+treatment)',
            r'(must|should)\s+have\s+in\s*house\s+(forging|casting|heat\s+treatment)',

            # Specific factory/organization requirements
            r'(MPF|OFB|AVNL|Machine\s+Tool\s+Prototype\s+Factory)',
            r'sister\s+factories\s+of\s+(AVNL|OFB)',

            # Experience requirements that are too specific
            r'(minimum|min\.?)\s+\d+\s+years?\s+experience\s+in\s+manufacturing',
            r'experience\s+in\s+manufacturing\s+of\s+similar\s+items\s+for\s+\d+\s+years',

            # Specific certification requirements that are too restrictive
            r'(ISO|AS|NABL|DGQA)\s+certification\s+(is\s+)?(mandatory|required|essential)',

            # Specific customer base requirements
            r'(must|should)\s+have\s+supplied\s+to\s+(defence|government|PSU)',
            r'previous\s+supply\s+to\s+(defence|government|PSU)\s+is\s+(mandatory|required|essential)',

            # Specific manufacturing capability requirements that are too restrictive
            r'(must|should)\s+have\s+capability\s+to\s+manufacture\s+.{0,50}(specific|particular)',
            r'manufacturing\s+capability\s+for\s+\w+\s+is\s+(mandatory|required|essential)',

            # Specific testing facility requirements that are too restrictive
            r'(must|should)\s+have\s+testing\s+facility\s+for\s+.{0,50}(specific|particular)',
            r'testing\s+facility\s+for\s+\w+\s+is\s+(mandatory|required|essential)',
        ]

        # Define patterns that should NOT trigger rejection (whitelist)
        self.whitelist_patterns = [
            # Basic manufacturer requirements (not restrictive)
            r'submit\s+self-attested\s+in\s+house\s+plant\s+and\s+machinery\s+details',
            r'show\s+that\s+they\s+are\s+manufacturer',
            r'offers\s+from\s+traders\s*\/?\s*stockiest\s+shall\s+not',
            r'traders\s+and\s+stockiest\s+are\s+not\s+allowed',

            # Basic experience requirements (not restrictive)
            r'bidder\s+should\s+be\s+manufacturer',
            r'bidder\s+should\s+have\s+experience',

            # Established vendors for higher assembly (not restrictive)
            r'offers\s+of\s+established\s+vendors\s+for\s+higher\s+assy',
        ]

        # Define disqualifying patterns for EMD details
        self.disqualifying_emd_patterns = [
            r'required\s+section',
            r'section\s+required',
            r'applicable\s+for\s+selected\s+section',
            r'section\s+wise\s+EMD',
            r'EMD\s+exemption\s+not\s+allowed',
        ]

        # Define keywords that might indicate disqualifying conditions
        self.disqualifying_keywords = [
            "sister factory", "specific experience", "OFB", "AVNL", "MPF",
            "minimum years", "defence", "PSU", "government", "capability",
            "testing facility", "manufacturing facility", "forging", "machining"
        ]

    def check_term_for_disqualification(self, term):
        """Check if a term contains disqualifying patterns."""
        term_lower = term.lower()

        # First check if the term matches any whitelist patterns
        # If it does, don't reject it regardless of other patterns
        for pattern in self.whitelist_patterns:
            if re.search(pattern, term_lower, re.IGNORECASE):
                # Check if it also contains specific organization requirements
                # Even whitelisted terms should be rejected if they mention specific organizations
                if re.search(r'(MPF|OFB|AVNL|Machine\s+Tool\s+Prototype\s+Factory)', term, re.IGNORECASE):
                    # Only reject if it's a restrictive requirement, not just a mention
                    if re.search(r'(registered|established|approved)\s+(with|by|for|at)\s+.{0,20}(MPF|OFB|AVNL)', term_lower, re.IGNORECASE):
                        return True, "organization-specific requirement"
                return False, None

        # Check against disqualifying patterns
        for pattern in self.disqualifying_term_patterns:
            if re.search(pattern, term_lower, re.IGNORECASE):
                return True, pattern

        # Check for keyword combinations that might indicate disqualification
        keyword_count = sum(1 for keyword in self.disqualifying_keywords if keyword.lower() in term_lower)
        if keyword_count >= 2:  # If two or more keywords are found, flag for review
            # Double-check against whitelist patterns to avoid false positives
            whitelist_match = False
            for pattern in self.whitelist_patterns:
                if re.search(pattern, term_lower, re.IGNORECASE):
                    whitelist_match = True
                    break

            if not whitelist_match:
                return True, "multiple disqualifying keywords"

        return False, None

    def check_emd_for_disqualification(self, emd_details):
        """Check if EMD details contain disqualifying patterns."""
        if not emd_details:
            return False, None

        # Convert EMD details to string for pattern matching
        emd_text = json.dumps(emd_details).lower()

        # Check against patterns
        for pattern in self.disqualifying_emd_patterns:
            if re.search(pattern, emd_text, re.IGNORECASE):
                return True, pattern

        return False, None

    def filter_document(self, document_path):
        """
        Filter a document based on its extracted EMD details and bid terms.

        Args:
            document_path: Path to the directory containing extracted document data

        Returns:
            A tuple (accepted, reasons) where:
            - accepted is a boolean indicating if the document is accepted
            - reasons is a list of reasons for rejection if not accepted
        """
        reasons = []

        # Check if the document directory exists
        if not os.path.isdir(document_path):
            return False, ["Document directory not found"]

        # Get the document name
        document_name = os.path.basename(document_path)

        # Check for EMD details
        emd_json_path = os.path.join(document_path, f"{document_name}_emd_details.json")
        if os.path.exists(emd_json_path):
            try:
                with open(emd_json_path, 'r', encoding='utf-8') as f:
                    emd_details = json.load(f)

                # Check EMD details for disqualification
                is_disqualified, pattern = self.check_emd_for_disqualification(emd_details)
                if is_disqualified:
                    reasons.append(f"EMD disqualification: {pattern}")
            except Exception as e:
                reasons.append(f"Error reading EMD details: {str(e)}")

        # Check for bid terms
        terms_json_path = os.path.join(document_path, f"{document_name}_bid_terms.json")
        if os.path.exists(terms_json_path):
            try:
                with open(terms_json_path, 'r', encoding='utf-8') as f:
                    bid_terms = json.load(f)

                # Check each term for disqualification
                if "terms_list" in bid_terms and bid_terms["terms_list"]:
                    for i, term in enumerate(bid_terms["terms_list"]):
                        is_disqualified, pattern = self.check_term_for_disqualification(term)
                        if is_disqualified:
                            reasons.append(f"Term {i+1} disqualification: {pattern}")
                            # Include the problematic term for reference
                            term_excerpt = term[:100] + "..." if len(term) > 100 else term
                            reasons.append(f"  Term excerpt: {term_excerpt}")
            except Exception as e:
                reasons.append(f"Error reading bid terms: {str(e)}")

        # Document is accepted if there are no reasons for rejection
        return len(reasons) == 0, reasons

    def filter_documents(self, input_dir, output_file=None):
        """
        Filter multiple documents and generate a report.

        Args:
            input_dir: Directory containing extracted document data
            output_file: Path to save the report (optional)

        Returns:
            A dictionary with filtering results
        """
        results = {
            "accepted": [],
            "rejected": [],
            "summary": {
                "total": 0,
                "accepted": 0,
                "rejected": 0,
                "rejection_reasons": defaultdict(int)
            }
        }

        # Find all document directories
        document_dirs = []
        for item in os.listdir(input_dir):
            item_path = os.path.join(input_dir, item)
            if os.path.isdir(item_path) and not item.startswith('.'):
                document_dirs.append(item_path)

        results["summary"]["total"] = len(document_dirs)

        # Process each document
        for doc_dir in document_dirs:
            doc_name = os.path.basename(doc_dir)
            accepted, reasons = self.filter_document(doc_dir)

            if accepted:
                results["accepted"].append({
                    "name": doc_name,
                    "path": doc_dir
                })
                results["summary"]["accepted"] += 1
            else:
                results["rejected"].append({
                    "name": doc_name,
                    "path": doc_dir,
                    "reasons": reasons
                })
                results["summary"]["rejected"] += 1

                # Count rejection reasons
                for reason in reasons:
                    if "disqualification:" in reason:
                        reason_type = reason.split("disqualification:")[0].strip()
                        results["summary"]["rejection_reasons"][reason_type] += 1

        # Save results to file if specified
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2)

            # Also create a human-readable report
            report_file = output_file.replace('.json', '.txt')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"Bid Document Filtering Report\n")
                f.write(f"===========================\n\n")

                f.write(f"Total documents: {results['summary']['total']}\n")
                f.write(f"Accepted documents: {results['summary']['accepted']}\n")
                f.write(f"Rejected documents: {results['summary']['rejected']}\n\n")

                f.write(f"Rejection Reasons Summary:\n")
                for reason, count in results["summary"]["rejection_reasons"].items():
                    f.write(f"- {reason}: {count}\n")

                f.write(f"\nAccepted Documents:\n")
                for doc in results["accepted"]:
                    f.write(f"- {doc['name']}\n")

                f.write(f"\nRejected Documents:\n")
                for doc in results["rejected"]:
                    f.write(f"- {doc['name']}\n")
                    for reason in doc["reasons"]:
                        f.write(f"  * {reason}\n")
                    f.write(f"\n")

        return results

def main():
    parser = argparse.ArgumentParser(description="Filter bid documents based on specific conditions")
    parser.add_argument("--input_dir", default="bid_results_batch", help="Directory containing extracted document data")
    parser.add_argument("--output_file", default="filtering_results.json", help="Path to save the filtering results")
    args = parser.parse_args()

    filter = BidDocumentFilter()
    results = filter.filter_documents(args.input_dir, args.output_file)

    print(f"Filtering complete. Results saved to {args.output_file}")
    print(f"Total documents: {results['summary']['total']}")
    print(f"Accepted documents: {results['summary']['accepted']}")
    print(f"Rejected documents: {results['summary']['rejected']}")

if __name__ == "__main__":
    main()
