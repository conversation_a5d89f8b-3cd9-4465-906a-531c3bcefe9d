# app/api/v1/endpoints/bids.py
import datetime
import re
import time
import uuid
import zipfile # For creating ZIP archives
import shutil
import os
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status, Query # Added Query
import requests
from sqlalchemy.orm import Session
from typing import List, Optional, Tuple
from fastapi import File, UploadFile # Add these
import shutil
from enum import Enum
import tempfile
from urllib.parse import urlparse
from app.db.database import get_db
from app.schemas import bid_schemas
from app.crud import crud_bid
from app.services import scraper_service
from app.services import filter_service # Import the new filter service
from pydantic import BaseModel, validator # For request body and validation
from app.core import config as settings # Import the new settings module
from app.services import file_downloader_service # Definitely need this
from app.core.config import settings # For DOWNLOADS_BASE_DIR
import requests  # For creating session for PDF downloads

from fastapi.responses import StreamingResponse # Import StreamingResponse
import requests # For synchronous download in this example
from app.tasks import (
    TaskInfo, TaskStatus, TASK_STATUS_STORE, update_task_status,
    cancel_task, should_task_cancel, register_background_task, unregister_background_task
)
from app.services import pdf_processor_service # Import the new service
from app.core.config import settings
from fastapi.responses import FileResponse
from fastapi import BackgroundTasks
from fastapi.responses import StreamingResponse
from fastapi import Form, File, UploadFile
from app.services import excel_exporter_service
from app.db.models import PDFAnalysisStatusEnum, ScrapeSourceTypeEnum
from app.db import models
import json

router = APIRouter()

# --- Background Task Definition (remains the same) ---
def run_background_scrape_and_save(
    db: Session, ministry_name: str, organization_name: str, max_iterations: int, task_id: str # Renamed for clarity within this function
):
    # Register this task as active
    register_background_task(task_id)

    try:
        time.sleep(1) # Small delay before starting intensive work
        update_task_status(task_id, status=TaskStatus.RUNNING, message="Scraping started...")
        print(f"Background task (ID: {task_id}): Starting scrape for Ministry '{ministry_name}', Organization '{organization_name}'")

        # Check if task should be cancelled
        if should_task_cancel(task_id):
            update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user before scraping started.", error="Task cancelled by user")
            print(f"Background task (ID: {task_id}): Cancelled before scraping started.")
            return

        scraped_bids_schemas: List[bid_schemas.BidCreate] = []
        try:
            # NOTE: Ideally, scraper_service would accept the update_task_status callback or task_id
            # to provide more granular progress (e.g., per page). For now, update before/after.
            # update_task_status(task_id, stage="Scraping Pages") # Example stage update

            scraped_bids_schemas = scraper_service.scrape_bids_by_ministry_organization(
                ministry_name=ministry_name, organization_name=organization_name, max_total_iterations=max_iterations
            )

            # Check if task should be cancelled after scraping
            if should_task_cancel(task_id):
                update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user after scraping but before saving.", error="Task cancelled by user")
                print(f"Background task (ID: {task_id}): Cancelled after scraping but before saving.")
                return

            if not scraped_bids_schemas:
                update_task_status(task_id, status=TaskStatus.COMPLETED, message="Scraping complete. No new bids found.", count=0)
                print(f"Background task (ID: {task_id}): No bids were scraped.")
                return

            update_task_status(task_id, status=TaskStatus.RUNNING, message=f"Scraped {len(scraped_bids_schemas)} bids. Saving to DB...", stage="Saving Data")
            print(f"Background task (ID: {task_id}): Scraped {len(scraped_bids_schemas)} unique bids. Attempting to save...")

            # Add scraping session tracking to each bid
            scrape_session_id = task_id  # Use task_id as session_id
            scrape_source_details = json.dumps({
                "ministry": ministry_name,
                "organization": organization_name
            })

            for bid_schema in scraped_bids_schemas:
                bid_schema.scrape_session_id = scrape_session_id
                bid_schema.scrape_source_type = ScrapeSourceTypeEnum.MINISTRY_ORGANIZATION
                bid_schema.scrape_source_details = scrape_source_details

            # Check if task should be cancelled before saving to DB
            if should_task_cancel(task_id):
                update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user before saving to database.", error="Task cancelled by user")
                print(f"Background task (ID: {task_id}): Cancelled before saving to database.")
                return

            # Pass db session to CRUD operation
            created_db_models = crud_bid.bulk_create_bids(db=db, bids_to_create=scraped_bids_schemas)

            final_message = f"Scraping and saving complete. Saved/updated {len(created_db_models)} bids."
            update_task_status(task_id, status=TaskStatus.COMPLETED, message=final_message, count=len(created_db_models))
            print(f"Background task (ID: {task_id}): {final_message}")

        except Exception as e:
            print(f"Background task {task_id} Error: {e}")
            import traceback
            traceback.print_exc()
            update_task_status(task_id, status=TaskStatus.FAILED, message="An error occurred during scraping or saving.", error=str(e))

        print(f"Background task (ID: {task_id}): Finished processing.")
    finally:
        # Always unregister the task when done
        unregister_background_task(task_id)


# --- API Endpoints ---

@router.post(
    "/scrape-and-save/ministry-organization",
    status_code=status.HTTP_202_ACCEPTED,
    summary="Trigger scraping for a ministry and organization and save to DB"
)
async def trigger_scrape_ministry_organization(
    ministry_name: str,
    organization_name: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    max_scrape_iterations: Optional[int] = Query(default=1, ge=1)
):
    task_id = str(uuid.uuid4())
    initial_details = {
        "ministry": ministry_name,
        "organization": organization_name,
        "max_iterations": max_scrape_iterations
    }
    # Create the initial task entry in the store
    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message="Ministry/Org scrape initiated.",
        details=initial_details
    )

    print(f"API: Received scrape request for Ministry: '{ministry_name}', Org: '{organization_name}', Task ID: {task_id}")

    background_tasks.add_task(
        run_background_scrape_and_save,
        db, ministry_name, organization_name, max_scrape_iterations, task_id
    )

    return { # Return task ID along with acceptance message
        "message": "Scraping process initiated in the background.",
        "task_id": task_id,
        "details": initial_details
    }


def run_background_location_scrape_and_save(
    db: Session, state_name: str, city_name: Optional[str], max_iterations: int, task_id: str
):
    # Register this task as active
    register_background_task(task_id)

    try:
        time.sleep(1)
        update_task_status(task_id, status=TaskStatus.RUNNING, message="Location scraping started...")
        print(f"Background task (ID: {task_id}): Starting Location scrape for State '{state_name}', City '{city_name or 'N/A'}'")

        # Check if task should be cancelled
        if should_task_cancel(task_id):
            update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user before location scraping started.", error="Task cancelled by user")
            print(f"Background task (ID: {task_id}): Cancelled before location scraping started.")
            return

        scraped_bids_schemas: List[bid_schemas.BidCreate] = []
        try:
            # update_task_status(task_id, stage="Scraping Pages")
            scraped_bids_schemas = scraper_service.scrape_bids_by_location(
                state_name=state_name, city_name=city_name, max_total_iterations=max_iterations
            )

            # Check if task should be cancelled after scraping
            if should_task_cancel(task_id):
                update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user after location scraping but before saving.", error="Task cancelled by user")
                print(f"Background task (ID: {task_id}): Cancelled after location scraping but before saving.")
                return

            if not scraped_bids_schemas:
                update_task_status(task_id, status=TaskStatus.COMPLETED, message="Location scraping complete. No new bids found.", count=0)
                print(f"Background task (ID: {task_id}): No bids were scraped for location.")
                return

            update_task_status(task_id, status=TaskStatus.RUNNING, message=f"Scraped {len(scraped_bids_schemas)} bids. Saving to DB...", stage="Saving Data")
            print(f"Background task (ID: {task_id}): Scraped {len(scraped_bids_schemas)} unique location bids. Attempting to save...")

            # Add scraping session tracking to each bid
            scrape_session_id = task_id  # Use task_id as session_id
            scrape_source_details = json.dumps({
                "state": state_name,
                "city": city_name or "All Cities"
            })

            for bid_schema in scraped_bids_schemas:
                bid_schema.scrape_session_id = scrape_session_id
                bid_schema.scrape_source_type = ScrapeSourceTypeEnum.LOCATION
                bid_schema.scrape_source_details = scrape_source_details

            # Check if task should be cancelled before saving to DB
            if should_task_cancel(task_id):
                update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user before saving location data to database.", error="Task cancelled by user")
                print(f"Background task (ID: {task_id}): Cancelled before saving location data to database.")
                return

            # Pass db session to CRUD operation
            created_db_models = crud_bid.bulk_create_bids(db=db, bids_to_create=scraped_bids_schemas)

            final_message = f"Location scraping and saving complete. Saved/updated {len(created_db_models)} bids."
            update_task_status(task_id, status=TaskStatus.COMPLETED, message=final_message, count=len(created_db_models))
            print(f"Background task (ID: {task_id}): {final_message}")

        except Exception as e:
            print(f"Background task {task_id} Error: {e}")
            import traceback
            traceback.print_exc()
            update_task_status(task_id, status=TaskStatus.FAILED, message="An error occurred during location scraping or saving.", error=str(e))

        print(f"Background task (ID: {task_id}): Finished processing location.")
    finally:
        # Always unregister the task when done
        unregister_background_task(task_id)



@router.post(
    "/scrape-and-save/location",
    status_code=status.HTTP_202_ACCEPTED,
    summary="Trigger scraping for a specific location (state and optional city) and save to DB"
)
async def trigger_scrape_location(
    state_name: str,
    background_tasks: BackgroundTasks,
    city_name: Optional[str] = Query(default=None),
    db: Session = Depends(get_db),
    max_scrape_iterations: Optional[int] = Query(default=1, ge=1)
):
    task_id = str(uuid.uuid4())
    initial_details = {
        "state": state_name,
        "city": city_name or "N/A",
        "max_iterations": max_scrape_iterations
    }
    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message="Location scrape initiated.",
        details=initial_details
    )

    print(f"API: Received location scrape request for State: '{state_name}', City: '{city_name or 'N/A'}', Task ID: {task_id}")

    background_tasks.add_task(
        run_background_location_scrape_and_save,
        db, state_name, city_name, max_scrape_iterations, task_id
    )

    return {
        "message": "Location scraping process initiated in the background.",
        "task_id": task_id,
        "details": initial_details
    }


@router.get(
    "/scrape-status/{task_id}",
    response_model=TaskInfo, # Use the Pydantic model for response structure
    summary="Get the status of a background scraping task"
)
async def get_scrape_status(task_id: str):
    """
    Retrieves the current status and progress of a background scraping task
    using the `task_id` returned when the scrape was initiated.
    """
    task = TASK_STATUS_STORE.get(task_id)
    if not task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Scraping task not found")
    return task


@router.post(
    "/task/{task_id}/cancel",
    summary="Cancel a running background task"
)
async def cancel_task_endpoint(task_id: str):
    """
    Cancel a running background task by its ID.
    This will mark the task for cancellation, but it's up to the task implementation to check for this flag.
    """
    from app.tasks import cancel_task

    if task_id not in TASK_STATUS_STORE:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task with ID {task_id} not found.")

    success = cancel_task(task_id)
    if success:
        return {"message": f"Task {task_id} has been marked for cancellation. It will stop at the next cancellation check point."}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Task {task_id} could not be cancelled. It may have already completed or failed."
        )

@router.post(
    "/stop-task/{task_id}",
    status_code=status.HTTP_200_OK,
    summary="Stop a running background task"
)
async def stop_task(task_id: str):
    """
    Stops a running background task by setting its cancellation flag
    and updating the message to indicate it was stopped by the user.
    """
    task = TASK_STATUS_STORE.get(task_id)
    if not task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")

    if task.status == TaskStatus.COMPLETED or task.status == TaskStatus.FAILED:
        return {"message": f"Task already in final state: {task.status}"}

    # Mark the task for cancellation
    if cancel_task(task_id):
        print(f"Task {task_id} marked for cancellation")
        return {"message": "Task cancellation requested", "task_id": task_id}
    else:
        # If cancel_task returns False, the task couldn't be cancelled
        # This could happen if the task is in a state that can't be cancelled
        # or if the task_id doesn't exist
        update_task_status(
            task_id=task_id,
            status=TaskStatus.FAILED,
            message="Task could not be cancelled, marked as failed",
            error="Task was manually stopped by the user but could not be cancelled gracefully"
        )
        return {"message": "Task could not be cancelled, marked as failed", "task_id": task_id}

class KeywordFilterRequest(BaseModel):
    keywords: List[str]


from enum import Enum


class FilterType(str, Enum):
    CUSTOM = "custom"   # For keywords provided in the request

class AdvancedFilterRequest(BaseModel):
    filter_type: FilterType
    custom_keywords: Optional[List[str]] = None # Only used if filter_type is CUSTOM

    @validator('custom_keywords', pre=True, always=True)
    def check_custom_keywords_for_custom_type(cls, v, values):
        if not v:
            raise ValueError("custom_keywords must be provided")
        return v


@router.post(
    "/bids/advanced-filter",
    response_model=List[bid_schemas.BidInDB],
    summary="Filter stored bids using pre-defined keyword lists or custom keywords"
)
async def advanced_filter_bids(
    request: AdvancedFilterRequest,
    db: Session = Depends(get_db)
):
    if not request.custom_keywords:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="custom_keywords are required.")

    keywords_to_use = request.custom_keywords
    print(f"API: Using CUSTOM keywords for filtering: {keywords_to_use}")

    # Fetch all bids from the database
    all_db_bids = crud_bid.get_bids(db, limit=50000, skip=0)
    if not all_db_bids:
        return []

    # Apply keyword filtering using the service
    python_filtered_bids = filter_service.filter_bids_by_keywords_in_items(
        bids=all_db_bids,
        keywords=keywords_to_use
    )

    print(f"API: Advanced filter result: {len(python_filtered_bids)} bids matched. Returning ALL filtered bids.")
    return python_filtered_bids


@router.post(
    "/bids/filter", # Simplified endpoint name
    response_model=List[bid_schemas.BidInDB],
    summary="Filter stored bids by a list of keywords in their 'items' description"
)
async def filter_bids_by_provided_keywords(
    request: KeywordFilterRequest, # Takes the list of keywords from request body
    db: Session = Depends(get_db)
):
    """
    Retrieves ALL bids from the database and then applies keyword filtering
    to the 'items' field based on the provided list of keywords.

    - **keywords**: A list of strings to search for within the bid items.
    """
    print(f"API: Received keyword filter request with keywords: {request.keywords}")

    if not request.keywords:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Keywords list cannot be empty for filtering.")

    # Step 1: Get ALL bids from the database.
    # WARNING: Fetching all bids can be memory intensive if the DB is very large.
    # Consider if pre-filtering by date range or other criteria is always needed.
    # For now, as requested, it filters from all bids.
    # The 'limit' here in crud_bid.get_bids is a safety, not for final pagination.
    all_db_bids = crud_bid.get_bids(db, limit=5000, skip=0) # Fetch a very large number, effectively "all" for most cases

    if not all_db_bids:
        print("API: No bids found in the database to filter.")
        return []

    # These are SQLAlchemy model instances.

    # Step 2: Apply keyword filtering using the service
    print(f"API: Filtering {len(all_db_bids)} bids with keywords: {request.keywords}")
    python_filtered_bids = filter_service.filter_bids_by_keywords_in_items(
        bids=all_db_bids, # Pass SA models
        keywords=request.keywords
    )

    print(f"API: Filter result: {len(python_filtered_bids)} bids matched keywords. Returning ALL filtered bids.")

    # FastAPI will convert the SQLAlchemy models to BidInDB Pydantic models
    # because of the `response_model=List[bid_schemas.BidInDB]`
    return python_filtered_bids



def run_background_bid_document_download(
    db: Session,
    bid_numbers: List[str],
    # target_subdirectory_name is now more like a prefix for the zip or temp folder
    user_suggested_prefix: str,
    task_id: str
):
    # Register this task as active
    register_background_task(task_id)

    try:
        time.sleep(1)
        update_task_status(task_id, status=TaskStatus.RUNNING, message="Download and archiving process started...")
        print(f"Background Download Task (ID: {task_id}): Starting download for {len(bid_numbers)} bids. User prefix: '{user_suggested_prefix}'")

        # Check if task should be cancelled
        if should_task_cancel(task_id):
            update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user before download started.", error="Task cancelled by user")
            print(f"Background Download Task (ID: {task_id}): Cancelled before download started.")
            return

        session = requests.Session()
        session.headers.update(settings.REQUEST_HEADERS)

        download_count = 0
        error_count = 0

        # Create a unique temporary directory for this task's PDFs
        # This temp dir will be inside DOWNLOADS_BASE_DIR
        # The final ZIP will go into TEMP_ZIP_DIR
        temp_pdf_storage_dir_name = f"{user_suggested_prefix}_{task_id}" # Unique folder for this task's PDFs
        temp_pdf_full_path = os.path.join(settings.DOWNLOADS_BASE_DIR, temp_pdf_storage_dir_name)
        os.makedirs(temp_pdf_full_path, exist_ok=True)

        downloaded_file_paths_for_zip: List[str] = []

        for i, bid_number in enumerate(bid_numbers):
            # Check if task should be cancelled before each download
            if should_task_cancel(task_id):
                update_task_status(task_id, status=TaskStatus.FAILED, message=f"Task cancelled by user during download. Downloaded {download_count} files before cancellation.", error="Task cancelled by user")
                print(f"Background Download Task (ID: {task_id}): Cancelled during download. Downloaded {download_count} files.")
                # Clean up temp directory
                try:
                    shutil.rmtree(temp_pdf_full_path)
                except OSError as e:
                    print(f"Error removing temp PDF dir {temp_pdf_full_path}: {e}")
                return

            # Enhanced progress tracking with detailed information
            progress_message = f"Downloading {i+1}/{len(bid_numbers)}: {bid_number} (Downloaded: {download_count}, Errors: {error_count})"
            update_task_status(
                task_id,
                stage="Downloading PDFs",
                current=i+1,
                total=len(bid_numbers),
                message=progress_message,
                count=download_count
            )
            print(f"BG Download Task: Processing bid {bid_number} ({i+1}/{len(bid_numbers)}) for PDF download.")

            db_bid = crud_bid.get_bid_by_bid_number(db, bid_number=bid_number)
            if not db_bid or not db_bid.bid_details_url:
                print(f"  Bid {bid_number} not found or has no document URL. Skipping.")
                error_count += 1
                continue

            doc_url_to_download = db_bid.bid_details_url
            sane_bid_number_filename_stem = file_downloader_service.sanitize_filename(bid_number)

            downloaded_pdf_path = file_downloader_service.download_file(
                session,
                doc_url_to_download,
                target_dir=temp_pdf_full_path, # Save to this task's unique temp PDF folder
                base_filename=sane_bid_number_filename_stem
            )

            if downloaded_pdf_path:
                download_count += 1
                downloaded_file_paths_for_zip.append(downloaded_pdf_path)
                print(f"  Successfully downloaded for {bid_number} to: {downloaded_pdf_path}")
            else:
                error_count += 1
                print(f"  Failed to download document for bid {bid_number} from {doc_url_to_download}")
            time.sleep(0.2) # Shorter sleep as we are polite overall

        # Check if task should be cancelled before creating ZIP
        if should_task_cancel(task_id):
            update_task_status(task_id, status=TaskStatus.FAILED, message=f"Task cancelled by user after downloading. Downloaded {download_count} files before cancellation.", error="Task cancelled by user")
            print(f"Background Download Task (ID: {task_id}): Cancelled after downloading. Downloaded {download_count} files.")
            # Clean up temp directory
            try:
                shutil.rmtree(temp_pdf_full_path)
            except OSError as e:
                print(f"Error removing temp PDF dir {temp_pdf_full_path}: {e}")
            return

        if not downloaded_file_paths_for_zip:
            final_message = "No PDF documents were successfully downloaded to archive."
            update_task_status(task_id, status=TaskStatus.FAILED, message=final_message, error_message=final_message, count=0, stage="Archiving")
            print(f"Background Download Task (ID: {task_id}): {final_message}")
            # Clean up empty temp PDF directory
            try: shutil.rmtree(temp_pdf_full_path)
            except OSError as e: print(f"Error removing temp PDF dir {temp_pdf_full_path}: {e}")
            return

        # Create ZIP file
        update_task_status(
            task_id,
            status=TaskStatus.RUNNING,
            message=f"Creating ZIP archive with {download_count} files...",
            stage="Archiving",
            current=len(bid_numbers),
            total=len(bid_numbers),
            count=download_count
        )
        print(f"BG Download Task: Archiving {download_count} PDFs...")

        # Ensure TEMP_ZIP_DIR exists
        os.makedirs(settings.TEMP_ZIP_DIR, exist_ok=True)
        zip_filename = f"{user_suggested_prefix}_{task_id}.zip" # Unique ZIP filename
        zip_filepath = os.path.join(settings.TEMP_ZIP_DIR, zip_filename)

        try:
            with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zf:
                for i, file_path in enumerate(downloaded_file_paths_for_zip):
                    # Check for cancellation during ZIP creation
                    if should_task_cancel(task_id):
                        print(f"BG Download Task (ID: {task_id}): Cancellation requested during ZIP creation.")
                        update_task_status(
                            task_id,
                            status=TaskStatus.FAILED,
                            message=f"Download cancelled during ZIP creation. Downloaded {download_count} files.",
                            stage="Cancelled",
                            count=download_count
                        )
                        return

                    # Update progress during ZIP creation
                    if i % 10 == 0 or i == len(downloaded_file_paths_for_zip) - 1:  # Update every 10 files or on last file
                        zip_progress_message = f"Adding file {i+1}/{len(downloaded_file_paths_for_zip)} to archive..."
                        update_task_status(
                            task_id,
                            message=zip_progress_message,
                            stage="Archiving",
                            current=len(bid_numbers),
                            total=len(bid_numbers),
                            count=download_count
                        )

                    # Add file to zip, using just the filename (not full path) as arcname
                    zf.write(file_path, arcname=os.path.basename(file_path))

            print(f"BG Download Task: Successfully created ZIP archive: {zip_filepath}")
            final_message = f"Archive created with {download_count} files. Errors/skips: {error_count}."
            update_task_status(
                task_id,
                status=TaskStatus.COMPLETED,
                message=final_message,
                count=download_count,
                zip_file=zip_filename, # Use 'zip_file'
                stage="Completed",
                result_dtl={"zip_filename": zip_filename, "individual_pdf_dir": temp_pdf_storage_dir_name}
            )

        except Exception as e:
            print(f"BG Download Task: Error creating ZIP file: {e}")
            import traceback; traceback.print_exc()
            update_task_status(
                task_id,
                status=TaskStatus.FAILED,
                message="Error creating ZIP archive.",
                error=str(e), # Use 'error'
                stage="Archiving Failed"
            )

        finally:
            # Clean up the temporary directory with individual PDFs
            try:
                print(f"BG Download Task: Cleaning up temporary PDF storage: {temp_pdf_full_path}")
                shutil.rmtree(temp_pdf_full_path)
            except OSError as e:
                print(f"Error removing temporary PDF directory {temp_pdf_full_path}: {e}")

        print(f"Background Download Task (ID: {task_id}): Finished processing.")
    except Exception as e:
        print(f"Background Download Task (ID: {task_id}) Error: {e}")
        import traceback
        traceback.print_exc()
        update_task_status(task_id, status=TaskStatus.FAILED, message="An error occurred during download process.", error=str(e))
    finally:
        # Always unregister the task when done
        unregister_background_task(task_id)


# --- Pydantic model for the download request ---
class DownloadBidsRequest(BaseModel):
    bid_numbers: List[str]
    # This is the path ON THE SERVER where files will be saved.
    # The frontend should NOT send an arbitrary client-side path.
    # For security, the backend might construct this path based on a pre-defined root
    # and a user-supplied (sanitized) subfolder name.
    # For simplicity now, let's assume frontend sends a *relative subdirectory name*
    # that the backend will append to a safe base path.
    target_subdirectory_name: Optional[str] = "gem_bid_downloads" # Default sub-folder name

# ... (Existing API endpoints: scrape triggers, GET /bids, GET /bids/{bid_number}, POST /bids/advanced-filter) ...

@router.post(
    "/bids/initiate-download",
    status_code=status.HTTP_202_ACCEPTED,
    summary="Initiates background download of documents for specified bid numbers"
)
async def initiate_bid_documents_download(
    request_body: DownloadBidsRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    if not request_body.bid_numbers:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="List of bid_numbers cannot be empty.")

    # Sanitize the user-suggested name to use as a prefix or part of the zip filename
    user_prefix = file_downloader_service.sanitize_filename(
        request_body.target_subdirectory_name or "gem_docs_archive"
    )
    if not user_prefix: user_prefix = "archive" # Fallback

    task_id = str(uuid.uuid4())
    initial_details = {"requested_prefix": user_prefix, "num_bids": len(request_body.bid_numbers)}
    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message=f"Bid document download and archive initiated with prefix '{user_prefix}'.",
        details=initial_details
    )

    print(f"API: Download & Archive request for {len(request_body.bid_numbers)} bids. Prefix: '{user_prefix}', Task ID: {task_id}")

    background_tasks.add_task(
        run_background_bid_document_download,
        db,
        request_body.bid_numbers,
        user_prefix, # Pass the sanitized prefix
        task_id
    )
    return {"message": "Bid document download and archive process initiated.", "task_id": task_id, "details": initial_details}


@router.get( # Single PDF download endpoint
    "/bids/download/pdf/{bid_id_internal}",
    summary="Download the PDF document for a specific bid",
    responses={ # ... (responses definition as before) ...
        200: {"content": {"application/pdf": {}}, "description": "Successful Response"},
        404: {"description": "Bid or document URL not found"},
        500: {"description": "Failed to download document from source"},
    }
)
async def download_single_bid_pdf(
    bid_id_internal: str,
    db: Session = Depends(get_db)
):
    print(f"API: Request to download PDF for internal bid ID: {bid_id_internal}")
    db_bid = crud_bid.get_bid_by_internal_id(db, bid_id_internal=bid_id_internal)
    if not db_bid:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Bid with internal ID '{bid_id_internal}' not found.")

    doc_url = db_bid.bid_details_url # CRUCIAL ASSUMPTION: Direct PDF URL
    if not doc_url:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"No document URL for bid '{db_bid.bid_number}'.")

    sane_bid_number = file_downloader_service.sanitize_filename(db_bid.bid_number or f"bid_doc_{bid_id_internal}")

    # For single download, we don't use the full file_downloader_service typically meant for saving to disk,
    # as we want to stream it back. But we need the session headers.
    session = requests.Session()
    session.headers.update(settings.REQUEST_HEADERS)

    try:
        # Make an initial GET if your documents require some session cookies from the main site
        # session.get(str(settings.GEM_INITIAL_PAGE_URL), timeout=(10,20))

        response = session.get(doc_url, stream=True, timeout=(15, 60)) # Removed headers= here, session has them
        response.raise_for_status()

        content_type = response.headers.get('content-type', 'application/octet-stream')
        filename_from_disposition = None
        content_disposition = response.headers.get('content-disposition')
        if content_disposition:
            fname_match = re.findall('filename="?([^"]+)"?', content_disposition)
            if fname_match: filename_from_disposition = fname_match[0]

        final_filename = file_downloader_service.sanitize_filename(filename_from_disposition or f"{sane_bid_number}.pdf")
        if not os.path.splitext(final_filename)[1] and "pdf" in content_type.lower():
             final_filename += ".pdf"

        return StreamingResponse(
            response.iter_content(chunk_size=8192), # Stream in chunks
            media_type=content_type,
            headers={'Content-Disposition': f'attachment; filename="{final_filename}"'}
        )
    except requests.exceptions.Timeout:
        raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail=f"Timeout downloading document from source.")
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail=f"Failed to fetch document from source: {e}")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Unexpected error during download: {e}")



@router.get(
    "/bids",
    response_model=List[bid_schemas.BidInDB],
    summary="Retrieve stored bids with optional filtering"
)
async def read_stored_bids( # ... (rest of the function is the same)
    ministry: Optional[str] = None,
    organization: Optional[str] = None,
    skip: int = Query(default=0, ge=0),
    limit: int = Query(default=100, ge=1, le=1000), # Added validation with Query
    pdf_analysis_status: Optional[PDFAnalysisStatusEnum] = Query(default=None, description="Filter by PDF analysis status"),
    pdf_processing_session: Optional[str] = Query(default=None, description="Filter by PDF processing session"),
    db: Session = Depends(get_db)
):
    db_bids = crud_bid.get_bids(
        db,
        skip=skip,
        limit=limit,
        ministry=ministry,
        organization=organization,
        pdf_status=pdf_analysis_status,
        pdf_processing_session=pdf_processing_session
    )
    return db_bids # Returns empty list if none found, which is fine

@router.get(
    "/bids/recent-scrape/{session_id}",
    response_model=List[bid_schemas.BidInDB],
    summary="Get bids from a specific scraping session"
)
async def get_bids_by_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """Get all bids that were scraped in a specific session (task_id)"""
    db_bids = crud_bid.get_bids_by_session(db, session_id=session_id)
    return db_bids

@router.get(
    "/bids/by-source/{source_type}",
    response_model=List[bid_schemas.BidInDB],
    summary="Get bids filtered by scrape source type"
)
async def get_bids_by_source_type(
    source_type: ScrapeSourceTypeEnum,
    db: Session = Depends(get_db)
):
    """Get all bids that were scraped from a specific source type (ministry_organization or location)"""
    db_bids = crud_bid.get_bids_by_source_type(db, source_type=source_type)
    return db_bids

@router.get(
    "/bids/latest-session",
    response_model=List[bid_schemas.BidInDB],
    summary="Get bids from the most recent scraping sessions"
)
async def get_latest_scraped_bids(
    hours: int = Query(default=1, ge=1, le=24, description="Hours threshold for recent bids"),
    db: Session = Depends(get_db)
):
    """
    Get all bids from recent scraping sessions within the specified hours threshold.
    This ensures we get all bids from a complete scraping queue (multiple tasks).
    Default is 1 hour, which should cover most normal scraping operations.
    """
    db_bids = crud_bid.get_latest_scraped_bids(db, hours_threshold=hours)
    return db_bids

@router.get(
    "/bids/all-from-latest-sources",
    response_model=List[bid_schemas.BidInDB],
    summary="Get ALL bids (new + old) from the same sources as recent scraping sessions"
)
async def get_all_bids_from_latest_sources(
    hours: int = Query(default=1, ge=1, le=24, description="Hours threshold for recent sources"),
    db: Session = Depends(get_db)
):
    """
    Get ALL bids (new + old) from the same sources as recent scraping sessions.
    Uses time-based approach to get sources from recent scraping sessions.
    Default is 1 hour, which should cover most normal scraping operations.
    """
    db_bids = crud_bid.get_all_bids_from_latest_scrape_sources(db, hours_threshold=hours)
    return db_bids

@router.get(
    "/bids/scrape-sources",
    summary="Get unique scrape sources for filtering"
)
async def get_scrape_sources(db: Session = Depends(get_db)):
    """Get unique scrape sources to populate filter buttons"""
    sources = crud_bid.get_unique_scrape_sources(db)
    return sources

# GET /bids/{bid_number} (remains the same)
@router.get(
    "/bids/{bid_number}",
    response_model=bid_schemas.BidInDB,
    summary="Get a specific bid by its bid_number"
)
async def read_specific_bid(bid_number: str, db: Session = Depends(get_db)): # ... (rest of function is the same)
    db_bid = crud_bid.get_bid_by_internal_id(db, bid_id_internal=bid_number)
    if db_bid is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Bid with number '{bid_number}' not found")
    return db_bid

class CheckPdfDirectoryRequest(BaseModel):
    # Frontend provides the *subdirectory name* that was used for download
    # The backend resolves the full path based on DOWNLOADS_BASE_DIR
    target_subdirectory_name: str

class PdfCheckResult(BaseModel):
    filename: str
    status: str # "ACCEPT" or "REJECT"
    reasons: List[str] = []

class ProcessFilteredBidsRequest(BaseModel):
    bid_ids: List[int]  # List of internal bid IDs to process
    session_name: Optional[str] = None  # Optional name for this processing session


@router.post(
    "/bids/check-downloaded-pdfs",
    response_model=List[PdfCheckResult],
    summary="Check downloaded bid PDFs in a specified server-side subdirectory for EMD and restrictive clauses"
)
async def check_downloaded_pdfs_endpoint(
    request: CheckPdfDirectoryRequest # Expects target_subdirectory_name
):
    """
    Analyzes all PDF files within a specified subdirectory (relative to the
    server's DOWNLOADS_BASE_DIR) based on pre-defined criteria.
    This subdirectory name should correspond to one created by a download task.

    - **target_subdirectory_name**: The name of the folder within the server's
      base download directory (DOWNLOADS_BASE_DIR) containing the PDFs to check.
      Example: "filtered_bids_2025-05-10_sometaskid"
    """
    print(f"API: Received request to check PDFs in server subdirectory: '{request.target_subdirectory_name}'")

    # Path construction
    # The target_subdirectory_name IS the folder directly under DOWNLOADS_BASE_DIR
    # It should NOT be sanitized again here if it was already a server-generated name.
    # If it can be arbitrary user input, then sanitization IS needed.
    # Let's assume it's a name derived from task_id and user_prefix, so it's somewhat controlled.

    directory_to_check = os.path.join(settings.DOWNLOADS_BASE_DIR, request.target_subdirectory_name)

    print(f"API: Checking PDFs in resolved server path: {directory_to_check}")

    if not os.path.isdir(directory_to_check):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND,
                            detail=f"Directory '{request.target_subdirectory_name}' not found under base download path on server.")

    results: List[PdfCheckResult] = []
    processed_files_count = 0
    try:
        for filename in os.listdir(directory_to_check):
            if filename.lower().endswith(".pdf"):
                pdf_path = os.path.join(directory_to_check, filename)
                print(f"  Checking PDF file: {pdf_path}")

                # Use the analysis function from pdf_processor_service
                analysis_status, analysis_reasons = pdf_processor_service.check_bid_pdf(pdf_path)

                results.append(PdfCheckResult(
                    filename=filename,
                    status=analysis_status,
                    reasons=analysis_reasons
                    # new_path is not relevant here as we are not moving files with this endpoint anymore
                ))
                processed_files_count += 1
            else:
                print(f"  Skipping non-PDF file: {filename}")

    except Exception as e:
         print(f"API: Error listing or accessing directory {directory_to_check}: {e}")
         raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to process directory {directory_to_check}.")

    if processed_files_count == 0 and not results: # Check if any PDFs were actually found and processed
        print(f"API: No PDF files found to process in directory '{request.target_subdirectory_name}'.")
        # Optionally return an empty list or a specific message
        # return []
        # Or raise an error if this is unexpected:
        # raise HTTPException(status_code=404, detail=f"No PDF files found in directory: {request.target_subdirectory_name}")


    print(f"API: PDF check complete for directory '{request.target_subdirectory_name}'. Processed {processed_files_count} PDFs.")
    return results


@router.post( # Or use DELETE, but POST is fine for triggering an action
    "/bids/delete-expired",
    status_code=status.HTTP_200_OK, # Return OK after completion (can be long running)
    summary="Delete bids where the end date has passed (before today)"
)
async def delete_expired_bids_endpoint(
    db: Session = Depends(get_db)
    # Consider adding BackgroundTasks if deleting thousands of bids might take too long
    # background_tasks: BackgroundTasks
):
    """
    Deletes all bids from the database whose `end_date` is strictly before
    the current date (local server time). Bids ending today are NOT deleted.

    Note: This operation can take time if there are many bids to delete and
    might be better as a background task for very large datasets.
    """
    print("API: Received request to delete expired bids.")

    try:
        # Perform deletion synchronously for now
        deleted_count = crud_bid.delete_expired_bids(db)

        # If it needed to be a background task:
        # task_id = str(uuid.uuid4())
        # TASK_STATUS_STORE[task_id] = TaskInfo(task_id=task_id, message="Expired bid deletion initiated.")
        # background_tasks.add_task(crud_bid.delete_expired_bids, db) # Need wrapper if status update needed
        # return {"message": "Expired bid deletion initiated in background.", "task_id": task_id}

    except Exception as e:
         print(f"API: Error during expired bid deletion: {e}")
         # Rollback might happen in crud function's exception handler if you added one
         # db.rollback()
         raise HTTPException(
             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
             detail=f"An error occurred during bid deletion: {e}"
         )

    return {
        "message": "Expired bid deletion process completed.",
        "deleted_count": deleted_count
    }


@router.get(
    "/bids/download-archive/{task_id_or_filename}",
    summary="Download a generated ZIP archive of bid documents",
    response_class=FileResponse # Tells FastAPI to expect a file response
)
async def download_zip_archive(task_id_or_filename: str):
    """
    Downloads a previously generated ZIP archive.
    The `task_id_or_filename` can be the task_id (if we look up filename from TASK_STATUS_STORE)
    or the direct filename if known by the client.
    For simplicity, let's assume it's the filename for now.
    """
    print(f"API: Request to download archive: {task_id_or_filename}")

    # Construct path assuming task_id_or_filename IS the zip_filename
    # Basic sanitization for filename to prevent path traversal, although it should be server-generated
    sane_filename = file_downloader_service.sanitize_filename(task_id_or_filename)
    if not sane_filename.endswith(".zip"): # Ensure it's a zip file request
        sane_filename += ".zip" # Or raise error if not a zip

    zip_file_path = os.path.join(settings.TEMP_ZIP_DIR, sane_filename)

    if not os.path.exists(zip_file_path):
        # Alternative: if task_id_or_filename is a task_id, look up in TASK_STATUS_STORE
        # task_info = TASK_STATUS_STORE.get(task_id_or_filename)
        # if task_info and task_info.zip_filename:
        #     zip_file_path = os.path.join(settings.TEMP_ZIP_DIR, task_info.zip_filename)
        #     if not os.path.exists(zip_file_path):
        #          raise HTTPException(status_code=404, detail=f"Archive file '{task_info.zip_filename}' not found for task.")
        # else:
        #      raise HTTPException(status_code=404, detail="Archive not found or task ID invalid.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Archive file '{sane_filename}' not found.")

    return FileResponse(
        path=zip_file_path,
        media_type='application/zip',
        filename=sane_filename # This suggests the download name to the browser
    )

class PDFProcessResultItem(BaseModel):
    filename: str
    original_filename: str
    status: str # "ACCEPTED" or "REJECTED"
    reasons: List[str] = []
    new_path: Optional[str] = None # Server path where it was moved

class PDFBatchProcessResponse(BaseModel):
    task_id: str
    message: str
    total_processed: int
    total_accepted: int
    total_rejected: int
    accepted_dir: str # Relative server path
    rejected_dir: str # Relative server path
    results: List[PDFProcessResultItem]


def run_background_pdf_processing_and_sorting(
    task_id: str,
    uploaded_file_paths: List[Tuple[str, str]], # List of (temp_server_path, original_filename)
    # No db session needed if not updating bid status based on PDF check directly
):
    """
    Process uploaded PDF files in the background, analyze them, and sort them into accepted/rejected folders.
    Supports cancellation via the should_task_cancel function if available.
    """
    # Define dummy functions if they don't exist
    def dummy_register_task(task_id):
        print(f"Warning: Using dummy register_background_task for task {task_id}")
        return

    def dummy_check_cancel(_):
        return False

    # Use the real functions if they exist, otherwise use dummies
    register_task_fn = globals().get('register_background_task', dummy_register_task)
    check_cancel_fn = globals().get('should_task_cancel', dummy_check_cancel)

    # Register this task as active
    try:
        register_task_fn(task_id)
    except Exception as e:
        print(f"Warning: Failed to register task {task_id}: {e}")

    # Initialize task status
    try:
        update_task_status(task_id, status=TaskStatus.RUNNING, message="PDF processing started...", stage="Initialization")
        print(f"Background PDF Process Task (ID: {task_id}): Starting for {len(uploaded_file_paths)} files.")
    except Exception as e:
        print(f"Error initializing PDF processing task: {e}")
        return

    # Check for early cancellation
    try:
        if check_cancel_fn(task_id):
            update_task_status(task_id, status=TaskStatus.FAILED, message="Task cancelled by user before processing started.", error_msg="Task cancelled by user")
            print(f"Background PDF Process Task (ID: {task_id}): Cancelled before processing started.")
            return
    except Exception as e:
        print(f"Warning: Error checking for task cancellation: {e}")

    # Initialize variables
    processed_results = []
    accepted_count = 0
    rejected_count = 0
    task_result_base_dir_name = f"pdf_results_{task_id}"
    task_result_full_path = None
    accepted_dir = None
    rejected_dir = None

    # Create directories
    try:
        # Create unique subdirectories for this task's results
        task_result_full_path = os.path.join(settings.PDF_PROCESSING_BASE_DIR, task_result_base_dir_name)
        accepted_dir = os.path.join(task_result_full_path, "accepted_pdfs")
        rejected_dir = os.path.join(task_result_full_path, "rejected_pdfs")
        os.makedirs(accepted_dir, exist_ok=True)
        os.makedirs(rejected_dir, exist_ok=True)
    except Exception as e:
        error_msg = f"Error creating directories for PDF processing: {e}"
        print(error_msg)
        update_task_status(task_id, status=TaskStatus.FAILED, message=error_msg, error_msg=str(e))
        return

    # Process each uploaded file
    try:
        for i, (temp_pdf_path, original_filename) in enumerate(uploaded_file_paths):
            # Check for cancellation before each file
            try:
                if check_cancel_fn(task_id):
                    update_task_status(task_id, status=TaskStatus.FAILED,
                                      message=f"Task cancelled by user after processing {i} files.",
                                      error_msg="Task cancelled by user")
                    print(f"Background PDF Process Task (ID: {task_id}): Cancelled after processing {i} files.")
                    return
            except Exception as e:
                print(f"Warning: Error checking for task cancellation during processing: {e}")

            # Update status for current file
            update_task_status(task_id, stage="Processing PDF", current=i+1,
                              total=len(uploaded_file_paths),
                              message=f"Analyzing {original_filename}...")
            print(f"  Processing: {original_filename} (from {temp_pdf_path})")

            # Check if file exists
            if not os.path.exists(temp_pdf_path):
                print(f"    Error: Temp file {temp_pdf_path} not found. Skipping.")
                processed_results.append(PDFProcessResultItem(
                    filename=os.path.basename(temp_pdf_path),
                    original_filename=original_filename,
                    status="ERROR_FILE_NOT_FOUND",
                    reasons=["Temporary uploaded file disappeared before processing."]
                ))
                rejected_count += 1
                continue

        status, rejection_reasons = pdf_processor_service.check_bid_pdf(temp_pdf_path)

        new_file_path_on_server = None
        try:
            # Use original filename for saving to sorted folders (sanitized)
            sane_original_filename = file_downloader_service.sanitize_filename(original_filename)
            if not sane_original_filename: sane_original_filename = f"processed_file_{i}.pdf"
            if not sane_original_filename.lower().endswith(".pdf"): sane_original_filename+=".pdf"


            if status == "ACCEPT":
                destination_path = os.path.join(accepted_dir, sane_original_filename)
                # Ensure unique filename in destination
                counter = 1
                temp_dest_path = destination_path
                name_part, ext_part = os.path.splitext(sane_original_filename)
                while os.path.exists(temp_dest_path):
                    temp_dest_path = os.path.join(accepted_dir, f"{name_part}_{counter}{ext_part}")
                    counter +=1
                destination_path = temp_dest_path

                shutil.move(temp_pdf_path, destination_path)
                new_file_path_on_server = destination_path
                accepted_count += 1
                print(f"    -> ACCEPTED. Moved to: {destination_path}")
            else: # REJECT or Error
                destination_path = os.path.join(rejected_dir, sane_original_filename)
                # Ensure unique filename in destination
                counter = 1
                temp_dest_path = destination_path
                name_part, ext_part = os.path.splitext(sane_original_filename)
                while os.path.exists(temp_dest_path):
                    temp_dest_path = os.path.join(rejected_dir, f"{name_part}_{counter}{ext_part}")
                    counter +=1
                destination_path = temp_dest_path

                shutil.move(temp_pdf_path, destination_path)
                new_file_path_on_server = destination_path
                rejected_count += 1
                print(f"    -> REJECTED. Moved to: {destination_path}. Reasons: {rejection_reasons}")

            processed_results.append(PDFProcessResultItem(
                filename=os.path.basename(new_file_path_on_server), # The name it has in sorted folder
                original_filename=original_filename,
                status=status,
                reasons=rejection_reasons,
                new_path=new_file_path_on_server # Server path for reference
            ))

        except Exception as move_error:
            print(f"    Error moving file {temp_pdf_path}: {move_error}")
            # File remains in temp upload dir or processing failed before move
            processed_results.append(PDFProcessResultItem(
                filename=os.path.basename(temp_pdf_path), # Or original_filename
                original_filename=original_filename,
                status="ERROR_MOVING_FILE",
                reasons=[f"Analysis: {status} ({rejection_reasons})", f"Move Error: {move_error}"]
            ))
            rejected_count +=1 # Count as rejected/error
            # Ensure temp file is cleaned up if not moved
            if os.path.exists(temp_pdf_path):
                try: os.remove(temp_pdf_path)
                except OSError: pass

    except Exception as e:
        # Handle any errors in the main processing loop
        error_msg = f"Error during PDF processing: {e}"
        print(error_msg)
        update_task_status(task_id, status=TaskStatus.FAILED, message=error_msg, error_msg=str(e))
        return

    # Complete the task successfully
    final_message = f"PDF processing complete. Accepted: {accepted_count}, Rejected/Errors: {rejected_count}."

    # Use model_dump instead of dict for Pydantic v2 compatibility
    try:
        detailed_results_for_task = {
            "processed_results": [r.dict() for r in processed_results],
            "accepted_dir_rel": os.path.join(task_result_base_dir_name, "accepted_pdfs"),
            "rejected_dir_rel": os.path.join(task_result_base_dir_name, "rejected_pdfs")
        }
    except AttributeError:
        # Fallback for newer Pydantic versions
        try:
            detailed_results_for_task = {
                "processed_results": [r.model_dump() for r in processed_results],
                "accepted_dir_rel": os.path.join(task_result_base_dir_name, "accepted_pdfs"),
                "rejected_dir_rel": os.path.join(task_result_base_dir_name, "rejected_pdfs")
            }
        except AttributeError:
            # Last resort fallback
            detailed_results_for_task = {
                "processed_results": [{"filename": r.filename, "status": r.status, "reasons": r.reasons} for r in processed_results],
                "accepted_dir_rel": os.path.join(task_result_base_dir_name, "accepted_pdfs"),
                "rejected_dir_rel": os.path.join(task_result_base_dir_name, "rejected_pdfs")
            }

    update_task_status(
        task_id,
        status=TaskStatus.COMPLETED,
        message=final_message,
        count=accepted_count,
        stage="Completed",
        result_dtl=detailed_results_for_task
    )
    print(f"Background PDF Process Task (ID: {task_id}): {final_message}")
    # The temp_pdf_uploads directory itself is not deleted per task, only its content is moved.
    # You might want a separate cron job or manual process to clean old empty task folders in PDF_UPLOAD_TEMP_DIR.


def run_background_filtered_bids_processing(
    db: Session, bid_ids: List[int], session_name: str, task_id: str
):
    """Background task to download and process PDFs for filtered bids"""
    register_background_task(task_id)

    try:
        update_task_status(
            task_id,
            status=TaskStatus.RUNNING,
            message=f"Starting processing of {len(bid_ids)} filtered bids",
            stage="Initializing",
            current=0,
            total=len(bid_ids)
        )

        # Get bid details from database and filter out already processed ones
        bids_to_process = []
        already_processed_count = 0

        for bid_id in bid_ids:
            bid = crud_bid.get_bid_by_id(db, bid_id=bid_id)
            if bid:
                # Check if already processed (has a PDF analysis status other than PENDING)
                if bid.pdf_analysis_status and bid.pdf_analysis_status != PDFAnalysisStatusEnum.PENDING:
                    already_processed_count += 1
                    print(f"Skipping bid {bid.bid_number} - already processed with status: {bid.pdf_analysis_status}")
                else:
                    bids_to_process.append(bid)

        if not bids_to_process:
            final_message = f"No bids to process. {already_processed_count} already processed, {len(bid_ids) - len(bids_to_process) - already_processed_count} not found."
            update_task_status(
                task_id,
                status=TaskStatus.COMPLETED,
                message=final_message,
                stage="Completed",
                current=len(bid_ids),
                total=len(bid_ids),
                count=0
            )
            return

        print(f"Processing {len(bids_to_process)} bids, skipping {already_processed_count} already processed")

        # Create temporary directory for downloads
        temp_dir = tempfile.mkdtemp(prefix=f"filtered_bids_{task_id}_")
        processed_results = []
        accepted_count = 0

        # Create requests session for PDF downloads
        pdf_session = requests.Session()

        update_task_status(
            task_id,
            status=TaskStatus.RUNNING,
            message="Downloading and processing PDFs",
            stage="Processing",
            current=0,
            total=len(bids_to_process)
        )

        for i, bid in enumerate(bids_to_process):
            try:
                # Update progress
                update_task_status(
                    task_id,
                    status=TaskStatus.RUNNING,
                    message=f"Processing bid {bid.bid_number}",
                    stage="Processing",
                    current=i + 1,
                    total=len(bids_to_process)
                )

                # Download PDF using proper file downloader service
                pdf_path = None
                if bid.bid_details_url:
                    try:
                        # Use the proper file downloader service
                        sane_bid_number_filename_stem = file_downloader_service.sanitize_filename(bid.bid_number)

                        pdf_path = file_downloader_service.download_file(
                            pdf_session,
                            bid.bid_details_url,
                            target_dir=temp_dir,
                            base_filename=sane_bid_number_filename_stem
                        )

                        if not pdf_path or not os.path.exists(pdf_path):
                            raise Exception("Download failed - no file returned")

                    except Exception as e:
                        print(f"Failed to download PDF for bid {bid.bid_number}: {e}")
                        # Update bid status in database
                        crud_bid.update_bid_pdf_analysis(
                            db,
                            bid_id=bid.id,
                            status=PDFAnalysisStatusEnum.NO_DOCUMENT_URL,
                            reasons=f"Download failed: {str(e)}",
                            processing_session=session_name
                        )
                        processed_results.append({
                            "bid_id": bid.id,
                            "bid_number": bid.bid_number,
                            "status": "NO_DOCUMENT_URL",
                            "reasons": [f"Download failed: {str(e)}"]
                        })
                        continue

                # Process PDF if downloaded
                if pdf_path and os.path.exists(pdf_path):
                    try:
                        # Use our PDF processing service
                        from app.services.pdf_processor_service import analyze_with_rules
                        from app.config.criteria_config import DEFAULT_CRITERIA

                        result = analyze_with_rules(pdf_path, DEFAULT_CRITERIA)

                        # Update bid in database
                        status = PDFAnalysisStatusEnum.ACCEPTED if result.status == "ACCEPT" else PDFAnalysisStatusEnum.REJECTED
                        reasons_text = "; ".join(result.reasons) if result.reasons else ""

                        crud_bid.update_bid_pdf_analysis(
                            db,
                            bid_id=bid.id,
                            status=status,
                            reasons=reasons_text,
                            processing_session=session_name
                        )

                        if result.status == "ACCEPT":
                            accepted_count += 1

                        processed_results.append({
                            "bid_id": bid.id,
                            "bid_number": bid.bid_number,
                            "status": result.status,
                            "reasons": result.reasons,
                            "confidence": result.confidence_score
                        })

                    except Exception as e:
                        print(f"Failed to process PDF for bid {bid.bid_number}: {e}")
                        crud_bid.update_bid_pdf_analysis(
                            db,
                            bid_id=bid.id,
                            status=PDFAnalysisStatusEnum.PROCESSING_ERROR,
                            reasons=f"Processing error: {str(e)}",
                            processing_session=session_name
                        )
                        processed_results.append({
                            "bid_id": bid.id,
                            "bid_number": bid.bid_number,
                            "status": "PROCESSING_ERROR",
                            "reasons": [f"Processing error: {str(e)}"]
                        })
                else:
                    # No bid_details_url
                    crud_bid.update_bid_pdf_analysis(
                        db,
                        bid_id=bid.id,
                        status=PDFAnalysisStatusEnum.NO_DOCUMENT_URL,
                        reasons="No bid details URL available",
                        processing_session=session_name
                    )
                    processed_results.append({
                        "bid_id": bid.id,
                        "bid_number": bid.bid_number,
                        "status": "NO_DOCUMENT_URL",
                        "reasons": ["No bid details URL available"]
                    })

            except Exception as e:
                print(f"Error processing bid {bid.bid_number}: {e}")
                processed_results.append({
                    "bid_id": bid.id,
                    "bid_number": bid.bid_number,
                    "status": "PROCESSING_ERROR",
                    "reasons": [f"Unexpected error: {str(e)}"]
                })

        # Clean up temporary directory and session
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"Failed to clean up temp directory {temp_dir}: {e}")

        try:
            pdf_session.close()
        except Exception as e:
            print(f"Failed to close PDF session: {e}")

        # Final status update
        final_message = f"Processed {len(bids_to_process)} bids. {accepted_count} accepted, {len(bids_to_process) - accepted_count} rejected/failed."

        update_task_status(
            task_id,
            status=TaskStatus.COMPLETED,
            message=final_message,
            stage="Completed",
            current=len(bids_to_process),
            total=len(bids_to_process),
            count=accepted_count,
            result_dtl={
                "session_name": session_name,
                "processed_results": processed_results,
                "accepted_count": accepted_count,
                "total_processed": len(bids_to_process)
            }
        )

        print(f"Background Filtered Bids Processing Task (ID: {task_id}): {final_message}")

    except Exception as e:
        error_msg = f"Fatal error in filtered bids processing: {str(e)}"
        print(f"Background Filtered Bids Processing Task (ID: {task_id}) ERROR: {error_msg}")
        update_task_status(
            task_id,
            status=TaskStatus.FAILED,
            message="Processing failed due to unexpected error",
            error_msg=error_msg
        )
    finally:
        unregister_background_task(task_id)


@router.post(
    "/bids/process-filtered-bids",
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process PDFs for filtered bids (download and analyze)"
)
async def process_filtered_bids_endpoint(
    request: ProcessFilteredBidsRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Download and process PDFs for a list of filtered bids.

    This endpoint:
    1. Takes a list of bid IDs from the filtered results
    2. Downloads the PDF for each bid
    3. Processes each PDF using our learned criteria
    4. Updates the bid's PDF analysis status in the database
    5. Returns a task ID for tracking progress
    """
    if not request.bid_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="List of bid_ids cannot be empty."
        )

    # Generate session name if not provided
    session_name = request.session_name or f"filtered_session_{int(time.time())}"

    task_id = str(uuid.uuid4())
    initial_details = {
        "session_name": session_name,
        "num_bids": len(request.bid_ids),
        "bid_ids": request.bid_ids
    }

    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message=f"Filtered bids processing initiated for {len(request.bid_ids)} bids.",
        details=initial_details
    )

    print(f"API: Filtered bids processing request for {len(request.bid_ids)} bids. Session: '{session_name}', Task ID: {task_id}")

    background_tasks.add_task(
        run_background_filtered_bids_processing,
        db,
        request.bid_ids,
        session_name,
        task_id
    )

    return {
        "message": "Filtered bids processing initiated in background.",
        "task_id": task_id,
        "session_name": session_name,
        "details": initial_details
    }


@router.post(
    "/bids/upload-and-process-pdfs",
    status_code=status.HTTP_202_ACCEPTED,
    summary="Upload multiple PDF files for analysis, sorting, and EMD/clause checking"
)
async def upload_and_process_pdfs_endpoint(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db), # Still need DB session if the background task uses it via its first arg
):
    if not files:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No PDF files uploaded.")

    os.makedirs(settings.PDF_UPLOAD_TEMP_DIR, exist_ok=True)
    uploaded_file_details: List[Tuple[str, str]] = []

    for file in files:
        if not file.filename:
            safe_filename = f"uploaded_file_{uuid.uuid4().hex}.pdf"
        else:
            safe_filename = file_downloader_service.sanitize_filename(file.filename)
            if not safe_filename.lower().endswith(".pdf"):
                 print(f"Skipping non-PDF file based on name: {file.filename}")
                 continue

        temp_file_id = uuid.uuid4().hex
        temp_file_path = os.path.join(settings.PDF_UPLOAD_TEMP_DIR, f"{temp_file_id}_{safe_filename}")

        try:
            with open(temp_file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            uploaded_file_details.append((temp_file_path, file.filename or safe_filename))
            print(f"API: Successfully saved uploaded file to temp path: {temp_file_path}")
        except Exception as e:
            print(f"API: Error saving uploaded file {file.filename}: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Could not save uploaded file: {file.filename}")
        finally:
            file.file.close()

    if not uploaded_file_details:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No valid PDF files were processed from the upload.")

    task_id = str(uuid.uuid4())
    initial_details = {"num_files_uploaded": len(uploaded_file_details)}
    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message=f"PDF batch processing initiated for {len(uploaded_file_details)} files.",
        details=initial_details
    )

    # The background task run_background_pdf_processing_and_sorting
    # does not currently take a 'db: Session' argument. If it did, you'd pass it here.
    # For now, it only needs task_id and uploaded_file_details.
    background_tasks.add_task(
        run_background_pdf_processing_and_sorting,
        task_id,
        uploaded_file_details
    )

    return {
        "message": "PDF processing and sorting initiated in background.",
        "task_id": task_id,
        "files_received": len(uploaded_file_details)
    }


class DrawingFilterExportRequest(BaseModel):
    drawing_keywords: List[str]
    # Optional: add ministry/org pre-filters if needed for the initial dataset
    # ministry_filter: Optional[str] = None
    # organization_filter: Optional[str] = None

@router.post(
    "/bids/export/drawing-filter-excel",
    summary="Filters bids by drawing number keywords and returns an Excel file",
    response_class=StreamingResponse # Important for file downloads
)
async def export_drawing_filtered_bids_to_excel(
    request: DrawingFilterExportRequest, # Takes drawing keywords in request body
    db: Session = Depends(get_db)
):
    """
    Filters bids based on drawing number keywords found in the 'items' field
    and exports the results to an Excel (XLSX) file.
    """
    print(f"API: Received request to export drawing-filtered bids to Excel. Keywords: {request.drawing_keywords}")

    if not request.drawing_keywords:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Drawing keywords list cannot be empty.")

    # 1. Fetch all bids (or apply broader pre-filters if provided in request)
    # As before, fetching all can be memory intensive for very large DBs.
    all_db_bids_sqla = crud_bid.get_bids(db, limit=50000, skip=0) # Fetch SQLAlchemy models

    if not all_db_bids_sqla:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No bids found in the database to filter.")

    # 2. Apply keyword filtering (using existing filter service)
    # The filter_service expects Pydantic models or objects with .items attribute.
    # SQLAlchemy models have this.
    filtered_bids_sqla = filter_service.filter_bids_by_keywords_in_items(
        bids=all_db_bids_sqla, # Pass SQLAlchemy models directly
        keywords=request.drawing_keywords
    )

    if not filtered_bids_sqla:
        # Return an empty Excel file or a specific response
        # For now, let's use the service to generate an empty Excel with headers
        empty_excel_buffer = excel_exporter_service.generate_excel_for_drawing_filter([])
        return StreamingResponse(
            empty_excel_buffer,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=filtered_drawing_bids_empty.xlsx"}
        )
        # Alternatively: raise HTTPException(status_code=404, detail="No bids found matching drawing keywords.")


    # 3. Convert filtered SQLAlchemy models to Pydantic BidInDB models for the Excel generator
    # (Our excel_exporter_service currently expects List[bid_schemas.BidInDB])
    filtered_bids_pydantic: List[bid_schemas.BidInDB] = []
    for bid_sqla in filtered_bids_sqla:
        try:
            filtered_bids_pydantic.append(bid_schemas.BidInDB.from_orm(bid_sqla))
        except Exception as e:
            print(f"Error converting SQLAlchemy model to Pydantic for bid {bid_sqla.bid_number}: {e}")
            # Optionally skip this bid or handle error
            continue

    if not filtered_bids_pydantic: # If conversion failed for all
         raise HTTPException(status_code=500, detail="Error processing bid data for Excel export.")


    # 4. Generate Excel file in memory
    try:
        excel_bytes_io = excel_exporter_service.generate_excel_for_drawing_filter(filtered_bids_pydantic)
    except Exception as e:
        print(f"Error generating Excel file: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate Excel file.")

    # 5. Stream the Excel file as a response
    filename = f"filtered_drawing_bids.xlsx"
    return StreamingResponse(
        excel_bytes_io,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )



@router.post(
"/bids/export-update/drawing-filter-excel", # New or modified path
summary="Filters bids by drawing keywords, optionally updates an existing Excel, and returns Excel file",
response_class=StreamingResponse
)
async def export_update_drawing_filtered_bids_excel(
    db: Session = Depends(get_db),
    # Keywords will come as a form field, which we'll parse
    drawing_keywords_str: str = Form(..., description="Comma-separated drawing keywords"),
    existing_excel_file: Optional[UploadFile] = File(default=None, description="Optional existing .xlsx file to update")
):
    """
    Filters bids by drawing keywords. If an existing Excel file is uploaded,
    it updates that file; otherwise, a new Excel file is generated.
    The result is streamed back as an .xlsx file.
    """
    print(f"API: Excel export/update request. Keywords string: '{drawing_keywords_str}'")
    if existing_excel_file:
        print(f"API: Existing Excel file uploaded: {existing_excel_file.filename}")

    try:
        drawing_keywords = [kw.strip() for kw in drawing_keywords_str.split(',') if kw.strip()]
        if not drawing_keywords:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Drawing keywords list cannot be empty.")
    except Exception:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid format for drawing_keywords_str. Should be comma-separated.")

    # 1. Fetch and filter bids from DB
    all_db_bids_sqla = crud_bid.get_bids(db, limit=50000, skip=0)
    if not all_db_bids_sqla:
        # Generate and return an empty Excel if no bids in DB
        empty_excel_buffer = excel_exporter_service.generate_or_update_excel_drawing_filter([])
        return StreamingResponse(
            empty_excel_buffer,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=empty_filtered_bids.xlsx"}
        )

    filtered_bids_sqla = filter_service.filter_bids_by_keywords_in_items(
        bids=all_db_bids_sqla, keywords=drawing_keywords
    )

    # Convert to Pydantic models for the excel service
    filtered_bids_pydantic: List[bid_schemas.BidInDB] = []
    for bid_sqla in filtered_bids_sqla:
        try:
            filtered_bids_pydantic.append(bid_schemas.BidInDB.from_orm(bid_sqla))
        except Exception as e:
            print(f"Error converting SQLAlchemy model to Pydantic for Excel: {bid_sqla.bid_number}: {e}")
            continue # Skip bids that fail conversion

    # Handle uploaded existing Excel file
    existing_excel_bytes_content: Optional[bytes] = None
    if existing_excel_file:
        try:
            existing_excel_bytes_content = await existing_excel_file.read()
            print(f"API: Read {len(existing_excel_bytes_content)} bytes from uploaded Excel.")
        except Exception as e:
            print(f"API Error: Could not read uploaded Excel file: {e}")
            # Optionally, proceed without it or return an error
            # For now, proceed as if no file was uploaded
            existing_excel_bytes_content = None
        finally:
            if existing_excel_file:
                 await existing_excel_file.close()


    # 2. Generate or Update Excel
    try:
        excel_bytes_io = excel_exporter_service.generate_or_update_excel_drawing_filter(
            filtered_bids_pydantic,
            existing_excel_bytes_content
        )
    except Exception as e:
        print(f"Error generating/updating Excel file: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to generate/update Excel: {e}")

    # 3. Stream the Excel file
    output_filename_prefix = "updated_drawing_bids" if existing_excel_bytes_content else "new_drawing_bids"
    filename = f"{output_filename_prefix}_.xlsx"

    return StreamingResponse(
        excel_bytes_io,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


def run_daily_workflow_task(db: Session, task_id: str, bid_numbers: List[str]):
    update_task_status(task_id, status=TaskStatus.RUNNING, message="Daily workflow started: Processing specified bids...", stage="Processing Bids")
    print(f"Daily Workflow Task (ID: {task_id}): Starting with {len(bid_numbers)} bid numbers...")

    pdf_accepted_count = 0
    pdf_rejected_count = 0
    pdf_no_url_count = 0
    pdf_download_error_count = 0
    pdf_analysis_error_count = 0
    bids_skipped_already_processed = 0 # New counter

    try:
        if not bid_numbers:
            err_msg = "No bid numbers provided for processing."
            update_task_status(task_id, status=TaskStatus.FAILED, message=err_msg, error_msg="Empty bid_numbers list.")
            print(f"  Daily Workflow Task (ID: {task_id}): {err_msg}")
            return

        print(f"  Daily Workflow: Processing bids: {bid_numbers}")

        # Fetch the specific bids by their bid numbers
        specified_bids_sqla = []
        for bid_number in bid_numbers:
            bid = crud_bid.get_bid_by_bid_number(db, bid_number=bid_number)
            if bid:
                specified_bids_sqla.append(bid)
            else:
                print(f"  Warning: Bid number {bid_number} not found in database.")

        if not specified_bids_sqla:
            update_task_status(task_id, status=TaskStatus.COMPLETED, message="None of the specified bid numbers were found in the database.", count=0)
            print(f"  Daily Workflow Task (ID: {task_id}): No valid bids found.")
            return

        # --- MODIFICATION: Filter for bids that need PDF analysis ---
        bids_requiring_pdf_analysis: List[models.Bid] = []
        for bid_model in specified_bids_sqla:
            if bid_model.pdf_analysis_status is None or bid_model.pdf_analysis_status == PDFAnalysisStatusEnum.PENDING:
                bids_requiring_pdf_analysis.append(bid_model)
            else:
                bids_skipped_already_processed += 1

        if not bids_requiring_pdf_analysis:
            final_msg_skip = f"Daily workflow: All {len(specified_bids_sqla)} specified bids have already had PDF analysis. Skipped: {bids_skipped_already_processed}."
            update_task_status(task_id, status=TaskStatus.COMPLETED, message=final_msg_skip, count=0)
            print(f"  Daily Workflow Task (ID: {task_id}): {final_msg_skip}")
            return
        # --- END MODIFICATION ---

        total_bids_to_actually_process = len(bids_requiring_pdf_analysis)
        update_task_status(task_id,
                           message=f"Specified bids: {len(specified_bids_sqla)}. PDF analysis needed for: {total_bids_to_actually_process}. Starting...",
                           stage="PDF Analysis",
                           total=total_bids_to_actually_process,
                           current=0)
        print(f"  Daily Workflow: From {len(specified_bids_sqla)} specified bids, {total_bids_to_actually_process} require PDF analysis.")

        pdf_session = requests.Session()
        pdf_session.headers.update(settings.REQUEST_HEADERS)
        try:
            pdf_session.get(str(settings.GEM_INITIAL_PAGE_URL), timeout=(20,40))
        except Exception:
            print("  Daily Workflow: Warning - initial GET to GeM page failed during PDF processing setup.")

        task_temp_pdf_dir = os.path.join(settings.PDF_UPLOAD_TEMP_DIR, f"daily_workflow_{task_id}")
        os.makedirs(task_temp_pdf_dir, exist_ok=True)

        # Iterate only over bids_requiring_pdf_analysis
        for i, bid_sqla_model in enumerate(bids_requiring_pdf_analysis):
            update_task_status(task_id, current=i+1, message=f"Analyzing PDF for bid: {bid_sqla_model.bid_number}")
            print(f"    Processing PDF {i+1}/{total_bids_to_actually_process} for bid: {bid_sqla_model.bid_number} (DB ID: {bid_sqla_model.id})")

            doc_url = bid_sqla_model.bid_details_url
            current_pdf_status_enum = PDFAnalysisStatusEnum.PENDING # Will be updated
            current_analysis_reasons = []

            if not doc_url:
                # ... (handling no doc URL as before) ...
                print(f"      No document URL for bid {bid_sqla_model.bid_number}. Marking status.")
                current_pdf_status_enum = PDFAnalysisStatusEnum.NO_DOCUMENT_URL
                pdf_no_url_count += 1
                crud_bid.update_bid_pdf_analysis_status(db, bid_id=bid_sqla_model.id, status=current_pdf_status_enum)
                continue

            sane_bid_number_stem = file_downloader_service.sanitize_filename(bid_sqla_model.bid_number or f"bid_{bid_sqla_model.id}")
            temp_pdf_path = file_downloader_service.download_file(
                pdf_session, doc_url, task_temp_pdf_dir, sane_bid_number_stem
            )

            if not temp_pdf_path or not os.path.exists(temp_pdf_path):
                # ... (handling download failure as before) ...
                print(f"      Failed to download PDF for bid {bid_sqla_model.bid_number} from {doc_url}.")
                current_pdf_status_enum = PDFAnalysisStatusEnum.PROCESSING_ERROR
                current_analysis_reasons = ["Failed to download PDF."]
                pdf_download_error_count += 1
            else:
                try:
                    # Use the new PDF processing service with rules
                    from app.services.pdf_processor_service import analyze_with_rules
                    from app.config.criteria_config import DEFAULT_CRITERIA

                    result = analyze_with_rules(temp_pdf_path, DEFAULT_CRITERIA)

                    # Map result to database enum
                    if result.status == "ACCEPT":
                        current_pdf_status_enum = PDFAnalysisStatusEnum.ACCEPTED
                        pdf_accepted_count += 1
                    else:
                        current_pdf_status_enum = PDFAnalysisStatusEnum.REJECTED
                        pdf_rejected_count += 1

                    current_analysis_reasons = result.reasons if result.reasons else []
                except Exception as analysis_exc:
                    # ... (handling analysis exception as before) ...
                    print(f"      Error analyzing PDF {temp_pdf_path} for bid {bid_sqla_model.bid_number}: {analysis_exc}")
                    current_pdf_status_enum = PDFAnalysisStatusEnum.PROCESSING_ERROR
                    current_analysis_reasons = [f"Error during PDF analysis: {str(analysis_exc)[:100]}"]
                    pdf_analysis_error_count += 1
                finally:
                    if os.path.exists(temp_pdf_path):
                        try: os.remove(temp_pdf_path)
                        except OSError as e_remove: print(f"Warning: Could not remove temp PDF {temp_pdf_path}: {e_remove}")

            crud_bid.update_bid_pdf_analysis_status(
                db, bid_id=bid_sqla_model.id, status=current_pdf_status_enum, reasons=current_analysis_reasons
            )
            time.sleep(0.1)

        if os.path.exists(task_temp_pdf_dir): # Ensure directory exists before trying to remove
            try:
                shutil.rmtree(task_temp_pdf_dir)
                print(f"  Daily Workflow: Cleaned up temp PDF directory: {task_temp_pdf_dir}")
            except OSError as e_rm_main: print(f"Warning: Could not remove task temp PDF dir {task_temp_pdf_dir}: {e_rm_main}")

        total_errors = pdf_no_url_count + pdf_download_error_count + pdf_analysis_error_count
        final_message = (
            f"Daily workflow complete. Total specified bids: {len(specified_bids_sqla)}. "
            f"PDFs Analyzed this run: {total_bids_to_actually_process}. "
            f"Accepted: {pdf_accepted_count}, Rejected: {pdf_rejected_count}, "
            f"Errors/No URL: {total_errors}. Skipped (already processed): {bids_skipped_already_processed}."
        )
        update_task_status(task_id, status=TaskStatus.COMPLETED, message=final_message, count=pdf_accepted_count)
        print(f"  Daily Workflow Task (ID: {task_id}): {final_message}")

    except Exception as e:
        # ... (outer error handling as before) ...
        error_msg_detail = f"Daily Workflow Task (ID: {task_id}) Error: {e}"
        print(error_msg_detail)
        import traceback
        traceback.print_exc()
        update_task_status(task_id, status=TaskStatus.FAILED, message="An error occurred during the daily workflow.", error_msg=str(e))


class DailyWorkflowRequest(BaseModel):
    bid_numbers: List[str]

@router.post(
    "/workflows/trigger-daily",
    status_code=status.HTTP_202_ACCEPTED,
    summary="Triggers the daily workflow: analyze PDFs for specified bid numbers."
)
async def trigger_daily_workflow(
    request: DailyWorkflowRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db) # DB session needed for the background task
):
    if not request.bid_numbers:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="bid_numbers list cannot be empty.")

    task_id = str(uuid.uuid4())
    TASK_STATUS_STORE[task_id] = TaskInfo(
        task_id=task_id,
        message="Daily workflow initiated.",
        details={"workflow_name": "daily_pdf_analysis", "bid_count": len(request.bid_numbers)}
    )
    print(f"API: Daily workflow triggered for {len(request.bid_numbers)} bids. Task ID: {task_id}")

    background_tasks.add_task(run_daily_workflow_task, db, task_id, request.bid_numbers) # Pass bid numbers

    return {"message": "Daily workflow initiated in the background.", "task_id": task_id}

class ExcelFilterConditionType(str, Enum):
    PDF_PROCESSED = "pdf_processed"
    FILTERED_BIDS = "filtered_bids"


@router.post(
    "/bids/export-excel", # More generic endpoint name
    summary="Filters bids based on specified criteria, optionally updates an existing Excel, and returns Excel file",
    response_class=StreamingResponse
)
async def export_filtered_bids_to_excel_generic( # Renamed function
    db: Session = Depends(get_db),
    filter_condition_type: ExcelFilterConditionType = Form(..., description="Type of filter to apply: 'pdf_processed' or 'filtered_bids'"),
    keywords_str: Optional[str] = Form(default=None, description="Comma-separated keywords (used for filtering)"),
    bid_numbers: Optional[str] = Form(default=None, description="Comma-separated bid numbers to export (alternative to keywords)"),
    existing_excel_file: Optional[UploadFile] = File(default=None, description="Optional existing .xlsx file to update")
):
    print(f"API: Excel export request. Filter type: '{filter_condition_type}'. Keywords: '{keywords_str if keywords_str else 'N/A'}'. Bid numbers: '{bid_numbers if bid_numbers else 'N/A'}'")
    if existing_excel_file:
        print(f"API: Existing Excel file uploaded: {existing_excel_file.filename}")

    # Validate keywords or bid_numbers if required by filter_type
    keywords_list: List[str] = []
    bid_numbers_list: List[str] = []

    if filter_condition_type == ExcelFilterConditionType.FILTERED_BIDS:
        # For filtered bids export, we need either bid_numbers or keywords
        if bid_numbers and bid_numbers.strip():
            try:
                bid_numbers_list = [bn.strip() for bn in bid_numbers.split(',') if bn.strip()]
                if not bid_numbers_list:
                    raise ValueError("Empty bid numbers list after parsing.")
                print(f"API: Using specific bid numbers for filtered export: {len(bid_numbers_list)} bids")
            except Exception:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid format for bid_numbers. Should be comma-separated and non-empty.")
        elif keywords_str and keywords_str.strip():
            try:
                keywords_list = [kw.strip() for kw in keywords_str.split(',') if kw.strip()]
                if not keywords_list:
                    raise ValueError("Empty keywords list after parsing.")
                print(f"API: Using keywords for filtering: {keywords_list}")
            except Exception:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid format for keywords_str. Should be comma-separated and non-empty.")
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Either keywords_str or bid_numbers are required for filtered_bids export type.")
    # PDF_PROCESSED export doesn't need validation - it exports ALL bids

    # 1. Fetch initial set of bids based on filter_condition_type
    initial_bids_to_filter_sqla: List[models.Bid] = []

    if filter_condition_type == ExcelFilterConditionType.PDF_PROCESSED:
        # For PDF processed export, get ALL bids from database
        initial_bids_to_filter_sqla = crud_bid.get_bids(db, limit=50000, skip=0)
        print(f"API: Fetched {len(initial_bids_to_filter_sqla)} total bids for PDF processed export.")
    elif filter_condition_type == ExcelFilterConditionType.FILTERED_BIDS:
        # Export only filtered bids
        if bid_numbers_list:
            # Get specific bids by bid numbers
            initial_bids_to_filter_sqla = db.query(models.Bid).filter(
                models.Bid.bid_number.in_(bid_numbers_list)
            ).all()
            print(f"API: Found {len(initial_bids_to_filter_sqla)} currently filtered bids.")
        elif keywords_list:
            # Filter by keywords
            print(f"API: Filtering by keywords: {keywords_list}")
            all_bids_sqla = crud_bid.get_bids(db, limit=50000, skip=0)
            initial_bids_to_filter_sqla = filter_service.filter_bids_by_keywords_in_items(
                bids=all_bids_sqla, keywords=keywords_list
            )
        else:
            raise HTTPException(status_code=400, detail="No filtered bids provided for filtered_bids export type.")
    else:
        # Should not happen if Enum validation works
        raise HTTPException(status_code=400, detail="Invalid filter_condition_type.")

    if not initial_bids_to_filter_sqla:
        # Generate empty Excel based on export type
        if filter_condition_type == ExcelFilterConditionType.PDF_PROCESSED:
            empty_excel_buffer = excel_exporter_service.generate_pdf_processed_excel([])
        else:
            empty_excel_buffer = excel_exporter_service.generate_filtered_bids_excel([])
        return StreamingResponse(
            empty_excel_buffer,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=empty_export_{filter_condition_type}.xlsx"}
        )

    # Convert SQLAlchemy models to Pydantic BidInDB models for the excel service
    filtered_bids_pydantic: List[bid_schemas.BidInDB] = []
    for bid_sqla in initial_bids_to_filter_sqla: # Already filtered based on type
        try:
            filtered_bids_pydantic.append(bid_schemas.BidInDB.from_orm(bid_sqla))
        except Exception as e:
            print(f"Error converting bid {bid_sqla.bid_number} to Pydantic: {e}")
            continue

    if not filtered_bids_pydantic and initial_bids_to_filter_sqla: # If all conversions failed but there were bids
        raise HTTPException(status_code=500, detail="Error processing bid data for Excel.")

    # Handle uploaded existing Excel file (same logic as before)
    existing_excel_bytes_content: Optional[bytes] = None
    if existing_excel_file:
        try:
            existing_excel_bytes_content = await existing_excel_file.read()
        finally:
            await existing_excel_file.close()

    # Generate Excel based on export type
    try:
        if filter_condition_type == ExcelFilterConditionType.PDF_PROCESSED:
            excel_bytes_io = excel_exporter_service.generate_pdf_processed_excel(
                filtered_bids_pydantic,
                existing_excel_bytes_content
            )
        else:  # FILTERED_BIDS
            excel_bytes_io = excel_exporter_service.generate_filtered_bids_excel(
                filtered_bids_pydantic,
                existing_excel_bytes_content
            )
    except Exception as e:
        print(f"Excel generation error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to generate Excel: {str(e)}")

    # Stream the Excel file with custom filename format: DD_MM_YYYY_HH:mm_filtered_bids/pdf_processed_bids
    now = datetime.datetime.now()
    day = now.strftime("%d")
    month = now.strftime("%m")
    year = now.strftime("%Y")
    hour_min = now.strftime("%H:%M")

    if filter_condition_type == ExcelFilterConditionType.PDF_PROCESSED:
        filename = f"{day}_{month}_{year}_{hour_min}_pdf_processed_bids.xlsx"
    else:  # FILTERED_BIDS
        filename = f"{day}_{month}_{year}_{hour_min}_filtered_bids.xlsx"

    return StreamingResponse(
        excel_bytes_io,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )