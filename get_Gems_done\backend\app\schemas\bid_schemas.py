# app/schemas/bid_schemas.py
from pydantic import BaseModel, HttpUrl, validator, field_serializer
from typing import Optional
from datetime import datetime, timezone
from app.db.models import PDFAnalysisStatusEnum, ScrapeSourceTypeEnum

class BidBase(BaseModel):
    bid_id_internal: Optional[str] = None
    bid_number: str
    items: Optional[str] = None
    quantity: Optional[int] = None
    ministry_name: Optional[str] = None
    department_name: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    bid_details_url: Optional[HttpUrl] = None

    @validator('items', 'ministry_name', 'department_name', 'bid_id_internal', pre=True)
    def empty_str_to_none(cls, v):
        if isinstance(v, str) and v == "": return None
        return v

class BidCreate(BidBase):
    # New fields for tracking scraping sessions and sources
    scrape_session_id: Optional[str] = None
    scrape_source_type: Optional[ScrapeSourceTypeEnum] = None
    scrape_source_details: Optional[str] = None

class BidInDB(BidBase):
    id: int
    scraped_at: datetime

    pdf_analysis_status: Optional[PDFAnalysisStatusEnum] = None
    pdf_analysis_reasons: Optional[str] = None # Or List[str] if you parse it from JSON string
    pdf_last_analysed_at: Optional[datetime] = None
    pdf_processing_session: Optional[str] = None # Track which processing session analyzed this PDF

    # New fields for tracking scraping sessions and sources
    scrape_session_id: Optional[str] = None
    scrape_source_type: Optional[ScrapeSourceTypeEnum] = None
    scrape_source_details: Optional[str] = None

    class Config:
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }