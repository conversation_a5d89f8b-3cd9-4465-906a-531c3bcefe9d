// src/components/scrape/ScrapeControls.tsx
"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { fetchApi, ApiError, stopTask } from "@/lib/api";
import { XCircle, PlusCircle, RotateCcw, StopCircle } from "lucide-react";
import {
	saveLastScrapeSettings,
	loadLastScrapeSettings,
	ScrapeSettings,
} from "@/lib/localStorage";
import LoadingBar from "@/components/ui/LoadingBar";
import ProgressBar from "@/components/ui/ProgressBar";
import StatusBadge from "@/components/ui/StatusBadge";

// --- Static Data for Dropdowns ---
const STATIC_MINISTRIES: string[] = [
	"Autonomous Body",
	"Cabinet Secretariat",
	"Comptroller and Auditor General (CAG) of India",
	"Department of Space",
	"Election Commission of India",
	"Insurance Regulatory and Development Authority (IRDA)",
	"Law Commission of India",
	"Lok Sabha Secretariat",
	"Ministry of Agriculture and Farmers Welfare",
	"Ministry of AYUSH",
	"Ministry of Chemicals and Fertilizers",
	"Ministry of Civil Aviation",
	"Ministry of Coal",
	"Ministry of Commerce and Industry",
	"Ministry of Communications",
	"Ministry of Consumer Affairs Food and Public Distribution",
	"Ministry of Cooperation",
	"Ministry of Corporate Affairs",
	"Ministry of Culture",
	"Ministry of Defence",
	"Ministry of Development of North Eastern Region",
	"Ministry of Drinking Water and Sanitation",
	"Ministry of Earth Sciences",
	"Ministry of Education",
	"Ministry of Electronics and Information Technology",
	"Ministry of Environment Forest and Climate Change",
	"Ministry of External Affairs",
	"Ministry of Finance",
	"Ministry of Fisheries Animal Husbandry Dairying",
	"Ministry of Food Processing Industries",
	"Ministry of Health and Family Welfare",
	"Ministry of Heavy Industries and Public Enterprises",
	"Ministry of Home Affairs",
	"Ministry of Housing & Urban Affairs (MoHUA)",
	"Ministry of Housing and Urban Poverty Alleviation",
	"Ministry of Human Resource Development",
	"Ministry of Information and Broadcasting",
	"Ministry of Labour and Employment",
	"Ministry of Law and Justice",
	"Ministry of Micro Small and Medium Enterprises",
	"Ministry of Mines",
	"Ministry of Minority Affairs",
	"Ministry of New and Renewable Energy",
	"Ministry of Panchayati Raj",
	"Ministry of Parliamentary Affairs",
	"Ministry of Personnel Public Grievances and Pensions",
	"Ministry of Petroleum and Natural Gas",
	"Ministry of Ports, Shipping and Waterways",
	"Ministry of Power",
	"Ministry of Railways",
	"Ministry of Road Transports and highways",
	"Ministry of Rural Development ",
	"Ministry of Science and Technology",
	"Ministry of Skill Development and Entrepreneurship",
	"Ministry of Social Justice and Empowerment",
	"Ministry of Statistics and Programme Implementation",
	"Ministry of Steel",
	"Ministry of Textiles",
	"Ministry of Tourism",
	"Ministry of Tribal Affairs",
	"Ministry of Urban Development",
	"Ministry of Water Resources River Development and Ganga Rejuvenation",
	"Ministry of Women and Child Development",
	"Ministry of Youth Affairs and Sports",
	"National Mission for Clean Ganga (NMCG), New Delhi",
	"National Rural Livelihoods Mission (NRLM) - Aajeevika",
	"NITI Aayog - National Institution for Transforming India",
	"Office of the Principal Scientific Adviser",
	"PMO",
	"President of India",
	"Rajya Sabha Secretariat",
	"Seventh Central Pay Commission, New Delhi",
	"Vice President of India",
].sort(); // Optionally sort them alphabetically

interface StaticState {
	// Keep state_id if your city fetching API needs it, otherwise just name
	state_name: string;
	state_id: string;
}
const STATIC_STATES: StaticState[] = [
	{ state_name: "ANDAMAN & NICOBAR", state_id: "1" },
	{ state_name: "ANDHRA PRADESH", state_id: "2" },
	{ state_name: "ARUNACHAL PRADESH", state_id: "3" },
	{ state_name: "ASSAM", state_id: "4" },
	{ state_name: "BIHAR", state_id: "5" },
	{ state_name: "CHANDIGARH", state_id: "6" },
	{ state_name: "CHHATTISGARH", state_id: "7" },
	{ state_name: "DADRA & NAGAR HAVELI", state_id: "8" },
	{ state_name: "DAMAN & DIU", state_id: "9" },
	{ state_name: "DELHI", state_id: "10" },
	{ state_name: "GOA", state_id: "11" },
	{ state_name: "GUJARAT", state_id: "12" },
	{ state_name: "HARYANA", state_id: "13" },
	{ state_name: "HIMACHAL PRADESH", state_id: "14" },
	{ state_name: "JAMMU & KASHMIR", state_id: "15" },
	{ state_name: "JHARKHAND", state_id: "16" },
	{ state_name: "KARNATAKA", state_id: "17" },
	{ state_name: "KERALA", state_id: "18" },
	{ state_name: "LAKSHADWEEP", state_id: "19" },
	{ state_name: "MADHYA PRADESH", state_id: "20" },
	{ state_name: "MAHARASHTRA", state_id: "21" },
	{ state_name: "MANIPUR", state_id: "22" },
	{ state_name: "MEGHALAYA", state_id: "23" },
	{ state_name: "MIZORAM", state_id: "24" },
	{ state_name: "NAGALAND", state_id: "25" },
	{ state_name: "ODISHA", state_id: "26" },
	{ state_name: "PUDUCHERRY", state_id: "27" },
	{ state_name: "PUNJAB", state_id: "28" },
	{ state_name: "RAJASTHAN", state_id: "29" },
	{ state_name: "SIKKIM", state_id: "30" },
	{ state_name: "TAMIL NADU", state_id: "31" },
	{ state_name: "TELANGANA", state_id: "32" },
	{ state_name: "TRIPURA", state_id: "33" },
	{ state_name: "UTTAR PRADESH", state_id: "34" },
	{ state_name: "UTTARAKHAND", state_id: "35" },
	{ state_name: "WEST BENGAL", state_id: "36" },
].sort((a, b) => a.state_name.localeCompare(b.state_name)); // Sort by name

// --- Interfaces for Data Structures ---
interface Ministry {
	name: string;
} // Assuming backend proxy returns list of strings
interface Organization {
	name: string;
} // Assuming backend proxy returns list of strings
interface State {
	state_name: string;
	state_id: string;
} // Matches backend proxy
interface City {
	city_name: string;
	pin_code?: string;
} // Matches backend proxy

interface ScrapeJob {
	id: string;
	type: "ministry" | "location";
	selectedMinistry: string;
	availableOrganizations: Organization[];
	selectedOrganization: string;
	iterations: number; // Combined iterations field
	isFetchingSublist: boolean; // Combined fetching flag
	selectedStateId: string;
	selectedStateName: string;
	availableCities: City[];
	selectedCity: string;
}

interface ScrapeControlsProps {
	ministryScrapeApiUrl: string;
	locationScrapeApiUrl: string;
	statusApiUrl: string;
	// gemMinistriesApiUrl: string;
	gemOrganizationsApiUrl: string;
	// gemStatesApiUrl: string;
	gemCitiesApiUrl: string;
	onScrapeComplete?: () => void; // Callback to refresh bid section when scraping completes
	isAnyProcessRunning?: boolean; // Disable when other processes are running
}

interface ApiInitiateResponse {
	message: string;
	task_id?: string;
	details?: any;
}

interface BackendTaskInfo {
	task_id: string;
	status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED";
	message: string;
	progress_stage?: string | null;
	progress_current?: number;
	progress_total?: number;
	result_count?: number | null;
	error_message?: string | null;
}

// For displaying status of each job in the queue initiated by this component
interface QueuedScrapeTaskDisplayStatus {
	id: string; // Corresponds to ScrapeJob.id
	label: string;
	taskIdFromServer?: string;
	status:
		| "idle"
		| "pending_initiation"
		| "initiating"
		| "running_on_backend"
		| "completed_on_backend"
		| "failed_on_backend"
		| "failed_initiation";
	message?: string;
	startTime?: number;
}

const MIN_POLL_INTERVAL_MS = 10000; // Poll every 10 seconds

const ScrapeControls: React.FC<ScrapeControlsProps> = ({
	ministryScrapeApiUrl,
	locationScrapeApiUrl,
	statusApiUrl,
	gemOrganizationsApiUrl,
	gemCitiesApiUrl,
	onScrapeComplete,
	isAnyProcessRunning = false,
}) => {
	const availableMinistries = STATIC_MINISTRIES;
	const availableStates = STATIC_STATES;

	// const [availableMinistries, setAvailableMinistries] = useState<string[]>([]);
	// const [availableStates, setAvailableStates] = useState<State[]>([]);
	// const [isLoadingMasterData, setIsLoadingMasterData] = useState({ ministries: true, states: true });

	const [orgCache, setOrgCache] = useState<Record<string, Organization[]>>({});
	const [cityCache, setCityCache] = useState<Record<string, City[]>>({});

	const [scrapeJobs, setScrapeJobs] = useState<ScrapeJob[]>([]);
	const [isLoadingSettings, setIsLoadingSettings] = useState(false);

	const [isProcessingQueue, setIsProcessingQueue] = useState(false);
	const [scrapeQueueDisplayStatus, setScrapeQueueDisplayStatus] = useState<
		QueuedScrapeTaskDisplayStatus[]
	>([]);
	const [currentTaskIndexInQueue, setCurrentTaskIndexInQueue] = useState(0);

	const pollingIntervalsRef = useRef<Record<string, NodeJS.Timeout>>({});
	const lastPollTimeRef = useRef<Record<string, number>>({});

	const createNewJobBase = (type: "ministry" | "location"): ScrapeJob => ({
		id: `${type}_job_${Date.now()}_${Math.random().toString(16).slice(2)}`,
		type,
		selectedMinistry: "",
		availableOrganizations: [],
		selectedOrganization: "",
		iterations: 3,
		isFetchingSublist: false,
		selectedStateId: "",
		selectedStateName: "",
		availableCities: [],
		selectedCity: "",
	});

	const addMinistryJob = () =>
		setScrapeJobs((prev) => [...prev, createNewJobBase("ministry")]);
	const addLocationJob = () =>
		setScrapeJobs((prev) => [...prev, createNewJobBase("location")]);
	const removeJob = (jobId: string) =>
		setScrapeJobs((prev) => prev.filter((job) => job.id !== jobId));

	const loadLastSettings = async () => {
		setIsLoadingSettings(true);

		try {
			const lastSettings = loadLastScrapeSettings();
			if (!lastSettings) {
				alert("No previous scrape settings found.");
				return;
			}

			// Check if there are any valid settings to load
			if (
				lastSettings.ministryJobs.length === 0 &&
				lastSettings.locationJobs.length === 0
			) {
				alert("No valid scrape settings found in the saved data.");
				return;
			}

			// Clear current jobs
			setScrapeJobs([]);

			// Create new jobs from saved settings
			const newJobs: ScrapeJob[] = [];

			// Add ministry jobs
			for (const ministryJob of lastSettings.ministryJobs) {
				const job = createNewJobBase("ministry");
				job.selectedMinistry = ministryJob.selectedMinistry;
				job.selectedOrganization = ministryJob.selectedOrganization;
				job.iterations = ministryJob.iterations;

				// Load organizations for this ministry if not in cache
				if (
					ministryJob.selectedMinistry &&
					!orgCache[ministryJob.selectedMinistry]
				) {
					try {
						job.isFetchingSublist = true;
						const orgs = await fetchApi<string[]>(
							`${gemOrganizationsApiUrl}?ministry_name=${encodeURIComponent(
								ministryJob.selectedMinistry
							)}`
						);
						const orgObjects = (orgs || []).map((name) => ({ name }));
						job.availableOrganizations = orgObjects;
						setOrgCache((prev) => ({
							...prev,
							[ministryJob.selectedMinistry]: orgObjects,
						}));
					} catch (error) {
						console.error(
							`Failed to fetch orgs for ${ministryJob.selectedMinistry}:`,
							error
						);
					} finally {
						job.isFetchingSublist = false;
					}
				} else if (orgCache[ministryJob.selectedMinistry]) {
					job.availableOrganizations = orgCache[ministryJob.selectedMinistry];
				}

				newJobs.push(job);
			}

			// Add location jobs
			for (const locationJob of lastSettings.locationJobs) {
				const job = createNewJobBase("location");
				job.selectedStateId = locationJob.selectedStateId;
				job.selectedStateName = locationJob.selectedStateName;
				job.selectedCity = locationJob.selectedCity;
				job.iterations = locationJob.iterations;

				// Load cities for this state if not in cache
				if (
					locationJob.selectedStateId &&
					!cityCache[locationJob.selectedStateId]
				) {
					try {
						job.isFetchingSublist = true;
						const cities = await fetchApi<City[]>(
							`${gemCitiesApiUrl}?state_name=${encodeURIComponent(
								locationJob.selectedStateName
							)}`
						);
						job.availableCities = cities || [];
						setCityCache((prev) => ({
							...prev,
							[locationJob.selectedStateId]: cities || [],
						}));
					} catch (error) {
						console.error(
							`Failed to fetch cities for state ID ${locationJob.selectedStateId}:`,
							error
						);
					} finally {
						job.isFetchingSublist = false;
					}
				} else if (cityCache[locationJob.selectedStateId]) {
					job.availableCities = cityCache[locationJob.selectedStateId];
				}

				newJobs.push(job);
			}

			setScrapeJobs(newJobs);

			// Show success message with timestamp
			const settingsDate = new Date(lastSettings.timestamp).toLocaleString();
			alert(`Loaded scrape settings from ${settingsDate}`);
		} catch (error) {
			console.error("Error loading last scrape settings:", error);
			alert("Failed to load last scrape settings. Please try again.");
		} finally {
			setIsLoadingSettings(false);
		}
	};

	const updateJobField = (
		jobId: string,
		field: keyof Partial<ScrapeJob>,
		value: any
	) => {
		// Partial for flexibility
		setScrapeJobs((prev) =>
			prev.map((job) => (job.id === jobId ? { ...job, [field]: value } : job))
		);
	};

	const handleMinistrySelection = useCallback(
		async (jobId: string, ministryName: string) => {
			updateJobField(jobId, "selectedMinistry", ministryName);
			updateJobField(jobId, "selectedOrganization", "");
			updateJobField(jobId, "availableOrganizations", []);
			if (!ministryName) return;
			if (orgCache[ministryName]) {
				updateJobField(jobId, "availableOrganizations", orgCache[ministryName]);
				return;
			}

			updateJobField(jobId, "isFetchingSublist", true);
			try {
				const orgs = await fetchApi<string[]>(
					`${gemOrganizationsApiUrl}?ministry_name=${encodeURIComponent(
						ministryName
					)}`
				);
				const orgObjects = (orgs || []).map((name) => ({ name }));
				updateJobField(jobId, "availableOrganizations", orgObjects);
				setOrgCache((prev) => ({ ...prev, [ministryName]: orgObjects }));
			} catch (error) {
				console.error(`Failed to fetch orgs for ${ministryName}:`, error);
			} finally {
				updateJobField(jobId, "isFetchingSublist", false);
			}
		},
		[gemOrganizationsApiUrl, orgCache]
	);

	const handleStateSelection = useCallback(
		async (jobId: string, stateId: string) => {
			const selectedStateObj = availableStates.find(
				(s) => s.state_id === stateId
			); // Use STATIC_STATES here
			updateJobField(jobId, "selectedStateId", stateId);
			updateJobField(
				jobId,
				"selectedStateName",
				selectedStateObj?.state_name || ""
			);
			updateJobField(jobId, "selectedCity", "");
			updateJobField(jobId, "availableCities", []);
			if (!stateId) return;
			if (cityCache[stateId]) {
				updateJobField(jobId, "availableCities", cityCache[stateId]);
				return;
			}

			const stateNameToFetch = selectedStateObj?.state_name;
			if (!stateNameToFetch) {
				updateJobField(jobId, "isFetchingSublist", false);
				return;
			}

			updateJobField(jobId, "isFetchingSublist", true);
			try {
				const cities = await fetchApi<City[]>(
					`${gemCitiesApiUrl}?state_name=${encodeURIComponent(
						stateNameToFetch
					)}`
				);
				updateJobField(jobId, "availableCities", cities || []);
				setCityCache((prev) => ({ ...prev, [stateId]: cities || [] }));
			} catch (error) {
				console.error(`Failed to fetch cities for state ID ${stateId}:`, error);
			} finally {
				updateJobField(jobId, "isFetchingSublist", false);
			}
		},
		[availableStates, gemCitiesApiUrl, cityCache]
	);

	const updateQueueDisplayStatus = (
		frontendJobId: string,
		updates: Partial<QueuedScrapeTaskDisplayStatus>
	) => {
		setScrapeQueueDisplayStatus((prev) =>
			prev.map((task) =>
				task.id === frontendJobId ? { ...task, ...updates } : task
			)
		);
	};

	const handleStopTask = async (
		frontendJobId: string,
		backendTaskId: string
	) => {
		if (!backendTaskId) return;

		try {
			// Call the API to stop the task
			await stopTask(backendTaskId);

			// Update the UI to show the task as stopped
			updateQueueDisplayStatus(frontendJobId, {
				status: "failed_on_backend",
				message: "Task stopped by user request",
			});

			// Clear the polling interval
			if (pollingIntervalsRef.current[backendTaskId]) {
				clearInterval(pollingIntervalsRef.current[backendTaskId]);
				delete pollingIntervalsRef.current[backendTaskId];
			}

			// Move to the next task in the queue if this was the current one
			if (
				isProcessingQueue &&
				scrapeQueueDisplayStatus[currentTaskIndexInQueue]?.taskIdFromServer ===
					backendTaskId
			) {
				setCurrentTaskIndexInQueue((prev) => prev + 1);
			}
		} catch (error) {
			console.error("Failed to stop task:", error);
			alert("Failed to stop the task. Please try again.");
		}
	};

	const pollBackendTaskStatus = useCallback(
		async (frontendJobId: string, backendTaskId: string) => {
			const taskInDisplayQueue = scrapeQueueDisplayStatus.find(
				(task) => task.id === frontendJobId
			);
			if (
				taskInDisplayQueue &&
				(taskInDisplayQueue.status === "completed_on_backend" ||
					taskInDisplayQueue.status === "failed_on_backend" ||
					taskInDisplayQueue.status === "failed_initiation")
			) {
				if (pollingIntervalsRef.current[backendTaskId]) {
					clearInterval(pollingIntervalsRef.current[backendTaskId]);
					delete pollingIntervalsRef.current[backendTaskId];
				}
				return;
			}
			const now = Date.now();
			if (
				lastPollTimeRef.current[backendTaskId] &&
				now - lastPollTimeRef.current[backendTaskId] < MIN_POLL_INTERVAL_MS
			)
				return;
			lastPollTimeRef.current[backendTaskId] = now;

			console.log(
				`Polling status for FE Job ID: ${frontendJobId} (Backend Task: ${backendTaskId})`
			);
			try {
				const backendStatus = await fetchApi<BackendTaskInfo>(
					`${statusApiUrl}/${backendTaskId}`
				);
				let newFEStatus: QueuedScrapeTaskDisplayStatus["status"] =
					"running_on_backend";
				if (backendStatus.status === "COMPLETED")
					newFEStatus = "completed_on_backend";
				else if (backendStatus.status === "FAILED")
					newFEStatus = "failed_on_backend";

				updateQueueDisplayStatus(frontendJobId, {
					status: newFEStatus,
					message: `Backend: ${backendStatus.message}`,
				});

				if (
					backendStatus.status === "COMPLETED" ||
					backendStatus.status === "FAILED"
				) {
					if (pollingIntervalsRef.current[backendTaskId]) {
						clearInterval(pollingIntervalsRef.current[backendTaskId]);
						delete pollingIntervalsRef.current[backendTaskId];
					}
					delete lastPollTimeRef.current[backendTaskId];

					// If this was the task at currentTaskIndexInQueue, advance the queue
					if (
						isProcessingQueue &&
						scrapeQueueDisplayStatus[currentTaskIndexInQueue]
							?.taskIdFromServer === backendTaskId
					) {
						setCurrentTaskIndexInQueue((prev) => prev + 1);
					}

					// Call the onScrapeComplete callback if provided and task completed successfully
					if (backendStatus.status === "COMPLETED" && onScrapeComplete) {
						onScrapeComplete();
					}
				}
			} catch (err) {
				/* ... error handling for polling ... */
			}
		},
		[
			statusApiUrl,
			isProcessingQueue,
			currentTaskIndexInQueue,
			scrapeQueueDisplayStatus,
		]
	); // Added dependencies

	const processScrapeJobsQueue = async () => {
		const validJobsToProcess = scrapeJobs.filter(
			(job) =>
				(job.type === "ministry" &&
					job.selectedMinistry &&
					job.selectedOrganization) ||
				(job.type === "location" && job.selectedStateId) // City is optional
		);
		if (validJobsToProcess.length === 0) {
			alert("Please configure at least one valid scrape target.");
			return;
		}

		// Save current scrape settings to localStorage before starting
		const settingsToSave: ScrapeSettings = {
			ministryJobs: validJobsToProcess
				.filter((job) => job.type === "ministry")
				.map((job) => ({
					selectedMinistry: job.selectedMinistry,
					selectedOrganization: job.selectedOrganization,
					iterations: job.iterations,
				})),
			locationJobs: validJobsToProcess
				.filter((job) => job.type === "location")
				.map((job) => ({
					selectedStateId: job.selectedStateId,
					selectedStateName: job.selectedStateName,
					selectedCity: job.selectedCity,
					iterations: job.iterations,
				})),
			timestamp: new Date().toISOString(),
		};
		saveLastScrapeSettings(settingsToSave);

		Object.values(pollingIntervalsRef.current).forEach(clearInterval);
		pollingIntervalsRef.current = {};
		lastPollTimeRef.current = {};

		setIsProcessingQueue(true);
		setScrapeQueueDisplayStatus(
			validJobsToProcess.map((job) => ({
				id: job.id,
				label:
					job.type === "ministry"
						? `${job.selectedMinistry} - ${job.selectedOrganization}`
						: `${job.selectedStateName} - ${job.selectedCity || "All Cities"}`,
				status: "pending_initiation",
				message: "Waiting in queue...",
			}))
		);
		setCurrentTaskIndexInQueue(0);
	};

	useEffect(() => {
		// Main useEffect for initiating tasks from the queue
		if (
			!isProcessingQueue ||
			currentTaskIndexInQueue >= scrapeQueueDisplayStatus.length
		) {
			if (
				isProcessingQueue &&
				currentTaskIndexInQueue >= scrapeQueueDisplayStatus.length
			)
				setIsProcessingQueue(false);
			return;
		}
		const taskToInitiate = scrapeQueueDisplayStatus[currentTaskIndexInQueue];
		const jobData = scrapeJobs.find((j) => j.id === taskToInitiate.id);

		if (jobData && taskToInitiate.status === "pending_initiation") {
			updateQueueDisplayStatus(jobData.id, {
				status: "initiating",
				message: "Sending request...",
			});
			let apiUrl = "";
			const params = new URLSearchParams();
			if (jobData.type === "ministry") {
				apiUrl = ministryScrapeApiUrl;
				params.append("ministry_name", jobData.selectedMinistry);
				params.append("organization_name", jobData.selectedOrganization);
				params.append("max_scrape_iterations", jobData.iterations.toString());
			} else {
				/* location type */
				apiUrl = locationScrapeApiUrl;
				params.append("state_name", jobData.selectedStateName); // Send name as backend expects
				if (jobData.selectedCity)
					params.append("city_name", jobData.selectedCity);
				params.append("max_scrape_iterations", jobData.iterations.toString());
			}
			fetchApi<ApiInitiateResponse>(`${apiUrl}?${params.toString()}`, {
				method: "POST",
			})
				.then((response) => {
					updateQueueDisplayStatus(jobData.id, {
						status: "running_on_backend",
						message: `Backend: ${response.message}`,
						taskIdFromServer: response.task_id,
						startTime: Date.now(),
					});
					if (response.task_id) {
						pollingIntervalsRef.current[response.task_id] = setInterval(
							() => pollBackendTaskStatus(jobData.id, response.task_id!),
							MIN_POLL_INTERVAL_MS
						);
						pollBackendTaskStatus(jobData.id, response.task_id); // Initial poll
					} else {
						updateQueueDisplayStatus(jobData.id, {
							status: "failed_initiation",
							message: "No task ID from backend.",
						});
						setCurrentTaskIndexInQueue((prev) => prev + 1); // Move to next on immediate failure
					}
				})
				.catch((err) => {
					updateQueueDisplayStatus(jobData.id, {
						status: "failed_initiation",
						message: `Initiation Failed: ${
							err instanceof Error ? err.message : "Unknown error"
						}`,
					});
					setCurrentTaskIndexInQueue((prev) => prev + 1); // Move to next on initiation failure
				});
		} else if (
			jobData &&
			(taskToInitiate.status === "completed_on_backend" ||
				taskToInitiate.status === "failed_on_backend" ||
				taskToInitiate.status === "failed_initiation")
		) {
			// Task already in terminal state, advance if this is the current index
			if (currentTaskIndexInQueue < scrapeQueueDisplayStatus.length - 1) {
				// Check if not the last task
				setCurrentTaskIndexInQueue((prev) => prev + 1);
			} else {
				setIsProcessingQueue(false); // All tasks processed
			}
		}
		// If task is 'initiating' or 'running_on_backend', polling will handle advancing the queue index
	}, [
		isProcessingQueue,
		currentTaskIndexInQueue,
		scrapeJobs,
		scrapeQueueDisplayStatus,
		ministryScrapeApiUrl,
		locationScrapeApiUrl,
		pollBackendTaskStatus,
	]); // Added scrapeQueueDisplayStatus as it's read

	useEffect(() => {
		// Unmount cleanup
		return () => {
			Object.values(pollingIntervalsRef.current).forEach(clearInterval);
			pollingIntervalsRef.current = {};
			lastPollTimeRef.current = {};
		};
	}, []);

	return (
		/* ... JSX for rendering scrapeJobs, add buttons, and scrapeQueueDisplayStatus ... */
		// <div className="space-y-4">
		// 	{isLoadingMasterData.ministries ||
		// 		(isLoadingMasterData.states && (
		// 			<p className="text-sm text-gray-400 dark:text-gray-300">
		// 				Loading initial dropdown data...
		// 			</p>
		// 		))}
		<div className="space-y-4">
			{scrapeJobs.map((job, index) => (
				<div
					key={job.id}
					className="p-4 border border-none dark:border-gray-600 rounded-lg space-y-3 bg-white dark:bg-gray-800/60 relative shadow-sm"
					style={{ padding: "12px" }}
				>
					<button
						onClick={() => removeJob(job.id)}
						className=" text-red-500 border-none bg-transparent hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors"
						title="Remove this scrape target"
					>
						<XCircle size={20} style={{ color: "grey" }} />
					</button>
					<h4
						className="text-md font-semibold text-gray-700 dark:text-gray-200"
						// style={{ marginTop: "10px" }}
					>
						{index + 1} -{" by "}
						<span className="capitalize font-normal">{job.type}</span>
					</h4>
					{job.type === "ministry" && (
						<>
							<div>
								<label
									htmlFor={`ministry-${job.id}`}
									className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
									style={{
										marginBottom: "6px",
									}}
								>
									Ministry:
								</label>
								<select
									id={`ministry-${job.id}`}
									value={job.selectedMinistry}
									onChange={(e) =>
										handleMinistrySelection(job.id, e.target.value)
									}
									className="input w-full dark:bg-gray-700 dark:text-gray-100 dark:border-gray-500"
									disabled={job.isFetchingSublist} // Only disable if fetching sublist for *this job*
								>
									<option value="">-- Select Ministry --</option>
									{availableMinistries.map((min) => (
										<option key={min} value={min}>
											{min}
										</option>
									))}
								</select>
							</div>
							<div>
								<label
									htmlFor={`organization-${job.id}`}
									className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
									style={{
										marginBottom: "6px",
									}}
								>
									Organization:
								</label>
								<select
									id={`organization-${job.id}`}
									value={job.selectedOrganization}
									onChange={(e) =>
										updateJobField(
											job.id,
											"selectedOrganization",
											e.target.value
										)
									}
									className="input w-full dark:bg-gray-700 dark:text-gray-100 dark:border-gray-500"
									disabled={
										!job.selectedMinistry ||
										job.isFetchingSublist ||
										job.availableOrganizations.length === 0
									}
								>
									<option value="">-- Select Organization --</option>
									{job.availableOrganizations.map((org) => (
										<option key={org.name} value={org.name}>
											{org.name}
										</option>
									))}
								</select>
								{job.isFetchingSublist && job.type === "ministry" && (
									<div className="mt-2">
										<LoadingBar
											height={2}
											label="Loading organizations..."
											className="text-xs"
										/>
									</div>
								)}
							</div>
						</>
					)}
					{job.type === "location" && (
						<>
							<div>
								<label
									htmlFor={`state-${job.id}`}
									className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
									style={{
										marginBottom: "6px",
									}}
								>
									State:
								</label>
								<select
									id={`state-${job.id}`}
									value={job.selectedStateId}
									onChange={(e) => handleStateSelection(job.id, e.target.value)}
									className="input w-full dark:bg-gray-700 dark:text-gray-100 dark:border-gray-500"
									disabled={job.isFetchingSublist} // Only disable if fetching sublist for *this job*
								>
									<option value="">-- Select State --</option>
									{availableStates.map((st) => (
										<option key={st.state_id} value={st.state_id}>
											{st.state_name}
										</option>
									))}
								</select>
							</div>
							<div>
								<label
									htmlFor={`city-${job.id}`}
									className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
									style={{
										marginBottom: "6px",
									}}
								>
									City:
								</label>
								<select
									id={`city-${job.id}`}
									value={job.selectedCity}
									onChange={(e) =>
										updateJobField(job.id, "selectedCity", e.target.value)
									}
									className="input w-full dark:bg-gray-700 dark:text-gray-100 dark:border-gray-500"
									disabled={
										!job.selectedStateId ||
										job.isFetchingSublist ||
										job.availableCities.length === 0
									}
								>
									<option value="">-- Select City --</option>
									{job.availableCities.map((c) => (
										<option key={c.city_name} value={c.city_name}>
											{c.city_name}
										</option>
									))}
								</select>
								{job.isFetchingSublist && job.type === "location" && (
									<div className="mt-2">
										<LoadingBar
											height={2}
											label="Loading cities..."
											className="text-xs"
										/>
									</div>
								)}
							</div>
						</>
					)}
					<div>
						<label
							htmlFor={`iter-${job.id}`}
							className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1"
							style={{
								marginBottom: "6px",
							}}
						>
							Passes:
						</label>
						<input
							type="number"
							id={`iter-${job.id}`}
							value={job.iterations}
							min="1"
							onChange={(e) =>
								updateJobField(
									job.id,
									"iterations",
									parseInt(e.target.value) || 3
								)
							}
							className="input w-24 dark:bg-gray-700 dark:text-gray-100 dark:border-gray-500"
						/>
					</div>
				</div>
			))}

			<div className="flex flex-col sm:flex-row gap-3 mt-4">
				<button
					onClick={addMinistryJob}
					disabled={isAnyProcessRunning}
					className={`btn btn-secondary py-2 px-4 text-sm flex items-center justify-center gap-1.5 ${
						isAnyProcessRunning ? "opacity-50 cursor-not-allowed" : ""
					}`}
					style={{
						marginBottom: "6px",
						border: "none",
					}}
					title={
						isAnyProcessRunning
							? "Another process is currently running. Please wait for it to complete."
							: ""
					}
				>
					<PlusCircle size={12} style={{ marginRight: "4px" }} /> Add
					Ministry/Org Target
				</button>
				<button
					onClick={addLocationJob}
					disabled={isAnyProcessRunning}
					className={`btn btn-secondary py-2 px-4 text-sm flex items-center justify-center gap-1.5 ${
						isAnyProcessRunning ? "opacity-50 cursor-not-allowed" : ""
					}`}
					style={{
						marginBottom: "6px",
						border: "none",
					}}
					title={
						isAnyProcessRunning
							? "Another process is currently running. Please wait for it to complete."
							: ""
					}
				>
					<PlusCircle size={12} style={{ marginRight: "4px" }} /> Add Location
					Target
				</button>
				<button
					onClick={loadLastSettings}
					className={`btn btn-secondary py-2 px-4 text-sm flex items-center justify-center gap-1.5 ${
						isAnyProcessRunning || isLoadingSettings
							? "opacity-50 cursor-not-allowed"
							: ""
					}`}
					style={{
						marginBottom: "6px",
						border: "none",
					}}
					title={
						isAnyProcessRunning
							? "Another process is currently running. Please wait for it to complete."
							: "Load the settings from your last scrape"
					}
					disabled={isAnyProcessRunning || isLoadingSettings}
				>
					<RotateCcw
						size={12}
						style={{ marginRight: "4px" }}
						className={isLoadingSettings ? "animate-spin" : ""}
					/>
					{isLoadingSettings ? "Loading..." : "Load Last Scrape Settings"}
				</button>
			</div>

			<div className="mt-6">
				<button
					onClick={processScrapeJobsQueue}
					className="btn btn-primary w-full py-3 text-base"
					disabled={
						isAnyProcessRunning ||
						isProcessingQueue ||
						scrapeJobs.length === 0 ||
						!scrapeJobs.some(
							(j) =>
								(j.type === "ministry" &&
									j.selectedMinistry &&
									j.selectedOrganization) ||
								(j.type === "location" && j.selectedStateId)
						)
					}
					title={
						isAnyProcessRunning && !isProcessingQueue
							? "Another process is currently running. Please wait for it to complete."
							: ""
					}
				>
					{isProcessingQueue
						? "Processing Scrape Queue..."
						: "Run All Added Scrapes"}
				</button>
			</div>

			{scrapeQueueDisplayStatus.length > 0 && (
				<div className="mt-6 space-y-3">
					<div className="flex items-center justify-between">
						<h4 className="text-md font-semibold text-gray-700 dark:text-gray-300">
							Scraping Progress
						</h4>
						<span className="text-sm text-gray-500">
							{
								scrapeQueueDisplayStatus.filter(
									(task) => task.status === "completed_on_backend"
								).length
							}{" "}
							of {scrapeQueueDisplayStatus.length} completed
						</span>
					</div>

					{/* Overall Progress Bar */}
					<ProgressBar
						current={
							scrapeQueueDisplayStatus.filter(
								(task) => task.status === "completed_on_backend"
							).length
						}
						total={scrapeQueueDisplayStatus.length}
						height={6}
						variant={
							scrapeQueueDisplayStatus.some((t) => t.status.includes("failed"))
								? "error"
								: isProcessingQueue
								? "processing"
								: "completed"
						}
						label="Overall Progress"
						showDetailed={true}
					/>

					{/* Individual Task Status */}
					<div className="space-y-2">
						{scrapeQueueDisplayStatus.map((task, index) => {
							const isCurrentTask = index === currentTaskIndexInQueue;
							const getStatusType = () => {
								if (task.status === "completed_on_backend") return "completed";
								if (
									task.status === "failed_initiation" ||
									task.status === "failed_on_backend"
								)
									return "failed";
								if (
									task.status === "running_on_backend" ||
									task.status === "initiating"
								)
									return "running";
								return "pending";
							};

							return (
								<div
									key={task.id}
									className={`p-3 rounded-md text-sm border transition-all duration-200 ${
										isCurrentTask
											? "ring-2 ring-blue-200 shadow-md"
											: "shadow-sm"
									} ${
										task.status === "completed_on_backend"
											? "bg-green-50 border-green-200 text-green-700"
											: task.status === "failed_initiation" ||
											  task.status === "failed_on_backend"
											? "bg-red-50 border-red-200 text-red-700"
											: task.status === "running_on_backend" ||
											  task.status === "initiating"
											? "bg-blue-50 border-blue-200 text-blue-700"
											: "bg-gray-50 border-gray-200 text-gray-700"
									}`}
								>
									<div className="flex items-center justify-between mb-2">
										<span className="font-medium">{task.label}</span>
										<div className="flex items-center gap-2">
											{(task.status === "running_on_backend" ||
												task.status === "initiating") &&
												task.taskIdFromServer && (
													<button
														onClick={() =>
															handleStopTask(task.id, task.taskIdFromServer!)
														}
														className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100"
														title="Stop this task"
													>
														<StopCircle size={16} />
													</button>
												)}
											<StatusBadge status={getStatusType()} />
										</div>
									</div>
									<p className="text-xs opacity-80">{task.message}</p>

									{/* Show loading bar for current running task */}
									{isCurrentTask &&
										(task.status === "running_on_backend" ||
											task.status === "initiating") && (
											<div className="mt-2">
												<LoadingBar
													height={3}
													variant="primary"
													pulse={task.status === "initiating"}
												/>
											</div>
										)}
								</div>
							);
						})}
					</div>
				</div>
			)}
		</div>
	);
};
export default ScrapeControls;
