// src/components/scrape/ManualScrapeInputs.tsx
"use client";

import React, { useState } from "react";
import { fetchApi, ApiError } from "@/lib/api";

interface ManualScrapeInputProps {
	ministryApiUrl: string;
	locationApiUrl: string;
	statusApiUrl: string;
}

interface ApiInitiateResponse {
	message: string;
	task_id?: string;
	details?: any;
}

const ManualScrapeInputs: React.FC<ManualScrapeInputProps> = ({
	ministryApiUrl,
	locationApiUrl,
	statusApiUrl,
}) => {
	const [manualMinistry, setManualMinistry] = useState("");
	const [manualOrganization, setManualOrganization] = useState("");
	const [manualMinistryIterations, setManualMinistryIterations] = useState(1);
	const [manualStateName, setManualStateName] = useState("");
	const [manualCity, setManualCity] = useState("");
	const [manualLocationIterations, setManualLocationIterations] = useState(1);

	const [isLoadingMinistry, setIsLoadingMinistry] = useState(false);
	const [isLoadingLocation, setIsLoadingLocation] = useState(false);
	const [statusMessage, setStatusMessage] = useState<string | null>(null);
	const [errorMessage, setErrorMessage] = useState<string | null>(null);
	const [lastTaskId, setLastTaskId] = useState<string | null>(null);

	const handleManualMinistryScrape = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!manualMinistry || !manualOrganization) {
			setErrorMessage("Manual Ministry and Organization names are required.");
			return;
		}
		setIsLoadingMinistry(true);
		setStatusMessage(null);
		setErrorMessage(null);
		setLastTaskId(null);
		const params = new URLSearchParams({
			ministry_name: manualMinistry,
			organization_name: manualOrganization,
			max_scrape_iterations: manualMinistryIterations.toString(),
		});
		try {
			const response = await fetchApi<ApiInitiateResponse>(
				`${ministryApiUrl}?${params.toString()}`,
				{ method: "POST" }
			);
			setStatusMessage(response.message);
			if (response.task_id) setLastTaskId(response.task_id);
			setManualMinistry("");
			setManualOrganization("");
		} catch (err) {
			/* ... (error handling similar to ScrapeControls) ... */
			const msg = err instanceof Error ? err.message : "Unknown error";
			setErrorMessage(`Manual Ministry Scrape Error: ${msg}`);
		} finally {
			setIsLoadingMinistry(false);
		}
	};

	const handleManualLocationScrape = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!manualStateName) {
			setErrorMessage("Manual State name is required.");
			return;
		}
		setIsLoadingLocation(true);
		setStatusMessage(null);
		setErrorMessage(null);
		setLastTaskId(null);
		const params = new URLSearchParams({
			state_name: manualStateName,
			max_scrape_iterations: manualLocationIterations.toString(),
		});
		if (manualCity) params.append("city_name", manualCity);
		try {
			const response = await fetchApi<ApiInitiateResponse>(
				`${locationApiUrl}?${params.toString()}`,
				{ method: "POST" }
			);
			setStatusMessage(response.message);
			if (response.task_id) setLastTaskId(response.task_id);
			setManualStateName("");
			setManualCity("");
		} catch (err) {
			/* ... (error handling similar to ScrapeControls) ... */
			const msg = err instanceof Error ? err.message : "Unknown error";
			setErrorMessage(`Manual Location Scrape Error: ${msg}`);
		} finally {
			setIsLoadingLocation(false);
		}
	};

	return (
		<div className="space-y-6">
			{/* Manual Ministry/Organization Scrape Form */}
			<form
				onSubmit={handleManualMinistryScrape}
				className="space-y-4 bg-gray-50 p-5 rounded-md"
			>
				<h3 className="text-lg font-semibold text-gray-800">
					Manual Ministry/Organization
				</h3>
				<div>
					<label
						htmlFor="manualMinistry"
						className="block text-sm font-medium mb-1 text-gray-700"
					>
						Ministry Name:
					</label>
					<input
						type="text"
						id="manualMinistry"
						value={manualMinistry}
						onChange={(e) => setManualMinistry(e.target.value)}
						className="input"
						placeholder="e.g., Ministry of Defence"
						required
					/>
				</div>
				<div>
					<label
						htmlFor="manualOrganization"
						className="block text-sm font-medium mb-1 text-gray-700"
					>
						Organization Name:
					</label>
					<input
						type="text"
						id="manualOrganization"
						value={manualOrganization}
						onChange={(e) => setManualOrganization(e.target.value)}
						className="input"
						placeholder="e.g., Armoured Vehicles Nigam Limited"
						required
					/>
				</div>
				<div>
					<label
						htmlFor="manualMinistryIterations"
						className="block text-sm font-medium mb-1 text-gray-700"
					>
						Max Scrape Passes:
					</label>
					<input
						type="number"
						id="manualMinistryIterations"
						value={manualMinistryIterations}
						onChange={(e) =>
							setManualMinistryIterations(parseInt(e.target.value) || 1)
						}
						min="1"
						className="input"
					/>
				</div>
				<button
					type="submit"
					disabled={isLoadingMinistry || isLoadingLocation}
					className="btn btn-primary w-full"
				>
					{isLoadingMinistry
						? "Initiating..."
						: "Scrape Manually (Ministry/Org)"}
				</button>
			</form>

			{/* Manual Location Scrape Form */}
			<form
				onSubmit={handleManualLocationScrape}
				className="space-y-4 bg-gray-50 p-5 rounded-md"
			>
				<h3 className="text-lg font-semibold text-gray-800">Manual Location</h3>
				<div>
					<label
						htmlFor="manualStateName"
						className="block text-sm font-medium mb-1 text-gray-700"
					>
						State Name:
					</label>
					<input
						type="text"
						id="manualStateName"
						value={manualStateName}
						onChange={(e) => setManualStateName(e.target.value)}
						className="input"
						placeholder="e.g., MAHARASHTRA"
						required
					/>
				</div>
				<div>
					<label
						htmlFor="manualCity"
						className="block text-sm font-medium mb-1 text-gray-700"
					>
						City Name (Optional):
					</label>
					<input
						type="text"
						id="manualCity"
						value={manualCity}
						onChange={(e) => setManualCity(e.target.value)}
						className="input"
						placeholder="e.g., PUNE"
					/>
				</div>
				<div>
					<label
						htmlFor="manualLocationIterations"
						className="block text-sm font-medium mb-1 text-gray-700"
					>
						Max Scrape Passes:
					</label>
					<input
						type="number"
						id="manualLocationIterations"
						value={manualLocationIterations}
						onChange={(e) =>
							setManualLocationIterations(parseInt(e.target.value) || 1)
						}
						min="1"
						className="input"
					/>
				</div>
				<button
					type="submit"
					disabled={isLoadingMinistry || isLoadingLocation}
					className="btn btn-primary w-full"
				>
					{isLoadingLocation ? "Initiating..." : "Scrape Manually (Location)"}
				</button>
			</form>

			{/* Status & Error Messages for manual scrapes */}
			{(isLoadingMinistry || isLoadingLocation) &&
				!statusMessage &&
				!errorMessage && (
					<div className="bg-yellow-50 text-yellow-700 p-3 rounded-md border border-yellow-200">
						Manual task initiated...
					</div>
				)}
			{statusMessage && (
				<div className="bg-green-50 text-green-700 p-3 rounded-md border border-green-200">
					{statusMessage} {lastTaskId ? `(Task ID: ${lastTaskId})` : ""}
				</div>
			)}
			{errorMessage && (
				<div className="bg-red-50 text-red-700 p-3 rounded-md border border-red-200">
					{errorMessage}
				</div>
			)}
		</div>
	);
};
export default ManualScrapeInputs;
