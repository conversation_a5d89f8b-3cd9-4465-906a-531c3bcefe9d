# app/main.py
from fastapi import FastAPI

from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.endpoints import gem_data_proxy as gem_data_router_v1
from app.api.v1.endpoints import bid as bids_router_v1
from app.api.v1.endpoints import batch_pdf_analysis as batch_analysis_router_v1
from app.db.database import engine, Base # Import engine and Base for table creation (optional here)

# Optional: Create tables if they don't exist (Alembic is the preferred way for production)
# This is more for quick local dev if you don't want to run alembic upgrade head every time
# BUT: It's generally better to rely on Alembic for schema management.
# If you use this, make sure your models are imported so Base.metadata knows about them.
# import app.db.models # Ensure models are loaded if using create_all
# Base.metadata.create_all(bind=engine) # Creates tables based on SQLAlchemy models

app = FastAPI(
    title="GeM Bid Scraper API",
    description="API to scrape and retrieve government bids from GeM.",
    version="0.1.0"
)

origins = [
    "http://localhost:3000", # Your Next.js frontend
    # Add other origins if needed
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"], # Allows all methods
    allow_headers=["*"], # Allows all headers
)

# Include the API router
app.include_router(bids_router_v1.router, prefix="/api/v1", tags=["Bids and Scrapes"])
app.include_router(gem_data_router_v1.router, prefix="/api/v1/gem-data", tags=["GeM Data Proxy"])
app.include_router(batch_analysis_router_v1.router, prefix="/api/v1", tags=["Batch PDF Analysis"])

@app.get("/", summary="Root path, returns a welcome message")
async def read_root():
    return {"message": "Welcome to the GeM Bid Scraper API!"}

# --- For development: A simple endpoint to see if the app is alive ---
@app.get("/health", summary="Health check endpoint")
async def health_check():
    return {"status": "healthy"}