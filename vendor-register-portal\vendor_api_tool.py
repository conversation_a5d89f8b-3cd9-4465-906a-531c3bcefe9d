#!/usr/bin/env python3
"""
Vendor Registration Portal API Tool
===================================

A comprehensive tool for making API calls to the vendor registration portal,
extracting table data from HTML responses, and generating beautifully formatted Excel reports.
Enhanced with nested table support, comprehensive filter options, and PDF download functionality.

Author: AI Assistant
Date: 2025-07-02
"""

import requests
import json
import re
from typing import Dict, List, Tuple
from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import argparse
import sys
from datetime import datetime
from urllib.parse import urljoin
import logging
from typing import Dict, List, Optional, Tuple
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vendor_api_tool.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Comprehensive Filter Options Mapping
FILTER_OPTIONS = {
    'factories': {
        '1': 'Ammunition Factory Khadki',
        '2': 'Armament Research & Development Establishment',
        '3': 'Bharat Dynamics Limited',
        '4': 'Bharat Electronics Limited',
        '5': 'Defence Electronics Research Laboratory',
        '6': 'Defence Food Research Laboratory',
        '7': 'Defence Institute of Advanced Technology',
        '8': 'Defence Metallurgical Research Laboratory',
        '9': 'Defence Research & Development Laboratory',
        '10': 'Defence Research & Development Organisation',
        '11': 'Electronics & Radar Development Establishment',
        '12': 'Gas Turbine Research Establishment',
        '13': 'Gun Carriage Factory Jabalpur',
        '14': 'Heavy Alloy Penetrator Project',
        '15': 'Heavy Vehicles Factory Avadi',
        '16': 'High Energy Materials Research Laboratory',
        '17': 'Indian Naval Ship Shivaji',
        '18': 'Instruments Research & Development Establishment',
        '19': 'Laser Science & Technology Centre',
        '20': 'Machine Tool Prototype Factory',
        '21': 'Mazagon Dock Shipbuilders Limited',
        '22': 'Mishra Dhatu Nigam Limited',
        '23': 'Naval Chemical & Metallurgical Laboratory',
        '24': 'Naval Physical & Oceanographic Laboratory',
        '25': 'Naval Science & Technological Laboratory',
        '26': 'Ordnance Cable Factory Chandigarh',
        '27': 'Ordnance Equipment Factory Hazratpur',
        '28': 'Ordnance Factory Ambajhari',
        '29': 'Ordnance Factory Badmal',
        '30': 'Ordnance Factory Bhandara',
        '31': 'Ordnance Factory Board',
        '32': 'Ordnance Factory Dehradun',
        '33': 'Ordnance Factory Dehu Road',
        '34': 'Ordnance Factory Itarsi',
        '35': 'Ordnance Factory Khamaria',
        '36': 'Ordnance Factory Medak',
        '37': 'Ordnance Factory Muradnagar',
        '38': 'Ordnance Factory Trichy',
        '39': 'Ordnance Factory Varangaon',
        '40': 'Ordnance Factory Yeddumailaram',
        '41': 'Research Centre Imarat',
        '42': 'Terminal Ballistics Research Laboratory',
        '43': 'Vehicle Factory Jabalpur'
    },
    'industry_categories': {
        'L': 'Large Scale',
        'M': 'Medium Scale',
        'S': 'Micro and Small Enterprises',
        'SS': 'Micro and Small Enterprises owned by SC/ST entrepreneurs',
        'U': 'Udyam'
    },
    'used_for': {
        'R': 'Raw material',
        'C': 'Component',
        'E': 'End Product',
        'S': 'Special Tools',
        'P': 'Production Consumables'
    },
    'sorting': {
        'valid_upto': 'Register Upto',
        'name': 'Vendor Name'
    }
}

class VendorAPITool:
    """Main class for handling vendor registration portal API interactions."""
    
    def __init__(self):
        self.base_url = "https://ddpdoo.gov.in"
        self.search_endpoint = "/vendor/general_reports/show/registered_vendors/search"
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """Configure the session with default headers."""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Host': 'ddpdoo.gov.in',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        self.connection_verified = False

    def verify_connection(self) -> bool:
        """Verify connection to the API endpoint."""
        try:
            print("🔍 Verifying connection to DDPDOO portal...")
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            if "vendor" in response.text.lower() and "search" in response.text.lower():
                print("✅ Connection verified successfully!")
                self.connection_verified = True
                return True
            else:
                print("⚠️ Connected but page content unexpected")
                return False

        except requests.exceptions.Timeout:
            print("❌ Connection timeout - please check your internet connection")
            return False
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - please check your internet connection")
            return False
        except requests.exceptions.HTTPError as e:
            print(f"❌ HTTP error: {e}")
            return False
        except Exception as e:
            print(f"❌ Connection verification failed: {e}")
            return False

    def get_system_status(self) -> dict:
        """Get comprehensive system status information."""
        status = {
            'connection': self.connection_verified,
            'session_active': bool(self.session),
            'base_url': self.base_url,
            'endpoint': self.search_endpoint,
            'user_agent': self.session.headers.get('User-Agent', 'Not set'),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Test basic connectivity
        try:
            response = self.session.head(self.base_url, timeout=5)
            status['server_reachable'] = response.status_code == 200
            status['response_time'] = response.elapsed.total_seconds()
        except:
            status['server_reachable'] = False
            status['response_time'] = None

        return status

    def get_csrf_token(self) -> Optional[str]:
        """Extract CSRF token from the search page."""
        try:
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrf_ofb'})
            
            if csrf_input:
                return csrf_input.get('value')
            else:
                logger.warning("CSRF token not found in page")
                return None
                
        except Exception as e:
            logger.error(f"Error getting CSRF token: {e}")
            return None
    
    def make_search_request(self, search_params: Dict[str, str]) -> Optional[str]:
        """Make a POST request to search for vendors."""
        try:
            csrf_token = self.get_csrf_token()
            if not csrf_token:
                logger.error("Could not obtain CSRF token")
                return None
            
            # Prepare form data
            form_data = {
                'csrf_ofb': csrf_token,
                'factory_id': search_params.get('factory_id', ''),
                'location': search_params.get('location', ''),
                'industry_category': search_params.get('industry_category', ''),
                'product': search_params.get('product', ''),
                'used_for': search_params.get('used_for', ''),
                'vendor': search_params.get('vendor', ''),
                'search': 'search'
            }
            
            # Update headers for POST request
            self.session.headers.update({
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': urljoin(self.base_url, self.search_endpoint)
            })
            
            url = urljoin(self.base_url, self.search_endpoint)
            logger.info(f"Making POST request to: {url}")
            logger.info(f"Form data: {form_data}")

            response = self.session.post(url, data=form_data)
            response.raise_for_status()

            logger.info(f"Search request successful. Response length: {len(response.text)}")

            # Debug: Check response content
            if len(response.text) < 1000:
                logger.warning(f"Response seems short: {response.text[:500]}...")

            # Check for common error indicators
            if "error" in response.text.lower() or "exception" in response.text.lower():
                logger.warning("Response may contain error messages")

            # Debug: Save response to file for inspection
            try:
                debug_filename = f"debug_response_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                with open(debug_filename, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                logger.info(f"Debug: Response saved to {debug_filename}")
            except Exception as debug_e:
                logger.warning(f"Could not save debug response: {debug_e}")

            return response.text
            
        except Exception as e:
            logger.error(f"Error making search request: {e}")
            return None
    
    def get_paginated_data(self, offset: int = 0) -> Optional[str]:
        """Get paginated data using GET request with connection verification."""
        try:
            # Verify connection if not already done
            if not self.connection_verified:
                if not self.verify_connection():
                    return None

            if offset == 0:
                url = urljoin(self.base_url, self.search_endpoint)
            else:
                url = urljoin(self.base_url, f"{self.search_endpoint}/{offset}")

            response = self.session.get(url)
            response.raise_for_status()

            logger.info(f"Paginated request successful for offset {offset}")
            return response.text

        except Exception as e:
            logger.error(f"Error getting paginated data: {e}")
            return None

    def download_pdf(self, download_url: str, filename: str = None) -> bool:
        """Download PDF file from the given URL."""
        try:
            # Handle relative URLs
            if download_url.startswith('/'):
                download_url = urljoin(self.base_url, download_url)

            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"vendor_report_{timestamp}.pdf"

            # Ensure filename has .pdf extension
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'

            logger.info(f"Downloading PDF from: {download_url}")
            response = self.session.get(download_url)
            response.raise_for_status()

            # Save the PDF file
            with open(filename, 'wb') as f:
                f.write(response.content)

            logger.info(f"PDF downloaded successfully: {filename}")
            return True

        except Exception as e:
            logger.error(f"Error downloading PDF: {e}")
            return False

    def get_filter_options(self) -> Dict[str, Dict[str, str]]:
        """Get the comprehensive filter options mapping."""
        return FILTER_OPTIONS

class HTMLTableExtractor:
    """Enhanced class for extracting table data from HTML responses with nested table support."""

    @staticmethod
    def extract_main_table(html_content: str) -> List[List[str]]:
        """Extract the main table data from HTML content with nested table handling."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Find the main data table using multiple strategies
            tables = soup.find_all('table')
            logger.info(f"Found {len(tables)} tables in HTML content")

            if not tables:
                logger.warning("No tables found in HTML content")
                # Check for common error messages or empty responses
                if "no data" in html_content.lower() or "no records" in html_content.lower():
                    logger.warning("HTML content indicates no data available")
                elif len(html_content) < 500:
                    logger.warning(f"HTML content seems too short: {html_content[:200]}...")
                return []

            # Log main table information
            if tables:
                main_table_rows = tables[0].find_all('tr')
                logger.info(f"Main table has {len(main_table_rows)} rows")

            main_table = HTMLTableExtractor._find_main_table(tables)

            if not main_table:
                logger.warning("Could not identify main data table")
                # Debug: Try to extract from the largest table as fallback
                if tables:
                    largest_table = max(tables, key=lambda t: len(t.find_all('tr')))
                    logger.info("Attempting extraction from largest table as fallback")
                    table_data = HTMLTableExtractor._extract_nested_table_data(largest_table)
                    if table_data:
                        logger.info(f"Fallback extraction successful: {len(table_data)} rows")
                        return table_data
                return []

            # Enhanced extraction with nested table support
            table_data = HTMLTableExtractor._extract_nested_table_data(main_table)

            logger.info(f"Extracted {len(table_data)} rows from main table (with nested support)")
            return table_data

        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    @staticmethod
    def extract_download_links(html_content: str) -> List[Dict[str, str]]:
        """Extract download links from HTML content."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            download_links = []

            # Look for download links with common patterns
            download_patterns = [
                {'tag': 'a', 'attrs': {'class': 'btn btn-warning'}},
                {'tag': 'a', 'attrs': {'href': re.compile(r'.*download.*', re.I)}},
                {'tag': 'a', 'text': re.compile(r'.*download.*', re.I)},
                {'tag': 'a', 'attrs': {'href': re.compile(r'.*pdf.*', re.I)}},
            ]

            for pattern in download_patterns:
                if 'text' in pattern:
                    links = soup.find_all(pattern['tag'], string=pattern['text'])
                else:
                    links = soup.find_all(pattern['tag'], pattern.get('attrs', {}))

                for link in links:
                    href = link.get('href')
                    text = link.get_text(strip=True)
                    if href and text:
                        download_links.append({
                            'url': href,
                            'text': text,
                            'type': 'pdf' if 'pdf' in href.lower() else 'download'
                        })

            logger.info(f"Found {len(download_links)} download links")
            return download_links

        except Exception as e:
            logger.error(f"Error extracting download links: {e}")
            return []

    @staticmethod
    def _find_main_table(tables):
        """Find the main data table from a list of tables."""
        logger.info(f"Analyzing {len(tables)} tables to find main data table...")

        # Strategy 1: Look for tables with specific classes or IDs that indicate data tables
        data_table_indicators = ['data', 'results', 'vendors', 'list', 'grid', 'table-striped', 'table-bordered']

        for i, table in enumerate(tables):
            table_class = table.get('class', [])
            table_id = table.get('id', '')

            if any(indicator in ' '.join(table_class).lower() for indicator in data_table_indicators):
                logger.info(f"Found table with data indicator class/id at index {i}")
                return table
            if any(indicator in table_id.lower() for indicator in data_table_indicators):
                logger.info(f"Found table with data indicator class/id at index {i}")
                return table

        # Strategy 2: Find table with the most rows (likely the main data table)
        best_table = None
        max_rows = 0
        best_table_index = -1

        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            row_count = len(rows)

            # Log details for large tables
            if row_count > 50:
                logger.info(f"Table {i+1}: {row_count} rows - potential main table")

            if row_count > max_rows:
                max_rows = row_count
                best_table = table
                best_table_index = i

        if best_table:
            logger.info(f"Selected table {best_table_index+1} with {max_rows} rows as main table")
        else:
            logger.warning("No suitable main table found")

        return best_table

    @staticmethod
    def _extract_nested_table_data(table) -> List[List[str]]:
        """Extract and clean data from a table with nested table support."""
        table_data = []
        rows = table.find_all('tr')  # Find all tr elements in the table

        for row in rows:
            # Get direct child cells only to avoid nested table cells
            cells = row.find_all(['td', 'th'], recursive=False)
            row_data = []

            for cell in cells:
                # Check if cell contains nested tables
                nested_tables = cell.find_all('table')

                if nested_tables:
                    # Handle nested table content
                    nested_content = HTMLTableExtractor._extract_nested_cell_content(cell)
                    row_data.append(nested_content)
                else:
                    # Regular cell processing
                    cell_text = HTMLTableExtractor._clean_cell_text(cell)
                    row_data.append(cell_text)

            # Only add rows with meaningful content
            if row_data and any(cell.strip() for cell in row_data):
                table_data.append(row_data)

        # Ensure consistent column count
        if table_data:
            max_cols = max(len(row) for row in table_data)
            for row in table_data:
                while len(row) < max_cols:
                    row.append('')
        else:
            logger.warning("No data rows were extracted from the table")

        return table_data

    @staticmethod
    def _extract_nested_cell_content(cell) -> str:
        """Extract content from a cell that may contain nested tables."""
        nested_tables = cell.find_all('table')

        if not nested_tables:
            return HTMLTableExtractor._clean_cell_text(cell)

        # Process nested tables and combine with cell text
        cell_content_parts = []

        # Extract content from each nested table
        for nested_table in nested_tables:
            nested_rows = nested_table.find_all('tr')
            nested_content = []

            for nested_row in nested_rows:
                nested_cells = nested_row.find_all(['td', 'th'])
                nested_row_text = []

                for nested_cell in nested_cells:
                    nested_cell_text = HTMLTableExtractor._clean_cell_text(nested_cell)
                    if nested_cell_text:
                        nested_row_text.append(nested_cell_text)

                if nested_row_text:
                    nested_content.append(' | '.join(nested_row_text))

            if nested_content:
                cell_content_parts.append(' ; '.join(nested_content))

        # Combine all parts
        if cell_content_parts:
            return ' [NESTED: ' + ' || '.join(cell_content_parts) + ']'
        else:
            return HTMLTableExtractor._clean_cell_text(cell)

    @staticmethod
    def _extract_table_data(table) -> List[List[str]]:
        """Extract and clean data from a table (legacy method for compatibility)."""
        return HTMLTableExtractor._extract_nested_table_data(table)

    @staticmethod
    def _clean_cell_text(cell) -> str:
        """Clean and normalize cell text content."""
        # Get text content, preserving some structure
        text = cell.get_text(separator=' ', strip=True)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove common HTML artifacts
        text = re.sub(r'[\r\n\t]+', ' ', text)

        # Clean up special characters
        text = text.replace('\u00a0', ' ')  # Non-breaking space
        text = text.replace('\u2013', '-')  # En dash
        text = text.replace('\u2014', '-')  # Em dash
        text = text.replace('\u2019', "'")  # Right single quotation mark

        return text.strip()

    @staticmethod
    def extract_all_tables(html_content: str) -> Dict[str, List[List[str]]]:
        """Extract all tables from HTML content with identification."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            tables = soup.find_all('table')

            extracted_tables = {}

            for idx, table in enumerate(tables):
                table_id = table.get('id', f'table_{idx}')
                table_class = ' '.join(table.get('class', []))

                # Create a descriptive name for the table
                if table_id != f'table_{idx}':
                    table_name = table_id
                elif table_class:
                    table_name = f"table_class_{table_class.replace(' ', '_')}"
                else:
                    table_name = f"table_{idx}"

                table_data = HTMLTableExtractor._extract_table_data(table)
                if table_data:
                    extracted_tables[table_name] = table_data

            logger.info(f"Extracted {len(extracted_tables)} tables from HTML")
            return extracted_tables

        except Exception as e:
            logger.error(f"Error extracting all tables: {e}")
            return {}

class WordReportGenerator:
    """Enhanced class for generating beautifully formatted Word documents with nested table support."""

    def __init__(self):
        self.document = Document()
        self.setup_styles()

    def setup_styles(self):
        """Define enhanced styles for the Word document."""
        # Set document margins
        sections = self.document.sections
        for section in sections:
            section.top_margin = Inches(0.5)
            section.bottom_margin = Inches(0.5)
            section.left_margin = Inches(0.5)
            section.right_margin = Inches(0.5)
    
    def create_report(self, table_data: List[List[str]], filename: str, title: str = "Vendor Registration Report", search_params: Dict = None):
        """Create a beautifully formatted Word document report."""
        try:
            if not table_data:
                logger.warning("No data to create report")
                return False

            # Add report title
            title_paragraph = self.document.add_heading(title, level=1)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Add timestamp
            timestamp_paragraph = self.document.add_paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            timestamp_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            timestamp_run = timestamp_paragraph.runs[0]
            timestamp_run.italic = True
            timestamp_run.font.size = Pt(10)

            # Add search parameters if provided
            if search_params:
                self._add_search_parameters(search_params)

            # Add spacing before table
            self.document.add_paragraph()

            # Create table with data
            if table_data:
                # Create table with appropriate number of columns
                num_cols = len(table_data[0])
                table = self.document.add_table(rows=1, cols=num_cols)
                table.style = 'Table Grid'

                # Add headers
                header_row = table.rows[0]
                for col_idx, header in enumerate(table_data[0]):
                    cell = header_row.cells[col_idx]
                    cell.text = header
                    # Style header cells
                    self._style_header_cell(cell)

                # Add data rows
                for row_data in table_data[1:]:
                    row = table.add_row()
                    for col_idx, cell_value in enumerate(row_data):
                        if col_idx < len(row.cells):  # Safety check
                            cell = row.cells[col_idx]
                            # Process cell content including nested tables and PDF links
                            # Pass row data for vendor code extraction
                            self._populate_cell_with_context(cell, cell_value, row_data)

                # Auto-fit table
                self._auto_fit_table(table)

                # Add summary information
                self._add_summary_info(len(table_data) - 1, num_cols)

            # Save the document
            self.document.save(filename)
            logger.info(f"Word report saved as: {filename}")
            return True

        except Exception as e:
            logger.error(f"Error creating Word report: {e}")
            return False

    def _add_search_parameters(self, search_params: Dict):
        """Add search parameters to the Word document."""
        if not search_params:
            return

        # Add search parameters header
        param_heading = self.document.add_heading("Search Parameters:", level=2)

        # Create a simple table for parameters
        param_table = self.document.add_table(rows=1, cols=2)
        param_table.style = 'Light Shading Accent 1'

        # Header row
        header_row = param_table.rows[0]
        header_row.cells[0].text = "Parameter"
        header_row.cells[1].text = "Value"

        for cell in header_row.cells:
            self._style_header_cell(cell)

        # Add parameter rows
        for key, value in search_params.items():
            if value:  # Only show non-empty parameters
                row = param_table.add_row()
                row.cells[0].text = key.replace('_', ' ').title()
                row.cells[1].text = str(value)

        # Add spacing after parameters
        self.document.add_paragraph()

    def _populate_cell(self, cell, cell_value):
        """Populate Word table cell with content, handling nested tables and PDF links."""
        self._populate_cell_with_context(cell, cell_value, None)

    def _populate_cell_with_context(self, cell, cell_value, row_data):
        """Populate Word table cell with content, handling nested tables and PDF links."""
        if not cell_value:
            return

        cell_value = str(cell_value)

        # Check for PDF links first
        pdf_links = self._extract_pdf_links(cell_value)

        # Get vendor code from row data (usually first column)
        vendor_code = ""
        if row_data and len(row_data) > 0:
            vendor_code = str(row_data[0])[:20]  # First 20 chars as vendor identifier

        # Check if this is nested table content
        if '[NESTED:' in cell_value and ']' in cell_value:
            # Handle nested table content
            self._populate_nested_cell(cell, cell_value, pdf_links, vendor_code)
        else:
            # Regular cell content
            self._populate_regular_cell(cell, cell_value, pdf_links, vendor_code)

    def _extract_pdf_links(self, content: str) -> List[str]:
        """Extract PDF download links from content."""
        pdf_links = []
        # Look for PDF links in the content
        import re
        pdf_pattern = r'https?://[^\s<>"]+\.pdf'
        pdf_links = re.findall(pdf_pattern, content, re.IGNORECASE)
        return pdf_links

    def _populate_regular_cell(self, cell, cell_value: str, pdf_links: List[str], vendor_code: str = ""):
        """Populate regular cell content with PDF download buttons."""
        try:
            # Remove PDF links from display text for cleaner appearance
            display_text = cell_value
            for link in pdf_links:
                display_text = display_text.replace(link, "").strip()

            # Clean up extra spaces and formatting
            display_text = ' '.join(display_text.split())

            cell.text = display_text

            # Add PDF download buttons if any
            if pdf_links:
                self._add_pdf_buttons(cell, pdf_links, vendor_code)

        except Exception as e:
            logger.warning(f"Error populating regular cell: {e}")
            cell.text = cell_value

    def _populate_nested_cell(self, cell, nested_value: str, pdf_links: List[str], vendor_code: str = ""):
        """Populate cell with actual nested table inside the cell."""
        try:
            # Extract content between [NESTED: and ]
            start_marker = '[NESTED:'
            end_marker = ']'

            start_idx = nested_value.find(start_marker)
            end_idx = nested_value.find(end_marker, start_idx)

            if start_idx == -1 or end_idx == -1:
                cell.text = nested_value
                return

            # Get the nested content
            nested_part = nested_value[start_idx + len(start_marker):end_idx].strip()

            # Clear cell content
            cell.text = ""

            # Parse nested content into table structure
            nested_tables = self._parse_nested_content(nested_part)

            # Create actual nested table inside the cell
            if nested_tables:
                # Add each nested table
                for table_idx, table_data in enumerate(nested_tables):
                    if table_idx > 0:
                        # Add spacing between multiple nested tables
                        cell.add_paragraph()

                    # Create nested table inside the cell
                    nested_table = cell.add_table(rows=len(table_data), cols=len(table_data[0]) if table_data else 1)
                    nested_table.style = 'Light List Accent 1'

                    # Populate nested table
                    for row_idx, row_data in enumerate(table_data):
                        for col_idx, cell_data in enumerate(row_data):
                            if row_idx < len(nested_table.rows) and col_idx < len(nested_table.rows[row_idx].cells):
                                nested_cell = nested_table.rows[row_idx].cells[col_idx]
                                nested_cell.text = str(cell_data)
                                # Style nested table cells
                                nested_cell.paragraphs[0].runs[0].font.size = Pt(8)

            # Add PDF download buttons if any
            if pdf_links:
                self._add_pdf_buttons(cell, pdf_links, vendor_code)

        except Exception as e:
            logger.warning(f"Error creating nested table: {e}")
            cell.text = nested_value

    def _parse_nested_content(self, nested_content: str) -> List[List[List[str]]]:
        """Parse nested content into table structure."""
        try:
            tables = []

            # Split by different nested tables (|| separator)
            table_parts = nested_content.split(' || ')

            for table_part in table_parts:
                if not table_part.strip():
                    continue

                # Split by rows (; separator)
                rows = table_part.split(' ; ')
                table_data = []

                for row in rows:
                    if not row.strip():
                        continue
                    # Split by cells (| separator)
                    cells = [cell.strip() for cell in row.split(' | ') if cell.strip()]
                    if cells:
                        table_data.append(cells)

                if table_data:
                    tables.append(table_data)

            return tables

        except Exception as e:
            logger.warning(f"Error parsing nested content: {e}")
            return []

    def _add_pdf_buttons(self, cell, pdf_links: List[str], vendor_code: str):
        """Add PDF download buttons to cell."""
        if not pdf_links:
            return

        # Add spacing
        cell.add_paragraph()

        # Add PDF buttons
        for i, link in enumerate(pdf_links, 1):
            button_paragraph = cell.add_paragraph()
            button_run = button_paragraph.add_run(f"📄 Download PDF {i}")
            button_run.bold = True
            button_run.font.size = Pt(8)

            # Add vendor code if available
            if vendor_code:
                code_run = button_paragraph.add_run(f" ({vendor_code})")
                code_run.font.size = Pt(7)
                code_run.italic = True

            # Add the actual link (clickable)
            link_paragraph = cell.add_paragraph()
            link_run = link_paragraph.add_run(link)
            link_run.font.size = Pt(7)
            link_run.font.color.rgb = None  # Blue color for links

    def _populate_regular_cell(self, cell, cell_value: str, pdf_links: List[str]):
        """Populate cell with regular content and PDF links."""
        # Clean the content (remove PDF URLs from display text)
        display_text = cell_value
        for link in pdf_links:
            display_text = display_text.replace(link, "").strip()

        # Set the main content
        cell.text = display_text

        # Add PDF download buttons if any
        if pdf_links:
            paragraph = cell.paragraphs[0]
            paragraph.add_run("\n\n📄 Documents:")
            for i, link in enumerate(pdf_links, 1):
                paragraph.add_run(f"\n[Download PDF {i}] {link}")

    def _add_summary_info(self, total_records: int, total_columns: int):
        """Add summary information at the bottom of the Word document."""
        try:
            # Add spacing before summary
            self.document.add_paragraph()

            # Add summary header
            summary_heading = self.document.add_heading("Summary", level=2)

            # Add summary information
            summary_paragraph = self.document.add_paragraph()
            summary_paragraph.add_run(f"Total Records: {total_records}\n")
            summary_paragraph.add_run(f"Total Columns: {total_columns}")

        except Exception as e:
            logger.warning(f"Could not add summary info: {e}")

    def _style_header_cell(self, cell):
        """Apply header styling to a Word table cell."""
        try:
            # Set background color (blue)
            shading_elm = parse_xml(r'<w:shd {} w:fill="366092"/>'.format(nsdecls('w')))
            cell._tc.get_or_add_tcPr().append(shading_elm)

            # Style the text
            paragraph = cell.paragraphs[0]
            run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
            run.bold = True
            run.font.size = Pt(11)
            run.font.color.rgb = None  # White text

            # Center alignment
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        except Exception as e:
            logger.warning(f"Could not style header cell: {e}")

    def _auto_fit_table(self, table):
        """Auto-fit table columns in Word document."""
        try:
            # Set table to auto-fit contents
            table.autofit = True

            # Set column widths based on content
            for row in table.rows:
                for cell in row.cells:
                    # Set minimum width for cells with nested content
                    if "📋 NESTED DATA:" in cell.text:
                        cell.width = Inches(3.0)  # Wider for nested content
                    elif "📄 Documents:" in cell.text:
                        cell.width = Inches(2.5)  # Medium width for PDF links

        except Exception as e:
            logger.warning(f"Could not auto-fit table: {e}")

def main():
    """Main function to run the vendor API tool."""
    parser = argparse.ArgumentParser(description='Vendor Registration Portal API Tool')
    parser.add_argument('--config', help='Configuration file path (JSON format)')
    parser.add_argument('--output', default='vendor_report.docx', help='Output Word document file name')
    parser.add_argument('--factory-id', help='Factory ID to search for')
    parser.add_argument('--location', help='Location to search for')
    parser.add_argument('--industry-category', help='Industry category')
    parser.add_argument('--product', help='Product to search for')
    parser.add_argument('--used-for', help='Used for parameter')
    parser.add_argument('--vendor', help='Vendor name to search for')
    parser.add_argument('--pagination', action='store_true', help='Enable pagination to get all data')
    
    args = parser.parse_args()
    
    # Initialize components
    api_tool = VendorAPITool()
    extractor = HTMLTableExtractor()
    word_generator = WordReportGenerator()
    
    # Prepare search parameters
    search_params = {}
    if args.factory_id:
        search_params['factory_id'] = args.factory_id
    if args.location:
        search_params['location'] = args.location
    if args.industry_category:
        search_params['industry_category'] = args.industry_category
    if args.product:
        search_params['product'] = args.product
    if args.used_for:
        search_params['used_for'] = args.used_for
    if args.vendor:
        search_params['vendor'] = args.vendor
    
    # Load configuration from file if provided
    if args.config:
        try:
            with open(args.config, 'r') as f:
                config = json.load(f)
                search_params.update(config.get('search_params', {}))
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
    
    # Make API request
    logger.info("Starting vendor data extraction...")
    
    if search_params:
        html_content = api_tool.make_search_request(search_params)
    else:
        html_content = api_tool.get_paginated_data()
    
    if not html_content:
        logger.error("Failed to get data from API")
        return 1
    
    # Extract table data
    table_data = extractor.extract_main_table(html_content)
    
    if not table_data:
        logger.error("No table data extracted")
        return 1
    
    # Generate Word report
    title = f"Vendor Registration Report - {datetime.now().strftime('%Y-%m-%d')}"
    success = word_generator.create_report(table_data, args.output, title, search_params)
    
    if success:
        logger.info(f"Report generated successfully: {args.output}")
        return 0
    else:
        logger.error("Failed to generate report")
        return 1

if __name__ == "__main__":
    sys.exit(main())
