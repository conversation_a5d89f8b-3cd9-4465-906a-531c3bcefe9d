#!/usr/bin/env python3
"""
Vendor Registration Portal API Tool
===================================

A comprehensive tool for making API calls to the vendor registration portal,
extracting table data from HTML responses, and generating beautifully formatted Excel reports.
Enhanced with nested table support, comprehensive filter options, and PDF download functionality.

Author: AI Assistant
Date: 2025-07-02
"""

import requests
import json
import re
from typing import Dict, List, Tuple
from bs4 import BeautifulSoup
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import argparse
import sys
from datetime import datetime
from urllib.parse import urljoin
import logging
from typing import Dict, List, Optional, Tuple
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vendor_api_tool.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Comprehensive Filter Options Mapping
FILTER_OPTIONS = {
    'factories': {
        '1': 'Ammunition Factory Khadki',
        '2': 'Armament Research & Development Establishment',
        '3': 'Bharat Dynamics Limited',
        '4': 'Bharat Electronics Limited',
        '5': 'Defence Electronics Research Laboratory',
        '6': 'Defence Food Research Laboratory',
        '7': 'Defence Institute of Advanced Technology',
        '8': 'Defence Metallurgical Research Laboratory',
        '9': 'Defence Research & Development Laboratory',
        '10': 'Defence Research & Development Organisation',
        '11': 'Electronics & Radar Development Establishment',
        '12': 'Gas Turbine Research Establishment',
        '13': 'Gun Carriage Factory Jabalpur',
        '14': 'Heavy Alloy Penetrator Project',
        '15': 'Heavy Vehicles Factory Avadi',
        '16': 'High Energy Materials Research Laboratory',
        '17': 'Indian Naval Ship Shivaji',
        '18': 'Instruments Research & Development Establishment',
        '19': 'Laser Science & Technology Centre',
        '20': 'Machine Tool Prototype Factory',
        '21': 'Mazagon Dock Shipbuilders Limited',
        '22': 'Mishra Dhatu Nigam Limited',
        '23': 'Naval Chemical & Metallurgical Laboratory',
        '24': 'Naval Physical & Oceanographic Laboratory',
        '25': 'Naval Science & Technological Laboratory',
        '26': 'Ordnance Cable Factory Chandigarh',
        '27': 'Ordnance Equipment Factory Hazratpur',
        '28': 'Ordnance Factory Ambajhari',
        '29': 'Ordnance Factory Badmal',
        '30': 'Ordnance Factory Bhandara',
        '31': 'Ordnance Factory Board',
        '32': 'Ordnance Factory Dehradun',
        '33': 'Ordnance Factory Dehu Road',
        '34': 'Ordnance Factory Itarsi',
        '35': 'Ordnance Factory Khamaria',
        '36': 'Ordnance Factory Medak',
        '37': 'Ordnance Factory Muradnagar',
        '38': 'Ordnance Factory Trichy',
        '39': 'Ordnance Factory Varangaon',
        '40': 'Ordnance Factory Yeddumailaram',
        '41': 'Research Centre Imarat',
        '42': 'Terminal Ballistics Research Laboratory',
        '43': 'Vehicle Factory Jabalpur'
    },
    'industry_categories': {
        'L': 'Large Scale',
        'M': 'Medium Scale',
        'S': 'Micro and Small Enterprises',
        'SS': 'Micro and Small Enterprises owned by SC/ST entrepreneurs',
        'U': 'Udyam'
    },
    'used_for': {
        'R': 'Raw material',
        'C': 'Component',
        'E': 'End Product',
        'S': 'Special Tools',
        'P': 'Production Consumables'
    },
    'sorting': {
        'valid_upto': 'Register Upto',
        'name': 'Vendor Name'
    }
}

class VendorAPITool:
    """Main class for handling vendor registration portal API interactions."""
    
    def __init__(self):
        self.base_url = "https://ddpdoo.gov.in"
        self.search_endpoint = "/vendor/general_reports/show/registered_vendors/search"
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """Configure the session with default headers."""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Host': 'ddpdoo.gov.in',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        self.connection_verified = False

    def verify_connection(self) -> bool:
        """Verify connection to the API endpoint."""
        try:
            print("🔍 Verifying connection to DDPDOO portal...")
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            if "vendor" in response.text.lower() and "search" in response.text.lower():
                print("✅ Connection verified successfully!")
                self.connection_verified = True
                return True
            else:
                print("⚠️ Connected but page content unexpected")
                return False

        except requests.exceptions.Timeout:
            print("❌ Connection timeout - please check your internet connection")
            return False
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - please check your internet connection")
            return False
        except requests.exceptions.HTTPError as e:
            print(f"❌ HTTP error: {e}")
            return False
        except Exception as e:
            print(f"❌ Connection verification failed: {e}")
            return False

    def get_system_status(self) -> dict:
        """Get comprehensive system status information."""
        status = {
            'connection': self.connection_verified,
            'session_active': bool(self.session),
            'base_url': self.base_url,
            'endpoint': self.search_endpoint,
            'user_agent': self.session.headers.get('User-Agent', 'Not set'),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Test basic connectivity
        try:
            response = self.session.head(self.base_url, timeout=5)
            status['server_reachable'] = response.status_code == 200
            status['response_time'] = response.elapsed.total_seconds()
        except:
            status['server_reachable'] = False
            status['response_time'] = None

        return status

    def get_csrf_token(self) -> Optional[str]:
        """Extract CSRF token from the search page."""
        try:
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrf_ofb'})
            
            if csrf_input:
                return csrf_input.get('value')
            else:
                logger.warning("CSRF token not found in page")
                return None
                
        except Exception as e:
            logger.error(f"Error getting CSRF token: {e}")
            return None
    
    def make_search_request(self, search_params: Dict[str, str]) -> Optional[str]:
        """Make a POST request to search for vendors."""
        try:
            csrf_token = self.get_csrf_token()
            if not csrf_token:
                logger.error("Could not obtain CSRF token")
                return None
            
            # Prepare form data
            form_data = {
                'csrf_ofb': csrf_token,
                'factory_id': search_params.get('factory_id', ''),
                'location': search_params.get('location', ''),
                'industry_category': search_params.get('industry_category', ''),
                'product': search_params.get('product', ''),
                'used_for': search_params.get('used_for', ''),
                'vendor': search_params.get('vendor', ''),
                'search': 'search'
            }
            
            # Update headers for POST request
            self.session.headers.update({
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': urljoin(self.base_url, self.search_endpoint)
            })
            
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.post(url, data=form_data)
            response.raise_for_status()
            
            logger.info(f"Search request successful. Response length: {len(response.text)}")
            return response.text
            
        except Exception as e:
            logger.error(f"Error making search request: {e}")
            return None
    
    def get_paginated_data(self, offset: int = 0) -> Optional[str]:
        """Get paginated data using GET request with connection verification."""
        try:
            # Verify connection if not already done
            if not self.connection_verified:
                if not self.verify_connection():
                    return None

            if offset == 0:
                url = urljoin(self.base_url, self.search_endpoint)
            else:
                url = urljoin(self.base_url, f"{self.search_endpoint}/{offset}")

            response = self.session.get(url)
            response.raise_for_status()

            logger.info(f"Paginated request successful for offset {offset}")
            return response.text

        except Exception as e:
            logger.error(f"Error getting paginated data: {e}")
            return None

    def download_pdf(self, download_url: str, filename: str = None) -> bool:
        """Download PDF file from the given URL."""
        try:
            # Handle relative URLs
            if download_url.startswith('/'):
                download_url = urljoin(self.base_url, download_url)

            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"vendor_report_{timestamp}.pdf"

            # Ensure filename has .pdf extension
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'

            logger.info(f"Downloading PDF from: {download_url}")
            response = self.session.get(download_url)
            response.raise_for_status()

            # Save the PDF file
            with open(filename, 'wb') as f:
                f.write(response.content)

            logger.info(f"PDF downloaded successfully: {filename}")
            return True

        except Exception as e:
            logger.error(f"Error downloading PDF: {e}")
            return False

    def get_filter_options(self) -> Dict[str, Dict[str, str]]:
        """Get the comprehensive filter options mapping."""
        return FILTER_OPTIONS

class HTMLTableExtractor:
    """Enhanced class for extracting table data from HTML responses with nested table support."""

    @staticmethod
    def extract_main_table(html_content: str) -> List[List[str]]:
        """Extract the main table data from HTML content with nested table handling."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Find the main data table using multiple strategies
            tables = soup.find_all('table')

            if not tables:
                logger.warning("No tables found in HTML content")
                return []

            main_table = HTMLTableExtractor._find_main_table(tables)

            if not main_table:
                logger.warning("Could not identify main data table")
                return []

            # Enhanced extraction with nested table support
            table_data = HTMLTableExtractor._extract_nested_table_data(main_table)

            logger.info(f"Extracted {len(table_data)} rows from main table (with nested support)")
            return table_data

        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return []

    @staticmethod
    def extract_download_links(html_content: str) -> List[Dict[str, str]]:
        """Extract download links from HTML content."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            download_links = []

            # Look for download links with common patterns
            download_patterns = [
                {'tag': 'a', 'attrs': {'class': 'btn btn-warning'}},
                {'tag': 'a', 'attrs': {'href': re.compile(r'.*download.*', re.I)}},
                {'tag': 'a', 'text': re.compile(r'.*download.*', re.I)},
                {'tag': 'a', 'attrs': {'href': re.compile(r'.*pdf.*', re.I)}},
            ]

            for pattern in download_patterns:
                if 'text' in pattern:
                    links = soup.find_all(pattern['tag'], string=pattern['text'])
                else:
                    links = soup.find_all(pattern['tag'], pattern.get('attrs', {}))

                for link in links:
                    href = link.get('href')
                    text = link.get_text(strip=True)
                    if href and text:
                        download_links.append({
                            'url': href,
                            'text': text,
                            'type': 'pdf' if 'pdf' in href.lower() else 'download'
                        })

            logger.info(f"Found {len(download_links)} download links")
            return download_links

        except Exception as e:
            logger.error(f"Error extracting download links: {e}")
            return []

    @staticmethod
    def _find_main_table(tables):
        """Find the main data table from a list of tables."""
        # Strategy 1: Look for tables with specific classes or IDs that indicate data tables
        data_table_indicators = ['data', 'results', 'vendors', 'list', 'grid', 'table-striped', 'table-bordered']

        for table in tables:
            table_class = table.get('class', [])
            table_id = table.get('id', '')

            if any(indicator in ' '.join(table_class).lower() for indicator in data_table_indicators):
                return table
            if any(indicator in table_id.lower() for indicator in data_table_indicators):
                return table

        # Strategy 2: Find table with the most data rows (excluding header-only tables)
        best_table = None
        max_data_rows = 0

        for table in tables:
            rows = table.find_all('tr')
            data_rows = 0

            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) > 1:  # Must have multiple columns
                    # Check if row contains actual data (not just headers or empty)
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    if any(text and len(text) > 2 for text in cell_texts):
                        data_rows += 1

            if data_rows > max_data_rows:
                max_data_rows = data_rows
                best_table = table

        return best_table

    @staticmethod
    def _extract_nested_table_data(table) -> List[List[str]]:
        """Extract and clean data from a table with nested table support."""
        table_data = []
        rows = table.find_all('tr', recursive=False)  # Only direct children to avoid nested table rows

        for row in rows:
            cells = row.find_all(['td', 'th'], recursive=False)  # Only direct children
            row_data = []

            for cell in cells:
                # Check if cell contains nested tables
                nested_tables = cell.find_all('table')

                if nested_tables:
                    # Handle nested table content
                    nested_content = HTMLTableExtractor._extract_nested_cell_content(cell)
                    row_data.append(nested_content)
                else:
                    # Regular cell processing
                    cell_text = HTMLTableExtractor._clean_cell_text(cell)
                    row_data.append(cell_text)

            # Only add rows with meaningful content
            if row_data and any(cell.strip() for cell in row_data):
                table_data.append(row_data)

        # Ensure consistent column count
        if table_data:
            max_cols = max(len(row) for row in table_data)
            for row in table_data:
                while len(row) < max_cols:
                    row.append('')

        return table_data

    @staticmethod
    def _extract_nested_cell_content(cell) -> str:
        """Extract content from a cell that may contain nested tables."""
        nested_tables = cell.find_all('table')

        if not nested_tables:
            return HTMLTableExtractor._clean_cell_text(cell)

        # Process nested tables and combine with cell text
        cell_content_parts = []

        # Extract content from each nested table
        for nested_table in nested_tables:
            nested_rows = nested_table.find_all('tr')
            nested_content = []

            for nested_row in nested_rows:
                nested_cells = nested_row.find_all(['td', 'th'])
                nested_row_text = []

                for nested_cell in nested_cells:
                    nested_cell_text = HTMLTableExtractor._clean_cell_text(nested_cell)
                    if nested_cell_text:
                        nested_row_text.append(nested_cell_text)

                if nested_row_text:
                    nested_content.append(' | '.join(nested_row_text))

            if nested_content:
                cell_content_parts.append(' ; '.join(nested_content))

        # Combine all parts
        if cell_content_parts:
            return ' [NESTED: ' + ' || '.join(cell_content_parts) + ']'
        else:
            return HTMLTableExtractor._clean_cell_text(cell)

    @staticmethod
    def _extract_table_data(table) -> List[List[str]]:
        """Extract and clean data from a table (legacy method for compatibility)."""
        return HTMLTableExtractor._extract_nested_table_data(table)

    @staticmethod
    def _clean_cell_text(cell) -> str:
        """Clean and normalize cell text content."""
        # Get text content, preserving some structure
        text = cell.get_text(separator=' ', strip=True)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove common HTML artifacts
        text = re.sub(r'[\r\n\t]+', ' ', text)

        # Clean up special characters
        text = text.replace('\u00a0', ' ')  # Non-breaking space
        text = text.replace('\u2013', '-')  # En dash
        text = text.replace('\u2014', '-')  # Em dash
        text = text.replace('\u2019', "'")  # Right single quotation mark

        return text.strip()

    @staticmethod
    def extract_all_tables(html_content: str) -> Dict[str, List[List[str]]]:
        """Extract all tables from HTML content with identification."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            tables = soup.find_all('table')

            extracted_tables = {}

            for idx, table in enumerate(tables):
                table_id = table.get('id', f'table_{idx}')
                table_class = ' '.join(table.get('class', []))

                # Create a descriptive name for the table
                if table_id != f'table_{idx}':
                    table_name = table_id
                elif table_class:
                    table_name = f"table_class_{table_class.replace(' ', '_')}"
                else:
                    table_name = f"table_{idx}"

                table_data = HTMLTableExtractor._extract_table_data(table)
                if table_data:
                    extracted_tables[table_name] = table_data

            logger.info(f"Extracted {len(extracted_tables)} tables from HTML")
            return extracted_tables

        except Exception as e:
            logger.error(f"Error extracting all tables: {e}")
            return {}

class ExcelReportGenerator:
    """Enhanced class for generating beautifully formatted Excel reports with nested table support."""

    def __init__(self):
        self.workbook = Workbook()
        self.worksheet = self.workbook.active
        self.setup_styles()

    def setup_styles(self):
        """Define enhanced styles for the Excel report."""
        # Header style
        self.header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

        # Data style
        self.data_font = Font(name='Arial', size=10)
        self.data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # Nested data style (for cells containing nested table data)
        self.nested_font = Font(name='Arial', size=9, italic=True)
        self.nested_fill = PatternFill(start_color='E8F4FD', end_color='E8F4FD', fill_type='solid')
        self.nested_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)

        # Border style
        thin_border = Side(border_style='thin', color='000000')
        self.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)

        # Alternating row colors
        self.even_row_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        self.odd_row_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
    
    def create_report(self, table_data: List[List[str]], filename: str, title: str = "Vendor Registration Report", search_params: Dict = None):
        """Create a beautifully formatted Excel report."""
        try:
            if not table_data:
                logger.warning("No data to create report")
                return False

            # Set worksheet title
            self.worksheet.title = "Vendor Data"

            # Add report title
            self.worksheet.merge_cells('A1:' + get_column_letter(len(table_data[0])) + '1')
            title_cell = self.worksheet['A1']
            title_cell.value = title
            title_cell.font = Font(name='Arial', size=16, bold=True, color='366092')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')

            # Add timestamp
            timestamp_row = 2
            self.worksheet.merge_cells(f'A{timestamp_row}:' + get_column_letter(len(table_data[0])) + str(timestamp_row))
            timestamp_cell = self.worksheet[f'A{timestamp_row}']
            timestamp_cell.value = f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            timestamp_cell.font = Font(name='Arial', size=10, italic=True)
            timestamp_cell.alignment = Alignment(horizontal='center')

            # Add search parameters if provided
            current_row = 3
            if search_params:
                current_row = self._add_search_parameters(search_params, current_row)

            # Start data from next row
            start_row = current_row + 1

            # Add headers
            if table_data:
                headers = table_data[0]
                for col_idx, header in enumerate(headers, 1):
                    cell = self.worksheet.cell(row=start_row, column=col_idx)
                    cell.value = header
                    cell.font = self.header_font
                    cell.fill = self.header_fill
                    cell.alignment = self.header_alignment
                    cell.border = self.border

                # Add data rows with enhanced nested table support
                for row_idx, row_data in enumerate(table_data[1:], start_row + 1):
                    for col_idx, cell_value in enumerate(row_data, 1):
                        cell = self.worksheet.cell(row=row_idx, column=col_idx)

                        # Enhanced processing for nested table content
                        processed_value, is_nested = self._process_nested_cell_value(cell_value)
                        cell.value = processed_value

                        # Apply appropriate styling based on content type
                        if is_nested:
                            cell.font = self.nested_font
                            cell.fill = self.nested_fill
                            cell.alignment = self.nested_alignment
                            # Increase row height for nested content
                            self.worksheet.row_dimensions[row_idx].height = 60
                        else:
                            cell.font = self.data_font
                            cell.alignment = self.data_alignment
                            # Alternating row colors for regular content
                            if row_idx % 2 == 0:
                                cell.fill = self.even_row_fill
                            else:
                                cell.fill = self.odd_row_fill

                        cell.border = self.border

                # Auto-adjust column widths
                self.auto_adjust_columns()

                # Add summary information
                self._add_summary_info(len(table_data) - 1, len(headers))

            # Save the workbook
            self.workbook.save(filename)
            logger.info(f"Excel report saved as: {filename}")
            return True

        except Exception as e:
            logger.error(f"Error creating Excel report: {e}")
            return False

    def _add_search_parameters(self, search_params: Dict, start_row: int) -> int:
        """Add search parameters to the report."""
        if not search_params:
            return start_row

        # Add search parameters header
        self.worksheet.merge_cells(f'A{start_row}:B{start_row}')
        param_header = self.worksheet[f'A{start_row}']
        param_header.value = "Search Parameters:"
        param_header.font = Font(name='Arial', size=12, bold=True, color='366092')
        param_header.alignment = Alignment(horizontal='left', vertical='center')

        current_row = start_row + 1

        for key, value in search_params.items():
            if value:  # Only show non-empty parameters
                # Parameter name
                param_cell = self.worksheet.cell(row=current_row, column=1)
                param_cell.value = key.replace('_', ' ').title() + ":"
                param_cell.font = Font(name='Arial', size=10, bold=True)

                # Parameter value
                value_cell = self.worksheet.cell(row=current_row, column=2)
                value_cell.value = str(value)
                value_cell.font = Font(name='Arial', size=10)

                current_row += 1

        return current_row

    def _process_nested_cell_value(self, value: str) -> Tuple[str, bool]:
        """Process cell value with nested table support, returns (processed_value, is_nested)."""
        if not value or not isinstance(value, str):
            return value, False

        # Check if this is nested table content
        if '[NESTED:' in value and ']' in value:
            # Extract and format nested content
            nested_content = self._format_nested_content(value)
            return nested_content, True

        # Regular cell processing
        processed_value = self._process_cell_value(value)
        return processed_value, False

    def _format_nested_content(self, nested_value: str) -> str:
        """Format nested table content for better Excel display."""
        try:
            # Extract content between [NESTED: and ]
            start_marker = '[NESTED:'
            end_marker = ']'

            start_idx = nested_value.find(start_marker)
            end_idx = nested_value.find(end_marker, start_idx)

            if start_idx == -1 or end_idx == -1:
                return nested_value

            # Get the nested content
            nested_part = nested_value[start_idx + len(start_marker):end_idx].strip()

            # Format the nested content for better readability
            # Replace separators with line breaks for better Excel display
            formatted_content = nested_part.replace(' || ', '\n\n')  # Between different nested tables
            formatted_content = formatted_content.replace(' ; ', '\n')  # Between rows in same table
            formatted_content = formatted_content.replace(' | ', ' → ')  # Between cells in same row

            # Add prefix to indicate nested content
            return f"📋 NESTED DATA:\n{formatted_content}"

        except Exception as e:
            logger.warning(f"Error formatting nested content: {e}")
            return nested_value

    def _process_cell_value(self, value: str):
        """Process cell value to appropriate data type."""
        if not value or not isinstance(value, str):
            return value

        # Try to convert to number
        try:
            # Check if it's an integer
            if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                return int(value)

            # Check if it's a float
            float_val = float(value)
            return float_val
        except ValueError:
            pass

        # Return as string
        return value

    def _add_summary_info(self, total_records: int, total_columns: int):
        """Add summary information at the bottom of the report."""
        try:
            # Find the last row with data
            last_row = self.worksheet.max_row + 2

            # Add summary header
            summary_cell = self.worksheet.cell(row=last_row, column=1)
            summary_cell.value = "Summary:"
            summary_cell.font = Font(name='Arial', size=12, bold=True, color='366092')

            # Add record count
            record_cell = self.worksheet.cell(row=last_row + 1, column=1)
            record_cell.value = f"Total Records: {total_records}"
            record_cell.font = Font(name='Arial', size=10)

            # Add column count
            column_cell = self.worksheet.cell(row=last_row + 2, column=1)
            column_cell.value = f"Total Columns: {total_columns}"
            column_cell.font = Font(name='Arial', size=10)

        except Exception as e:
            logger.warning(f"Could not add summary info: {e}")
    
    def auto_adjust_columns(self):
        """Auto-adjust column widths based on content with nested table support."""
        for column in self.worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            has_nested_content = False

            for cell in column:
                try:
                    cell_value = str(cell.value) if cell.value else ""

                    # Check for nested content
                    if "📋 NESTED DATA:" in cell_value:
                        has_nested_content = True
                        # For nested content, calculate based on longest line
                        lines = cell_value.split('\n')
                        line_max = max(len(line) for line in lines) if lines else 0
                        if line_max > max_length:
                            max_length = line_max
                    else:
                        if len(cell_value) > max_length:
                            max_length = len(cell_value)
                except:
                    pass

            # Set column width with appropriate padding
            if has_nested_content:
                # Wider columns for nested content
                adjusted_width = min(max_length + 5, 80)
            else:
                # Standard width for regular content
                adjusted_width = min(max_length + 2, 50)

            self.worksheet.column_dimensions[column_letter].width = adjusted_width

def main():
    """Main function to run the vendor API tool."""
    parser = argparse.ArgumentParser(description='Vendor Registration Portal API Tool')
    parser.add_argument('--config', help='Configuration file path (JSON format)')
    parser.add_argument('--output', default='vendor_report.xlsx', help='Output Excel file name')
    parser.add_argument('--factory-id', help='Factory ID to search for')
    parser.add_argument('--location', help='Location to search for')
    parser.add_argument('--industry-category', help='Industry category')
    parser.add_argument('--product', help='Product to search for')
    parser.add_argument('--used-for', help='Used for parameter')
    parser.add_argument('--vendor', help='Vendor name to search for')
    parser.add_argument('--pagination', action='store_true', help='Enable pagination to get all data')
    
    args = parser.parse_args()
    
    # Initialize components
    api_tool = VendorAPITool()
    extractor = HTMLTableExtractor()
    excel_generator = ExcelReportGenerator()
    
    # Prepare search parameters
    search_params = {}
    if args.factory_id:
        search_params['factory_id'] = args.factory_id
    if args.location:
        search_params['location'] = args.location
    if args.industry_category:
        search_params['industry_category'] = args.industry_category
    if args.product:
        search_params['product'] = args.product
    if args.used_for:
        search_params['used_for'] = args.used_for
    if args.vendor:
        search_params['vendor'] = args.vendor
    
    # Load configuration from file if provided
    if args.config:
        try:
            with open(args.config, 'r') as f:
                config = json.load(f)
                search_params.update(config.get('search_params', {}))
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
    
    # Make API request
    logger.info("Starting vendor data extraction...")
    
    if search_params:
        html_content = api_tool.make_search_request(search_params)
    else:
        html_content = api_tool.get_paginated_data()
    
    if not html_content:
        logger.error("Failed to get data from API")
        return 1
    
    # Extract table data
    table_data = extractor.extract_main_table(html_content)
    
    if not table_data:
        logger.error("No table data extracted")
        return 1
    
    # Generate Excel report
    title = f"Vendor Registration Report - {datetime.now().strftime('%Y-%m-%d')}"
    success = excel_generator.create_report(table_data, args.output, title, search_params)
    
    if success:
        logger.info(f"Report generated successfully: {args.output}")
        return 0
    else:
        logger.error("Failed to generate report")
        return 1

if __name__ == "__main__":
    sys.exit(main())
