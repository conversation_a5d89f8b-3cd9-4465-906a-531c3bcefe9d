#!/usr/bin/env python3
"""
Vendor Registration Portal API Tool
===================================

A comprehensive tool for making API calls to the vendor registration portal,
extracting table data from HTML responses, and generating beautifully formatted Excel reports.

Author: AI Assistant
Date: 2025-07-02
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import argparse
import sys
from datetime import datetime
from urllib.parse import urljoin
import logging
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vendor_api_tool.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VendorAPITool:
    """Main class for handling vendor registration portal API interactions."""
    
    def __init__(self):
        self.base_url = "https://ddpdoo.gov.in"
        self.search_endpoint = "/vendor/general_reports/show/registered_vendors/search"
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """Configure the session with default headers."""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Host': 'ddpdoo.gov.in',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })
        
    def get_csrf_token(self) -> Optional[str]:
        """Extract CSRF token from the search page."""
        try:
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrf_ofb'})
            
            if csrf_input:
                return csrf_input.get('value')
            else:
                logger.warning("CSRF token not found in page")
                return None
                
        except Exception as e:
            logger.error(f"Error getting CSRF token: {e}")
            return None
    
    def make_search_request(self, search_params: Dict[str, str]) -> Optional[str]:
        """Make a POST request to search for vendors."""
        try:
            csrf_token = self.get_csrf_token()
            if not csrf_token:
                logger.error("Could not obtain CSRF token")
                return None
            
            # Prepare form data
            form_data = {
                'csrf_ofb': csrf_token,
                'factory_id': search_params.get('factory_id', ''),
                'location': search_params.get('location', ''),
                'industry_category': search_params.get('industry_category', ''),
                'product': search_params.get('product', ''),
                'used_for': search_params.get('used_for', ''),
                'vendor': search_params.get('vendor', ''),
                'search': 'search'
            }
            
            # Update headers for POST request
            self.session.headers.update({
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': self.base_url,
                'Referer': urljoin(self.base_url, self.search_endpoint)
            })
            
            url = urljoin(self.base_url, self.search_endpoint)
            response = self.session.post(url, data=form_data)
            response.raise_for_status()
            
            logger.info(f"Search request successful. Response length: {len(response.text)}")
            return response.text
            
        except Exception as e:
            logger.error(f"Error making search request: {e}")
            return None
    
    def get_paginated_data(self, offset: int = 0) -> Optional[str]:
        """Get paginated data using GET request."""
        try:
            if offset == 0:
                url = urljoin(self.base_url, self.search_endpoint)
            else:
                url = urljoin(self.base_url, f"{self.search_endpoint}/{offset}")
            
            response = self.session.get(url)
            response.raise_for_status()
            
            logger.info(f"Paginated request successful for offset {offset}")
            return response.text
            
        except Exception as e:
            logger.error(f"Error getting paginated data: {e}")
            return None

class HTMLTableExtractor:
    """Class for extracting table data from HTML responses."""

    @staticmethod
    def extract_main_table(html_content: str) -> List[List[str]]:
        """Extract the main table data from HTML content."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Find the main data table using multiple strategies
            tables = soup.find_all('table')

            if not tables:
                logger.warning("No tables found in HTML content")
                return []

            main_table = HTMLTableExtractor._find_main_table(tables)

            if not main_table:
                logger.warning("Could not identify main data table")
                return []

            table_data = HTMLTableExtractor._extract_table_data(main_table)

            logger.info(f"Extracted {len(table_data)} rows from main table")
            return table_data

        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return []

    @staticmethod
    def _find_main_table(tables):
        """Find the main data table from a list of tables."""
        # Strategy 1: Look for tables with specific classes or IDs that indicate data tables
        data_table_indicators = ['data', 'results', 'vendors', 'list', 'grid', 'table-striped', 'table-bordered']

        for table in tables:
            table_class = table.get('class', [])
            table_id = table.get('id', '')

            if any(indicator in ' '.join(table_class).lower() for indicator in data_table_indicators):
                return table
            if any(indicator in table_id.lower() for indicator in data_table_indicators):
                return table

        # Strategy 2: Find table with the most data rows (excluding header-only tables)
        best_table = None
        max_data_rows = 0

        for table in tables:
            rows = table.find_all('tr')
            data_rows = 0

            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) > 1:  # Must have multiple columns
                    # Check if row contains actual data (not just headers or empty)
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    if any(text and len(text) > 2 for text in cell_texts):
                        data_rows += 1

            if data_rows > max_data_rows:
                max_data_rows = data_rows
                best_table = table

        return best_table

    @staticmethod
    def _extract_table_data(table) -> List[List[str]]:
        """Extract and clean data from a table."""
        table_data = []
        rows = table.find_all('tr')

        for row in rows:
            cells = row.find_all(['td', 'th'])
            row_data = []

            for cell in cells:
                # Handle colspan and rowspan
                cell_text = HTMLTableExtractor._clean_cell_text(cell)
                row_data.append(cell_text)

            # Only add rows with meaningful content
            if row_data and any(cell.strip() for cell in row_data):
                table_data.append(row_data)

        # Ensure consistent column count
        if table_data:
            max_cols = max(len(row) for row in table_data)
            for row in table_data:
                while len(row) < max_cols:
                    row.append('')

        return table_data

    @staticmethod
    def _clean_cell_text(cell) -> str:
        """Clean and normalize cell text content."""
        # Get text content, preserving some structure
        text = cell.get_text(separator=' ', strip=True)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove common HTML artifacts
        text = re.sub(r'[\r\n\t]+', ' ', text)

        # Clean up special characters
        text = text.replace('\u00a0', ' ')  # Non-breaking space
        text = text.replace('\u2013', '-')  # En dash
        text = text.replace('\u2014', '-')  # Em dash
        text = text.replace('\u2019', "'")  # Right single quotation mark

        return text.strip()

    @staticmethod
    def extract_all_tables(html_content: str) -> Dict[str, List[List[str]]]:
        """Extract all tables from HTML content with identification."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            tables = soup.find_all('table')

            extracted_tables = {}

            for idx, table in enumerate(tables):
                table_id = table.get('id', f'table_{idx}')
                table_class = ' '.join(table.get('class', []))

                # Create a descriptive name for the table
                if table_id != f'table_{idx}':
                    table_name = table_id
                elif table_class:
                    table_name = f"table_class_{table_class.replace(' ', '_')}"
                else:
                    table_name = f"table_{idx}"

                table_data = HTMLTableExtractor._extract_table_data(table)
                if table_data:
                    extracted_tables[table_name] = table_data

            logger.info(f"Extracted {len(extracted_tables)} tables from HTML")
            return extracted_tables

        except Exception as e:
            logger.error(f"Error extracting all tables: {e}")
            return {}

class ExcelReportGenerator:
    """Class for generating beautifully formatted Excel reports."""
    
    def __init__(self):
        self.workbook = Workbook()
        self.worksheet = self.workbook.active
        self.setup_styles()
    
    def setup_styles(self):
        """Define styles for the Excel report."""
        # Header style
        self.header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        
        # Data style
        self.data_font = Font(name='Arial', size=10)
        self.data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
        
        # Border style
        thin_border = Side(border_style='thin', color='000000')
        self.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
        
        # Alternating row colors
        self.even_row_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        self.odd_row_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
    
    def create_report(self, table_data: List[List[str]], filename: str, title: str = "Vendor Registration Report", search_params: Dict = None):
        """Create a beautifully formatted Excel report."""
        try:
            if not table_data:
                logger.warning("No data to create report")
                return False

            # Set worksheet title
            self.worksheet.title = "Vendor Data"

            # Add report title
            self.worksheet.merge_cells('A1:' + get_column_letter(len(table_data[0])) + '1')
            title_cell = self.worksheet['A1']
            title_cell.value = title
            title_cell.font = Font(name='Arial', size=16, bold=True, color='366092')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')

            # Add timestamp
            timestamp_row = 2
            self.worksheet.merge_cells(f'A{timestamp_row}:' + get_column_letter(len(table_data[0])) + str(timestamp_row))
            timestamp_cell = self.worksheet[f'A{timestamp_row}']
            timestamp_cell.value = f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            timestamp_cell.font = Font(name='Arial', size=10, italic=True)
            timestamp_cell.alignment = Alignment(horizontal='center')

            # Add search parameters if provided
            current_row = 3
            if search_params:
                current_row = self._add_search_parameters(search_params, current_row)

            # Start data from next row
            start_row = current_row + 1

            # Add headers
            if table_data:
                headers = table_data[0]
                for col_idx, header in enumerate(headers, 1):
                    cell = self.worksheet.cell(row=start_row, column=col_idx)
                    cell.value = header
                    cell.font = self.header_font
                    cell.fill = self.header_fill
                    cell.alignment = self.header_alignment
                    cell.border = self.border

                # Add data rows
                for row_idx, row_data in enumerate(table_data[1:], start_row + 1):
                    for col_idx, cell_value in enumerate(row_data, 1):
                        cell = self.worksheet.cell(row=row_idx, column=col_idx)

                        # Try to convert numeric values
                        processed_value = self._process_cell_value(cell_value)
                        cell.value = processed_value

                        cell.font = self.data_font
                        cell.alignment = self.data_alignment
                        cell.border = self.border

                        # Alternating row colors
                        if row_idx % 2 == 0:
                            cell.fill = self.even_row_fill
                        else:
                            cell.fill = self.odd_row_fill

                # Auto-adjust column widths
                self.auto_adjust_columns()

                # Add summary information
                self._add_summary_info(len(table_data) - 1, len(headers))

            # Save the workbook
            self.workbook.save(filename)
            logger.info(f"Excel report saved as: {filename}")
            return True

        except Exception as e:
            logger.error(f"Error creating Excel report: {e}")
            return False

    def _add_search_parameters(self, search_params: Dict, start_row: int) -> int:
        """Add search parameters to the report."""
        if not search_params:
            return start_row

        # Add search parameters header
        self.worksheet.merge_cells(f'A{start_row}:B{start_row}')
        param_header = self.worksheet[f'A{start_row}']
        param_header.value = "Search Parameters:"
        param_header.font = Font(name='Arial', size=12, bold=True, color='366092')
        param_header.alignment = Alignment(horizontal='left', vertical='center')

        current_row = start_row + 1

        for key, value in search_params.items():
            if value:  # Only show non-empty parameters
                # Parameter name
                param_cell = self.worksheet.cell(row=current_row, column=1)
                param_cell.value = key.replace('_', ' ').title() + ":"
                param_cell.font = Font(name='Arial', size=10, bold=True)

                # Parameter value
                value_cell = self.worksheet.cell(row=current_row, column=2)
                value_cell.value = str(value)
                value_cell.font = Font(name='Arial', size=10)

                current_row += 1

        return current_row

    def _process_cell_value(self, value: str):
        """Process cell value to appropriate data type."""
        if not value or not isinstance(value, str):
            return value

        # Try to convert to number
        try:
            # Check if it's an integer
            if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                return int(value)

            # Check if it's a float
            float_val = float(value)
            return float_val
        except ValueError:
            pass

        # Return as string
        return value

    def _add_summary_info(self, total_records: int, total_columns: int):
        """Add summary information at the bottom of the report."""
        try:
            # Find the last row with data
            last_row = self.worksheet.max_row + 2

            # Add summary header
            summary_cell = self.worksheet.cell(row=last_row, column=1)
            summary_cell.value = "Summary:"
            summary_cell.font = Font(name='Arial', size=12, bold=True, color='366092')

            # Add record count
            record_cell = self.worksheet.cell(row=last_row + 1, column=1)
            record_cell.value = f"Total Records: {total_records}"
            record_cell.font = Font(name='Arial', size=10)

            # Add column count
            column_cell = self.worksheet.cell(row=last_row + 2, column=1)
            column_cell.value = f"Total Columns: {total_columns}"
            column_cell.font = Font(name='Arial', size=10)

        except Exception as e:
            logger.warning(f"Could not add summary info: {e}")
    
    def auto_adjust_columns(self):
        """Auto-adjust column widths based on content."""
        for column in self.worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # Set column width with some padding
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            self.worksheet.column_dimensions[column_letter].width = adjusted_width

def main():
    """Main function to run the vendor API tool."""
    parser = argparse.ArgumentParser(description='Vendor Registration Portal API Tool')
    parser.add_argument('--config', help='Configuration file path (JSON format)')
    parser.add_argument('--output', default='vendor_report.xlsx', help='Output Excel file name')
    parser.add_argument('--factory-id', help='Factory ID to search for')
    parser.add_argument('--location', help='Location to search for')
    parser.add_argument('--industry-category', help='Industry category')
    parser.add_argument('--product', help='Product to search for')
    parser.add_argument('--used-for', help='Used for parameter')
    parser.add_argument('--vendor', help='Vendor name to search for')
    parser.add_argument('--pagination', action='store_true', help='Enable pagination to get all data')
    
    args = parser.parse_args()
    
    # Initialize components
    api_tool = VendorAPITool()
    extractor = HTMLTableExtractor()
    excel_generator = ExcelReportGenerator()
    
    # Prepare search parameters
    search_params = {}
    if args.factory_id:
        search_params['factory_id'] = args.factory_id
    if args.location:
        search_params['location'] = args.location
    if args.industry_category:
        search_params['industry_category'] = args.industry_category
    if args.product:
        search_params['product'] = args.product
    if args.used_for:
        search_params['used_for'] = args.used_for
    if args.vendor:
        search_params['vendor'] = args.vendor
    
    # Load configuration from file if provided
    if args.config:
        try:
            with open(args.config, 'r') as f:
                config = json.load(f)
                search_params.update(config.get('search_params', {}))
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
    
    # Make API request
    logger.info("Starting vendor data extraction...")
    
    if search_params:
        html_content = api_tool.make_search_request(search_params)
    else:
        html_content = api_tool.get_paginated_data()
    
    if not html_content:
        logger.error("Failed to get data from API")
        return 1
    
    # Extract table data
    table_data = extractor.extract_main_table(html_content)
    
    if not table_data:
        logger.error("No table data extracted")
        return 1
    
    # Generate Excel report
    title = f"Vendor Registration Report - {datetime.now().strftime('%Y-%m-%d')}"
    success = excel_generator.create_report(table_data, args.output, title, search_params)
    
    if success:
        logger.info(f"Report generated successfully: {args.output}")
        return 0
    else:
        logger.error("Failed to generate report")
        return 1

if __name__ == "__main__":
    sys.exit(main())
