# Environment Configuration for GeM Bid Scraper
# Copy this file to .env and update the values as needed

# Database Configuration
DATABASE_URL=sqlite:///./gem_bids.db
# For PostgreSQL: DATABASE_URL=postgresql://username:password@localhost/gem_bids
# For MySQL: DATABASE_URL=mysql://username:password@localhost/gem_bids

# GeM API Configuration
GEM_API_ENDPOINT_URL=https://bidplus.gem.gov.in/all-bids
GEM_INITIAL_PAGE_URL=https://bidplus.gem.gov.in/all-bids
CSRF_FORM_FIELD_NAME=csrf_bd_gem_nk
CSRF_COOKIE_NAME=csrf_bd_gem_nk
PAYLOAD_FORM_FIELD_NAME=payload

# Pagination Configuration
ITEMS_PER_PAGE=10

# File Storage Configuration
DOWNLOADS_BASE_DIR=gem_downloaded_bids
TEMP_ZIP_DIR=temp_zip_archives
PDF_PROCESSING_BASE_DIR=processed_bid_pdfs
PDF_UPLOAD_TEMP_DIR=temp_pdf_uploads

# Request Headers (JSON format)
REQUEST_HEADERS={"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1"}

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/gem_scraper.log

# Performance Configuration
MAX_CONCURRENT_DOWNLOADS=5
SCRAPER_DELAY_SECONDS=0.6
MAX_RETRIES=3
RETRY_DELAY=2

# PDF Processing Configuration
MAX_PDF_SIZE_MB=50
MIN_TEXT_LENGTH=100
MAX_PROCESSING_TIME=300

# AI/ML Configuration for PDF Analysis
GEMINI_API_KEY=your_gemini_api_key_here
# Get your API key from: https://makersuite.google.com/app/apikey

# Security Configuration
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Development Configuration
DEBUG=True
RELOAD=True
