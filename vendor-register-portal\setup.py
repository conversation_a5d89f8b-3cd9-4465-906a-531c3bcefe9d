#!/usr/bin/env python3
"""
Setup script for Vendor API Tool
================================

This script helps set up the environment and verify all dependencies.
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def verify_files():
    """Verify all required files are present."""
    print("\n📁 Verifying files...")
    required_files = [
        "vendor_api_tool.py",
        "interactive_ui.py",
        "requirements.txt",
        "config_template.json",
        "README.md"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - Missing")
            missing_files.append(file)
    
    return len(missing_files) == 0

def test_imports():
    """Test if all required modules can be imported."""
    print("\n🧪 Testing imports...")
    modules = [
        ("requests", "HTTP requests"),
        ("bs4", "HTML parsing"),
        ("openpyxl", "Excel generation"),
        ("json", "JSON handling"),
        ("logging", "Logging")
    ]
    
    failed_imports = []
    for module, description in modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} - Failed to import")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def create_sample_config():
    """Create a sample configuration file if it doesn't exist."""
    config_file = "my_config.json"
    if not os.path.exists(config_file):
        print(f"\n📝 Creating sample configuration: {config_file}")
        sample_config = {
            "search_params": {
                "location": "Delhi",
                "industry_category": "L",
                "product": "Cylinder"
            },
            "output_settings": {
                "filename": "delhi_vendors.xlsx",
                "title": "Delhi Vendor Report"
            }
        }
        
        import json
        with open(config_file, 'w') as f:
            json.dump(sample_config, f, indent=4)
        print(f"✅ Sample configuration created: {config_file}")

def main():
    """Main setup function."""
    print("🚀 Vendor API Tool Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Verify files
    if not verify_files():
        print("\n❌ Some required files are missing. Please ensure all files are present.")
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Test imports
    if not test_imports():
        print("\n❌ Some modules failed to import. Please check the installation.")
        return 1
    
    # Create sample config
    create_sample_config()
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run the interactive UI: python interactive_ui.py")
    print("2. Or use command line: python vendor_api_tool.py --help")
    print("3. Or run the batch file: run_vendor_tool.bat (Windows)")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
