import os
import re
import glob
import json
import argparse
import concurrent.futures
from parsing.bid_document_extractor import extract_text_with_pymupdf, extract_tables_with_pymupdf
from parsing.bid_document_extractor import extract_emd_details, extract_bid_terms_and_conditions

def process_bid_document(pdf_path, output_dir=None, save_full_results=False):
    """Process a bid document PDF to extract EMD details and bid terms."""
    try:
        # Extract text and tables
        text_result = extract_text_with_pymupdf(pdf_path)
        tables = extract_tables_with_pymupdf(pdf_path)

        if not text_result["plain_text"]:
            return {
                "success": False,
                "pdf_path": pdf_path,
                "error": "No text extracted"
            }

        # Extract EMD details
        emd_details = extract_emd_details(text_result["plain_text"], tables)

        # Extract bid terms and conditions
        bid_terms = extract_bid_terms_and_conditions(text_result["plain_text"])

        # Determine if this document has the information we're looking for
        has_emd_details = bool(emd_details)
        has_bid_terms = "section_title" in bid_terms

        # Save results if output directory is specified and document has relevant information
        if output_dir and (save_full_results or has_emd_details or has_bid_terms):
            os.makedirs(output_dir, exist_ok=True)

            pdf_name = os.path.basename(pdf_path)
            base_name = os.path.splitext(pdf_name)[0]

            # Create a subdirectory for this PDF
            pdf_dir = os.path.join(output_dir, base_name)
            os.makedirs(pdf_dir, exist_ok=True)

            # Save extracted text if full results requested
            if save_full_results:
                with open(os.path.join(pdf_dir, f"{base_name}_text.txt"), "w", encoding="utf-8") as f:
                    f.write(text_result["plain_text"])

            # Save EMD details
            if has_emd_details:
                with open(os.path.join(pdf_dir, f"{base_name}_emd_details.json"), "w", encoding="utf-8") as f:
                    json.dump(emd_details, f, indent=2)

                # Save EMD details in human-readable format
                with open(os.path.join(pdf_dir, f"{base_name}_emd_details.txt"), "w", encoding="utf-8") as f:
                    f.write("EMD (Earnest Money Deposit) Details:\n\n")

                    if "EMD_Amount" in emd_details:
                        f.write(f"Amount: {emd_details['EMD_Amount']}\n")

                    if "EMD_Percentage" in emd_details:
                        f.write(f"Percentage: {emd_details['EMD_Percentage']}\n")

                    if "EMD_Exemption" in emd_details:
                        f.write(f"Exemption: {emd_details['EMD_Exemption']}\n")
                        if "EMD_Exemption_Details" in emd_details:
                            f.write(f"Exemption Details: {emd_details['EMD_Exemption_Details']}\n")

                    if "EMD_Table" in emd_details:
                        f.write("\nEMD Table:\n")
                        for row in emd_details["EMD_Table"]:
                            f.write("| " + " | ".join(row) + " |\n")

            # Save bid terms
            if has_bid_terms:
                with open(os.path.join(pdf_dir, f"{base_name}_bid_terms.json"), "w", encoding="utf-8") as f:
                    json.dump(bid_terms, f, indent=2)

                # Save bid terms in human-readable format
                with open(os.path.join(pdf_dir, f"{base_name}_bid_terms.txt"), "w", encoding="utf-8") as f:
                    if "section_title" in bid_terms:
                        f.write(f"{bid_terms['section_title']}\n")
                        f.write("=" * len(bid_terms['section_title']) + "\n\n")

                        if "terms_list" in bid_terms and bid_terms["terms_list"]:
                            for i, term in enumerate(bid_terms["terms_list"]):
                                f.write(f"{i+1}. {term}\n\n")
                        elif "full_content" in bid_terms:
                            f.write(bid_terms["full_content"])
                    else:
                        f.write("No bid-specific terms and conditions found.")

            # Save all results
            with open(os.path.join(pdf_dir, f"{base_name}_results.json"), "w", encoding="utf-8") as f:
                json.dump({
                    "pdf_path": pdf_path,
                    "emd_details": emd_details,
                    "bid_terms_and_conditions": bid_terms
                }, f, indent=2)

        return {
            "success": True,
            "pdf_path": pdf_path,
            "has_emd_details": has_emd_details,
            "has_bid_terms": has_bid_terms,
            "emd_details": emd_details,
            "bid_terms_and_conditions": bid_terms
        }
    except Exception as e:
        return {
            "success": False,
            "pdf_path": pdf_path,
            "error": str(e)
        }

def process_pdf_worker(args):
    """Worker function for parallel processing."""
    pdf_path, output_dir, save_full_results = args
    return process_bid_document(pdf_path, output_dir, save_full_results)

def main():
    parser = argparse.ArgumentParser(description="Batch process bid document PDFs")
    parser.add_argument("--input_dir", help="Directory containing PDF files")
    parser.add_argument("--output_dir", default="bid_results", help="Directory to save results")
    parser.add_argument("--workers", type=int, default=1, help="Number of parallel workers")
    parser.add_argument("--save_full", action="store_true", help="Save full results for all PDFs")
    args = parser.parse_args()

    # Get list of PDFs to process
    if args.input_dir:
        pdf_files = glob.glob(os.path.join(args.input_dir, "**/*.pdf"), recursive=True)
    else:
        pdf_files = [r"../bids/NILAY 2/GEM-2025-B-6060979.pdf"]

    print(f"Found {len(pdf_files)} PDF files to process")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Process PDFs
    results = []

    if args.workers > 1 and len(pdf_files) > 1:
        # Parallel processing
        with concurrent.futures.ProcessPoolExecutor(max_workers=args.workers) as executor:
            worker_args = [(pdf_file, args.output_dir, args.save_full) for pdf_file in pdf_files]
            for i, result in enumerate(executor.map(process_pdf_worker, worker_args)):
                results.append(result)
                if result["success"]:
                    status = []
                    if result.get("has_emd_details", False):
                        status.append("EMD details")
                    if result.get("has_bid_terms", False):
                        status.append("Bid terms")

                    status_str = ", ".join(status) if status else "No relevant information"
                    print(f"[{i+1}/{len(pdf_files)}] Processed: {result['pdf_path']} - {status_str}")
                else:
                    print(f"[{i+1}/{len(pdf_files)}] Failed to process: {result['pdf_path']} - {result.get('error', 'Unknown error')}")
    else:
        # Sequential processing
        for i, pdf_file in enumerate(pdf_files):
            print(f"[{i+1}/{len(pdf_files)}] Processing: {pdf_file}")
            result = process_bid_document(pdf_file, args.output_dir, args.save_full)
            results.append(result)
            if result["success"]:
                status = []
                if result.get("has_emd_details", False):
                    status.append("EMD details")
                if result.get("has_bid_terms", False):
                    status.append("Bid terms")

                status_str = ", ".join(status) if status else "No relevant information"
                print(f"[{i+1}/{len(pdf_files)}] Processed: {pdf_file} - {status_str}")
            else:
                print(f"[{i+1}/{len(pdf_files)}] Failed to process: {pdf_file} - {result.get('error', 'Unknown error')}")

    # Save summary
    with open(os.path.join(args.output_dir, "summary.txt"), "w", encoding="utf-8") as f:
        f.write(f"Processed {len(results)} PDF files\n\n")

        # Count documents with EMD details
        emd_docs = [r for r in results if r["success"] and r.get("has_emd_details", False)]
        f.write(f"Documents with EMD details: {len(emd_docs)}\n")

        # Count documents with bid terms
        bid_terms_docs = [r for r in results if r["success"] and r.get("has_bid_terms", False)]
        f.write(f"Documents with bid-specific terms: {len(bid_terms_docs)}\n\n")

        # List documents with EMD details
        if emd_docs:
            f.write("Documents with EMD details:\n")
            for result in emd_docs:
                f.write(f"- {result['pdf_path']}\n")

                if "emd_details" in result and "EMD_Amount" in result["emd_details"]:
                    f.write(f"  Amount: {result['emd_details']['EMD_Amount']}\n")

                if "emd_details" in result and "EMD_Percentage" in result["emd_details"]:
                    f.write(f"  Percentage: {result['emd_details']['EMD_Percentage']}\n")

                f.write("\n")

        # List documents with bid terms
        if bid_terms_docs:
            f.write("\nDocuments with bid-specific terms:\n")
            for result in bid_terms_docs:
                f.write(f"- {result['pdf_path']}\n")

                if "bid_terms_and_conditions" in result and "section_title" in result["bid_terms_and_conditions"]:
                    f.write(f"  Section: {result['bid_terms_and_conditions']['section_title']}\n")

                if "bid_terms_and_conditions" in result and "terms_list" in result["bid_terms_and_conditions"]:
                    f.write(f"  Terms count: {len(result['bid_terms_and_conditions']['terms_list'])}\n")

                f.write("\n")

    # Save summary as JSON
    with open(os.path.join(args.output_dir, "summary.json"), "w", encoding="utf-8") as f:
        json.dump({
            "total_processed": len(results),
            "successful": sum(1 for r in results if r["success"]),
            "with_emd_details": len(emd_docs),
            "with_bid_terms": len(bid_terms_docs),
            "results": results
        }, f, indent=2)

    print(f"\nResults saved to {args.output_dir}")
    print(f"Summary saved to {os.path.join(args.output_dir, 'summary.txt')}")
    print(f"Found {len(emd_docs)} documents with EMD details")
    print(f"Found {len(bid_terms_docs)} documents with bid-specific terms")

if __name__ == "__main__":
    main()
