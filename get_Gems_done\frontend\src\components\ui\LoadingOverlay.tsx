// src/components/ui/LoadingOverlay.tsx
"use client";

import React from "react";
import LoadingBar from "./LoadingBar";

interface LoadingOverlayProps {
	/** Whether the overlay is visible */
	isVisible: boolean;
	/** Loading message to display */
	message?: string;
	/** Progress percentage (0-100) for progress bar */
	progress?: number;
	/** Whether to show a progress bar */
	showProgress?: boolean;
	/** Custom className for styling */
	className?: string;
	/** Whether to show spinner instead of progress bar */
	showSpinner?: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
	isVisible,
	message = "Loading...",
	progress,
	showProgress = false,
	className = "",
	showSpinner = true,
}) => {
	if (!isVisible) return null;

	return (
		<div className={`loading-overlay ${className}`}>
			<div className="bg-white rounded-lg p-6 shadow-lg max-w-sm w-full mx-4">
				<div className="text-center">
					{showSpinner && !showProgress && (
						<div className="loading-spinner mx-auto mb-4" />
					)}
					<p className="text-gray-700 mb-4">{message}</p>
					{showProgress && (
						<LoadingBar
							progress={progress}
							height={6}
							showPercentage={progress !== undefined}
							className="mt-4"
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default LoadingOverlay;
