# app/services/pdf_processor_service.py
import fitz # PyMuPDF
import os
import json
import base64
import asyncio
import re
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass
import google.generativeai as genai
from google.generativeai.caching import CachedContent
import time

from app.core.config import settings
from app.config.criteria_config import (
    BidCriteria,
    DEFAULT_CRITERIA,
    get_all_restriction_patterns,
    get_all_acceptable_patterns,
    is_restrictive_clause
)

# Configure Gemini API
genai.configure(api_key=settings.GEMINI_API_KEY)

@dataclass
class BidAnalysisResult:
    """Structured result from bid analysis"""
    status: str  # "ACCEPT" or "REJECT"
    reasons: List[str]
    emd_analysis: Dict[str, Any]
    buyer_terms_analysis: Dict[str, Any]
    confidence_score: float

# BidCriteria and DEFAULT_CRITERIA are now imported from app.config.criteria_config

# --- Helper Functions ---

def extract_pdf_content(pdf_path: str) -> Tuple[str, str]:
    """
    Extract text content and convert PDF to base64 for Gemini processing.
    Returns: (text_content, base64_pdf)
    """
    text_content = ""
    base64_pdf = ""

    try:
        # Extract text using PyMuPDF
        doc = fitz.open(pdf_path)
        for page in doc:
            text_content += page.get_text()
        doc.close()

        # Convert PDF to base64 for Gemini
        with open(pdf_path, "rb") as pdf_file:
            base64_pdf = base64.b64encode(pdf_file.read()).decode('utf-8')

    except Exception as e:
        print(f"Error processing PDF {pdf_path}: {e}")
        return "", ""

    return text_content, base64_pdf

def create_analysis_prompt(criteria: BidCriteria) -> str:
    """Create a comprehensive prompt for bid analysis with refined criteria"""

    prompt = f"""
You are an expert bid document analyzer for government procurement. Analyze this bid document and determine if it should be ACCEPTED or REJECTED based on the following REFINED criteria:

## ANALYSIS CRITERIA:

### 1. EMD (Earnest Money Deposit) Analysis:
- Check if EMD is required: {criteria.emd_required}
- Zero EMD acceptable: {criteria.emd_zero_acceptable}

**REJECT if EMD has these issues:**
- EMD amount is zero when required
- Section-wise EMD requirements (different EMD for different sections)
- "EMD exemption not allowed" clauses
- "EMD required for selected sections only"

**ACCEPT if:**
- EMD amount is specified (any non-zero amount)
- Standard EMD exemption clauses for MSMEs/startups
- Normal EMD percentage (1-3% of bid value)

### 2. Buyer Added Terms & Conditions Analysis:

**CRITICAL RESTRICTIVE CLAUSES - REJECT ONLY if these EXACT patterns found:**

**A. Organization-Specific Registration Requirements (VERY SPECIFIC):**
- "Seller/Bidder should be established & Registered Vendors for MPF"
- "Only established/registered vendors for MPF/OFB/AVNL/HVF"
- "Must be registered vendor with [specific organization]"
- "Sister Factories of AVNL/OFB only"
- "Registered vendors for the same range of Products/Technology in [organization]"

**B. Mandatory In-House Facility Requirements (VERY SPECIFIC):**
- "In house Facility for Forging is mandatory"
- "In house casting facility is mandatory/required/essential"
- "In house heat treatment facility is mandatory/required/essential"
- "Must have mandatory in-house [specific process] facility"

**C. Overly Specific Experience Requirements (VERY SPECIFIC):**
- "Minimum [X] years experience in manufacturing [exact same product] is mandatory"
- "Experience in supplying to defence/PSU is mandatory/essential"
- "Previous supply to [specific organization] is mandatory"
- "Export experience is mandatory/essential"

**D. Restrictive Certification Requirements (BEYOND STANDARD):**
- Only reject if certification is made "mandatory" for participation, not just mentioned

**ACCEPTABLE STANDARD TERMS - DO NOT REJECT:**
- Document submission requirements (PAN, GST, certificates, etc.)
- Basic manufacturer requirements (not trader/stockist)
- Standard quality certifications (ISO, NABL, DGQA as general requirements)
- Technical specifications and compliance requirements
- Delivery, warranty, and service terms
- Payment and commercial terms
- Standard government procurement clauses
- MSME/Startup/Women entrepreneur preferences
- Make in India requirements
- Environmental and safety compliance
- Any terms that don't restrict participation to specific vendors

### 3. Context-Aware Analysis:

**IMPORTANT: Distinguish between:**
- **RESTRICTIVE**: "Only established vendors for MPF" → REJECT
- **INFORMATIONAL**: "This tender is for MPF requirements" → ACCEPT
- **RESTRICTIVE**: "In-house forging mandatory" → REJECT
- **STANDARD**: "Submit manufacturing capability details" → ACCEPT

### 4. Technical Specifications:
- Check for overly specific brand/   requirements
- Flag requirements that unnecessarily limit competition
- Accept standard technical specifications and quality requirements

## OUTPUT FORMAT:
Provide your analysis in the following JSON format:

{{
    "status": "ACCEPT" or "REJECT",
    "confidence_score": 0.0-1.0,
    "emd_analysis": {{
        "emd_present": true/false,
        "emd_amount": "amount or 'not specified'",
        "emd_issues": ["list of issues found"],
        "emd_acceptable": true/false
    }},
    "buyer_terms_analysis": {{
        "restrictive_clauses_found": ["exact quotes of restrictive clauses"],
        "contractor_restrictions": ["organization-specific restrictions"],
        "facility_requirements": ["mandatory in-house facility requirements"],
        "certification_issues": ["overly restrictive certification requirements"],
        "experience_issues": ["overly specific experience requirements"],
        "standard_terms_found": ["acceptable standard terms identified"]
    }},
    "technical_analysis": {{
        "drawing_details_present": true/false,
        "overly_specific_requirements": ["list of overly specific requirements"],
        "brand_restrictions": ["any brand/model restrictions found"]
    }},
    "reasons": ["detailed list of reasons for acceptance/rejection with exact quotes"],
    "key_findings": ["most important findings from the document"]
}}

## CRITICAL DECISION RULES (VERY IMPORTANT):
1. **DEFAULT TO ACCEPT**: Unless you find VERY SPECIFIC restrictive clauses, ACCEPT the bid
2. **REJECT ONLY** if you find EXACT phrases like:
   - "established & Registered Vendors for [specific organization]"
   - "In house [facility] is mandatory"
   - "Only registered vendors with [specific organization]"
3. **IGNORE** general mentions of organizations, certifications, or requirements
4. **CONTEXT IS CRITICAL**: Same words can be restrictive or standard
5. **WHEN IN DOUBT, ACCEPT**: Err on the side of acceptance

## CLEAR EXAMPLES:
**REJECT (Very Specific Restrictive Language):**
- "Seller/Bidder should be established & Registered Vendors for MPF"
- "In house Facility for Forging is mandatory"
- "Only registered vendors with OFB are eligible"

**ACCEPT (Standard Language - Even if mentions organizations):**
- "Bidders are advised to check applicable GST"
- "Submit ISO certification if available"
- "NABL accredited labs preferred"
- "DGQA approval may be required"
- "OFB specifications to be followed"
- "MPF standards applicable"
- "Quality requirements as per industry standards"
- "Submit manufacturing capability details"
- "Vendor registration for future orders"

## MOST IMPORTANT RULE:
**IF YOU'RE NOT 100% SURE IT'S RESTRICTIVE, CHOOSE ACCEPT**

Analyze the provided bid document now:
"""
    return prompt

def analyze_with_rules(pdf_path: str, criteria: BidCriteria = DEFAULT_CRITERIA) -> BidAnalysisResult:
    """
    Fallback rule-based analysis when Gemini API is unavailable.
    Uses patterns from your existing PDF processor analysis.
    """
    try:
        # Extract PDF content
        text_content, _ = extract_pdf_content(pdf_path)

        if not text_content:
            return BidAnalysisResult(
                status="REJECT",
                reasons=["Failed to extract content from PDF"],
                emd_analysis={},
                buyer_terms_analysis={},
                confidence_score=0.0
            )

        text_lower = text_content.lower()
        reasons = []
        restrictive_clauses = []

        # Use centralized patterns from criteria_config
        from app.config.criteria_config import get_all_restriction_patterns

        # Check for restrictive patterns first (prioritize restrictions)
        restrictive_patterns = get_all_restriction_patterns()

        # Check for restrictive patterns
        for pattern in restrictive_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                # Get context around the match
                start = max(0, match.start() - 50)
                end = min(len(text_content), match.end() + 50)
                context = text_content[start:end].strip()

                restrictive_clauses.append(context)
                reasons.append(f"Restrictive clause found: {context}")

        # EMD Analysis (basic)
        emd_analysis = {
            "emd_present": bool(re.search(r'emd|earnest\s+money', text_lower)),
            "emd_amount": "not analyzed in fallback mode",
            "emd_issues": [],
            "emd_acceptable": True  # Default to acceptable in fallback
        }

        # Buyer terms analysis
        buyer_terms_analysis = {
            "restrictive_clauses_found": restrictive_clauses,
            "contractor_restrictions": [clause for clause in restrictive_clauses if any(org in clause.lower() for org in ['mpf', 'ofb', 'avnl'])],
            "facility_requirements": [clause for clause in restrictive_clauses if 'forging' in clause.lower() or 'casting' in clause.lower()],
            "certification_issues": [],
            "experience_issues": [],
            "standard_terms_found": []
        }

        # Decision logic
        if restrictive_clauses:
            status = "REJECT"
            confidence = 0.8  # High confidence for rule-based rejection
        else:
            status = "ACCEPT"
            confidence = 0.7  # Moderate confidence for rule-based acceptance
            reasons = ["No restrictive clauses found in rule-based analysis"]

        return BidAnalysisResult(
            status=status,
            reasons=reasons,
            emd_analysis=emd_analysis,
            buyer_terms_analysis=buyer_terms_analysis,
            confidence_score=confidence
        )

    except Exception as e:
        return BidAnalysisResult(
            status="REJECT",
            reasons=[f"Rule-based analysis failed: {str(e)}"],
            emd_analysis={},
            buyer_terms_analysis={},
            confidence_score=0.0
        )

async def analyze_with_gemini(pdf_path: str, criteria: BidCriteria = DEFAULT_CRITERIA) -> BidAnalysisResult:
    """
    Analyze PDF using Gemini AI with content caching for better accuracy.
    """
    try:
        # Extract PDF content
        text_content, base64_pdf = extract_pdf_content(pdf_path)

        if not text_content and not base64_pdf:
            return BidAnalysisResult(
                status="REJECT",
                reasons=["Failed to extract content from PDF"],
                emd_analysis={},
                buyer_terms_analysis={},
                confidence_score=0.0
            )

        # Create the analysis prompt
        prompt = create_analysis_prompt(criteria)

        # Initialize Gemini model
        model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

        # Prepare content for analysis
        if base64_pdf:
            # Use PDF directly if available
            pdf_part = {
                "mime_type": "application/pdf",
                "data": base64_pdf
            }
            content = [prompt, pdf_part]
        else:
            # Fallback to text content
            content = [prompt, f"\n\nDocument Text Content:\n{text_content}"]

        # Generate analysis
        response = model.generate_content(content)

        # Parse the JSON response
        try:
            # Extract JSON from response
            response_text = response.text
            # Find JSON content (handle potential markdown formatting)
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            elif "{" in response_text:
                json_start = response_text.find("{")
                json_end = response_text.rfind("}") + 1
                json_text = response_text[json_start:json_end]
            else:
                raise ValueError("No JSON found in response")

            analysis_data = json.loads(json_text)

            # Create structured result
            result = BidAnalysisResult(
                status=analysis_data.get("status", "REJECT"),
                reasons=analysis_data.get("reasons", []),
                emd_analysis=analysis_data.get("emd_analysis", {}),
                buyer_terms_analysis=analysis_data.get("buyer_terms_analysis", {}),
                confidence_score=analysis_data.get("confidence_score", 0.5)
            )

            return result

        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error parsing Gemini response: {e}")
            print(f"Raw response: {response.text}")

            # Fallback analysis based on response text
            response_lower = response.text.lower()
            if "reject" in response_lower:
                status = "REJECT"
                reasons = ["Document analysis suggests rejection based on restrictive clauses"]
            else:
                status = "ACCEPT"
                reasons = ["Document appears acceptable based on analysis"]

            return BidAnalysisResult(
                status=status,
                reasons=reasons,
                emd_analysis={"parsing_error": True},
                buyer_terms_analysis={"parsing_error": True},
                confidence_score=0.3
            )

    except Exception as e:
        print(f"Error in Gemini analysis: {e}")

        # Check if it's a quota/rate limit error
        error_str = str(e)
        if "429" in error_str or "quota" in error_str.lower() or "rate limit" in error_str.lower():
            print("Quota/rate limit detected, falling back to rule-based analysis...")
            return analyze_with_rules(pdf_path, criteria)

        return BidAnalysisResult(
            status="REJECT",
            reasons=[f"Analysis failed: {str(e)}"],
            emd_analysis={},
            buyer_terms_analysis={},
            confidence_score=0.0
        )


def check_bid_pdf(pdf_path: str, criteria: BidCriteria = DEFAULT_CRITERIA) -> Tuple[str, List[str]]:
    """
    Main function to check bid PDF using Gemini AI analysis.
    This replaces the old regex-based approach with AI-powered analysis.

    Args:
        pdf_path: Path to the PDF file to analyze
        criteria: BidCriteria object with analysis parameters

    Returns:
        Tuple of (status, reasons) where status is "ACCEPT" or "REJECT"
    """
    try:
        # Run async analysis in sync context
        import asyncio

        # Check if we're already in an async context
        try:
            loop = asyncio.get_running_loop()
            # We're in an async context, need to use a different approach
            # For now, we'll create a new event loop in a thread
            import concurrent.futures
            import threading

            def run_analysis():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(analyze_with_gemini(pdf_path, criteria))
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_analysis)
                result = future.result(timeout=60)  # 60 second timeout

        except RuntimeError:
            # No event loop running, we can use asyncio.run
            result = asyncio.run(analyze_with_gemini(pdf_path, criteria))

        return result.status, result.reasons

    except Exception as e:
        print(f"Error in PDF analysis: {e}")
        return "REJECT", [f"Analysis failed: {str(e)}"]

# Backward compatibility function for existing code
def check_bid_pdf_legacy(pdf_path: str) -> Tuple[str, List[str]]:
    """
    Legacy function for backward compatibility.
    Uses default criteria and maintains the same interface as the old function.
    """
    return check_bid_pdf(pdf_path, DEFAULT_CRITERIA)

# Advanced Batch Processing with Optimized Caching
@dataclass
class BatchProcessingConfig:
    """Configuration for batch PDF processing"""
    batch_size: int = 10  # PDFs per batch
    max_concurrent_batches: int = 3  # Parallel batches
    cache_ttl_minutes: int = 60  # Cache time-to-live
    max_retries: int = 2  # Retry failed analyses
    timeout_seconds: int = 120  # Per-PDF timeout

@dataclass
class BatchProcessingResult:
    """Result from batch processing"""
    total_processed: int
    successful: int
    failed: int
    results: List[BidAnalysisResult]
    processing_time: float
    cost_estimate: float
    batch_details: List[Dict[str, Any]]

class GeminiContentCache:
    """Manages Gemini content caching for batch processing"""

    def __init__(self):
        self.cache_store = {}
        self.cache_timestamps = {}

    async def create_cached_content(self, criteria: BidCriteria) -> Optional[CachedContent]:
        """Create cached content for the given criteria"""
        try:
            cache_key = self._generate_cache_key(criteria)

            # Check if cache exists and is still valid
            if self._is_cache_valid(cache_key):
                print(f"Using existing cache for criteria: {cache_key}")
                return self.cache_store[cache_key]

            # Create new cached content
            prompt = create_analysis_prompt(criteria)

            # Create cached content with Gemini
            cached_content = CachedContent.create(
                model='gemini-2.5-pro-preview-05-06',
                system_instruction=prompt,
                ttl=3600,  # 1 hour TTL
                display_name=f"bid_analysis_cache_{cache_key}"
            )

            # Store in local cache
            self.cache_store[cache_key] = cached_content
            self.cache_timestamps[cache_key] = time.time()

            print(f"Created new cache for criteria: {cache_key}")
            return cached_content

        except Exception as e:
            print(f"Error creating cached content: {e}")
            return None

    def _generate_cache_key(self, criteria: BidCriteria) -> str:
        """Generate a unique cache key for criteria"""
        import hashlib
        criteria_str = f"{criteria.emd_required}_{criteria.emd_zero_acceptable}_{criteria.check_contractor_restrictions}_{criteria.check_facility_requirements}_{criteria.check_certification_requirements}_{criteria.check_experience_requirements}_{sorted(criteria.restricted_organizations)}"
        return hashlib.md5(criteria_str.encode()).hexdigest()[:8]

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache is still valid"""
        if cache_key not in self.cache_store:
            return False

        cache_age = time.time() - self.cache_timestamps.get(cache_key, 0)
        return cache_age < 3600  # 1 hour validity

    async def cleanup_expired_caches(self):
        """Clean up expired caches"""
        current_time = time.time()
        expired_keys = []

        for cache_key, timestamp in self.cache_timestamps.items():
            if current_time - timestamp > 3600:  # 1 hour
                expired_keys.append(cache_key)

        for key in expired_keys:
            try:
                if key in self.cache_store:
                    # Delete from Gemini
                    self.cache_store[key].delete()
                    del self.cache_store[key]
                    del self.cache_timestamps[key]
                    print(f"Cleaned up expired cache: {key}")
            except Exception as e:
                print(f"Error cleaning up cache {key}: {e}")

# Global cache instance
_content_cache = GeminiContentCache()

async def analyze_with_cached_gemini(pdf_path: str, cached_content: CachedContent, criteria: BidCriteria) -> BidAnalysisResult:
    """Analyze PDF using cached Gemini content for efficiency"""
    try:
        # Extract PDF content
        text_content, base64_pdf = extract_pdf_content(pdf_path)

        if not text_content and not base64_pdf:
            return BidAnalysisResult(
                status="REJECT",
                reasons=["Failed to extract content from PDF"],
                emd_analysis={},
                buyer_terms_analysis={},
                confidence_score=0.0
            )

        # Initialize model with cached content
        model = genai.GenerativeModel(
            model_name='gemini-2.5-pro-preview-05-06',
            cached_content=cached_content
        )

        # Prepare content for analysis
        if base64_pdf:
            pdf_part = {
                "mime_type": "application/pdf",
                "data": base64_pdf
            }
            content = ["Analyze this bid document:", pdf_part]
        else:
            content = [f"Analyze this bid document text:\n\n{text_content}"]

        # Generate analysis
        response = model.generate_content(content)

        # Parse response (same as before)
        try:
            response_text = response.text
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            elif "{" in response_text:
                json_start = response_text.find("{")
                json_end = response_text.rfind("}") + 1
                json_text = response_text[json_start:json_end]
            else:
                raise ValueError("No JSON found in response")

            analysis_data = json.loads(json_text)

            return BidAnalysisResult(
                status=analysis_data.get("status", "REJECT"),
                reasons=analysis_data.get("reasons", []),
                emd_analysis=analysis_data.get("emd_analysis", {}),
                buyer_terms_analysis=analysis_data.get("buyer_terms_analysis", {}),
                confidence_score=analysis_data.get("confidence_score", 0.5)
            )

        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error parsing cached Gemini response: {e}")
            response_lower = response.text.lower()
            status = "REJECT" if "reject" in response_lower else "ACCEPT"
            return BidAnalysisResult(
                status=status,
                reasons=["Document analysis completed with parsing issues"],
                emd_analysis={"parsing_error": True},
                buyer_terms_analysis={"parsing_error": True},
                confidence_score=0.3
            )

    except Exception as e:
        print(f"Error in cached Gemini analysis: {e}")
        return BidAnalysisResult(
            status="REJECT",
            reasons=[f"Cached analysis failed: {str(e)}"],
            emd_analysis={},
            buyer_terms_analysis={},
            confidence_score=0.0
        )

async def process_pdf_batch(
    pdf_paths: List[str],
    criteria: BidCriteria,
    cached_content: CachedContent,
    batch_id: int,
    progress_callback: Optional[callable] = None
) -> Dict[str, Any]:
    """Process a single batch of PDFs"""
    batch_start_time = time.time()
    batch_results = []
    successful = 0
    failed = 0

    print(f"Processing batch {batch_id} with {len(pdf_paths)} PDFs")

    for i, pdf_path in enumerate(pdf_paths):
        try:
            if progress_callback:
                progress_callback(batch_id, i + 1, len(pdf_paths), pdf_path)

            result = await analyze_with_cached_gemini(pdf_path, cached_content, criteria)
            batch_results.append(result)

            if result.status == "ACCEPT":
                successful += 1
            else:
                failed += 1

        except Exception as e:
            print(f"Error processing {pdf_path} in batch {batch_id}: {e}")
            batch_results.append(BidAnalysisResult(
                status="REJECT",
                reasons=[f"Processing error: {str(e)}"],
                emd_analysis={},
                buyer_terms_analysis={},
                confidence_score=0.0
            ))
            failed += 1

    batch_time = time.time() - batch_start_time

    return {
        "batch_id": batch_id,
        "results": batch_results,
        "successful": successful,
        "failed": failed,
        "processing_time": batch_time,
        "pdfs_processed": len(pdf_paths)
    }

async def analyze_batch_optimized(
    pdf_paths: List[str],
    criteria: BidCriteria = DEFAULT_CRITERIA,
    config: BatchProcessingConfig = None,
    progress_callback: Optional[callable] = None,
    task_id: Optional[str] = None
) -> BatchProcessingResult:
    """
    Optimized batch processing for large numbers of PDFs with intelligent caching.

    Args:
        pdf_paths: List of PDF file paths to analyze
        criteria: Analysis criteria to use
        config: Batch processing configuration
        progress_callback: Function to call for progress updates
        task_id: Optional task ID for tracking

    Returns:
        BatchProcessingResult with detailed analysis results
    """
    if config is None:
        config = BatchProcessingConfig()

    start_time = time.time()
    total_pdfs = len(pdf_paths)

    print(f"Starting optimized batch analysis of {total_pdfs} PDFs")
    print(f"Batch size: {config.batch_size}, Max concurrent: {config.max_concurrent_batches}")

    # Create cached content for this criteria
    cached_content = await _content_cache.create_cached_content(criteria)
    if not cached_content:
        print("Warning: Failed to create cached content, falling back to individual analysis")
        # Fallback to non-cached processing
        results = []
        for i, pdf_path in enumerate(pdf_paths):
            if progress_callback:
                progress_callback(0, i + 1, total_pdfs, pdf_path)
            result = await analyze_with_gemini(pdf_path, criteria)
            results.append(result)

        successful = sum(1 for r in results if r.status == "ACCEPT")
        failed = total_pdfs - successful
        processing_time = time.time() - start_time

        return BatchProcessingResult(
            total_processed=total_pdfs,
            successful=successful,
            failed=failed,
            results=results,
            processing_time=processing_time,
            cost_estimate=total_pdfs * 0.005,  # Rough estimate
            batch_details=[]
        )

    # Split PDFs into batches
    batches = []
    for i in range(0, total_pdfs, config.batch_size):
        batch_pdfs = pdf_paths[i:i + config.batch_size]
        batches.append(batch_pdfs)

    print(f"Created {len(batches)} batches for processing")

    # Process batches with concurrency control
    all_results = []
    batch_details = []
    total_successful = 0
    total_failed = 0

    # Process batches in groups to respect concurrency limits
    for batch_group_start in range(0, len(batches), config.max_concurrent_batches):
        batch_group = batches[batch_group_start:batch_group_start + config.max_concurrent_batches]

        # Create tasks for concurrent batch processing
        batch_tasks = []
        for batch_idx, batch_pdfs in enumerate(batch_group):
            actual_batch_id = batch_group_start + batch_idx
            task = process_pdf_batch(
                batch_pdfs,
                criteria,
                cached_content,
                actual_batch_id,
                progress_callback
            )
            batch_tasks.append(task)

        # Wait for all batches in this group to complete
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

        # Process batch results
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                print(f"Batch processing error: {batch_result}")
                continue

            all_results.extend(batch_result["results"])
            batch_details.append(batch_result)
            total_successful += batch_result["successful"]
            total_failed += batch_result["failed"]

            print(f"Batch {batch_result['batch_id']} completed: {batch_result['successful']} accepted, {batch_result['failed']} rejected")

    processing_time = time.time() - start_time

    # Estimate cost (rough calculation based on token usage)
    estimated_cost = total_pdfs * 0.003  # Reduced cost due to caching

    print(f"Batch processing completed in {processing_time:.2f} seconds")
    print(f"Total: {total_successful} accepted, {total_failed} rejected")
    print(f"Estimated cost: ${estimated_cost:.4f}")

    # Cleanup cache if needed
    await _content_cache.cleanup_expired_caches()

    return BatchProcessingResult(
        total_processed=total_pdfs,
        successful=total_successful,
        failed=total_failed,
        results=all_results,
        processing_time=processing_time,
        cost_estimate=estimated_cost,
        batch_details=batch_details
    )

# Convenience function for backward compatibility
async def analyze_batch_with_cache(pdf_paths: List[str], criteria: BidCriteria = DEFAULT_CRITERIA) -> List[BidAnalysisResult]:
    """
    Simple batch analysis function for backward compatibility.
    """
    result = await analyze_batch_optimized(pdf_paths, criteria)
    return result.results