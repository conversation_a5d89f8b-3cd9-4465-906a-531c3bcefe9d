class GEMBidHighlighterContent {
	constructor() {
		this.bidData = new Map();
		this.exportType = null;
		this.highlightedElements = new Set();
		this.statusPanel = null;
		this.autoHighlightEnabled = false;
		this.currentUrl = window.location.href;
		this.currentHash = window.location.hash;
		this.reHighlightTimeout = null;
		this.init();
	}

	init() {
		console.log("GEM Bid Highlighter v2.0: Content script loaded");
		this.setupMessageListener();
		this.loadStoredData();
		this.setupPageChangeDetection();
		this.setupAutoHighlight();
	}

	setupMessageListener() {
		chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
			switch (message.action) {
				case "highlightBids":
					this.bidData = new Map(message.bidData);
					this.exportType = message.exportType;
					this.highlightBidsOnPage();
					sendResponse({ success: true });
					break;
				case "clearHighlights":
					this.clearAllHighlights();
					sendResponse({ success: true });
					break;
				case "enableAutoHighlight":
					this.bidData = new Map(message.bidData || []);
					this.exportType = message.exportType;
					this.autoHighlightEnabled = true;
					if (this.bidData.size > 0) {
						this.highlightBidsOnPage();
					}
					sendResponse({ success: true });
					break;
			}
		});
	}

	async loadStoredData() {
		try {
			const result = await chrome.storage.local.get(["gemBidData"]);
			if (result.gemBidData && result.gemBidData.bidData) {
				this.bidData = new Map(result.gemBidData.bidData);
				this.exportType = result.gemBidData.exportType || "filtered";
				this.autoHighlightEnabled = true;
				console.log(
					"GEM Bid Highlighter: Loaded",
					this.bidData.size,
					"bids from storage"
				);
			}
		} catch (error) {
			console.error("GEM Bid Highlighter: Error loading data:", error);
		}
	}

	setupPageChangeDetection() {
		// Detect URL changes (for SPAs)
		const originalPushState = history.pushState;
		const originalReplaceState = history.replaceState;

		history.pushState = (...args) => {
			originalPushState.apply(history, args);
			setTimeout(() => this.onPageChange(), 100);
		};

		history.replaceState = (...args) => {
			originalReplaceState.apply(history, args);
			setTimeout(() => this.onPageChange(), 100);
		};

		// Detect back/forward navigation
		window.addEventListener("popstate", () => {
			setTimeout(() => this.onPageChange(), 100);
		});

		// Detect hash changes (for pagination like #page-1, #page-2)
		window.addEventListener("hashchange", (event) => {
			const oldHash = new URL(event.oldURL).hash;
			const newHash = new URL(event.newURL).hash;
			console.log(
				"GEM Bid Highlighter: Hash changed from",
				oldHash,
				"to",
				newHash
			);

			// Check if it's a page navigation
			if (newHash.match(/#page-\d+/) || oldHash.match(/#page-\d+/)) {
				console.log(
					"GEM Bid Highlighter: Detected page navigation, will re-highlight"
				);
				setTimeout(() => this.onPageChange(), 1000); // Longer delay for page content to load
			}
		});

		// Detect page content changes
		this.pageObserver = new MutationObserver((mutations) => {
			let shouldReHighlight = false;
			mutations.forEach((mutation) => {
				if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
					// Check if significant content was added
					for (let node of mutation.addedNodes) {
						if (node.nodeType === Node.ELEMENT_NODE) {
							// Look for table content or bid containers
							if (
								node.tagName === "TABLE" ||
								node.tagName === "TBODY" ||
								node.tagName === "TR" ||
								(node.tagName === "DIV" && node.children.length > 2) ||
								node.querySelector?.("table") ||
								node.querySelector?.("tr") ||
								this.containsBidNumber(node.textContent || "")
							) {
								console.log(
									"GEM Bid Highlighter: Detected bid content change, will re-highlight"
								);
								shouldReHighlight = true;
								break;
							}
						}
					}
				}
			});

			if (
				shouldReHighlight &&
				this.autoHighlightEnabled &&
				this.bidData.size > 0
			) {
				// Debounce rapid changes
				clearTimeout(this.reHighlightTimeout);
				this.reHighlightTimeout = setTimeout(() => {
					console.log(
						"GEM Bid Highlighter: Re-highlighting due to content change"
					);
					this.highlightBidsOnPage();
				}, 1200); // Longer delay for GEM website
			}
		});

		this.pageObserver.observe(document.body, {
			childList: true,
			subtree: true,
		});
	}

	setupAutoHighlight() {
		// Auto-highlight when page loads if enabled
		console.log(
			"GEM Bid Highlighter: Setup auto-highlight, enabled:",
			this.autoHighlightEnabled,
			"data size:",
			this.bidData.size
		);
		if (this.autoHighlightEnabled && this.bidData.size > 0) {
			console.log("GEM Bid Highlighter: Will auto-highlight in 1 second");
			setTimeout(() => this.highlightBidsOnPage(), 1000);
		}
	}

	onPageChange() {
		const newUrl = window.location.href;
		const newHash = window.location.hash;
		const urlChanged = newUrl !== this.currentUrl;
		const hashChanged = newHash !== this.currentHash;

		if (urlChanged || hashChanged) {
			console.log(
				"GEM Bid Highlighter: Page/Hash changed from",
				this.currentUrl + this.currentHash,
				"to",
				newUrl
			);

			// Check if it's a page navigation
			const isPageNavigation =
				newHash.match(/#page-\d+/) || this.currentHash.match(/#page-\d+/);

			this.currentUrl = newUrl;
			this.currentHash = newHash;
			this.clearAllHighlights();

			if (this.autoHighlightEnabled && this.bidData.size > 0) {
				// Longer delay for page navigation as content takes time to load
				const delay = isPageNavigation ? 2000 : 1000;
				console.log(`GEM Bid Highlighter: Will re-highlight in ${delay}ms`);
				setTimeout(() => this.highlightBidsOnPage(), delay);
			}
		}
	}

	highlightBidsOnPage() {
		console.log("GEM Bid Highlighter: Starting to highlight bids");
		this.clearAllHighlights();

		if (this.bidData.size === 0) {
			console.log("GEM Bid Highlighter: No bid data available");
			return;
		}

		const bidElements = this.findBidElements();
		const foundBids = new Set();
		let highlightedCount = 0;

		bidElements.forEach((element) => {
			const bidNumber = this.extractBidNumber(element);
			if (bidNumber) {
				if (this.bidData.has(bidNumber)) {
					// Bid is in our Excel data
					this.highlightElement(element, bidNumber, "in-excel");
					foundBids.add(bidNumber);
					highlightedCount++;
				} else if (this.exportType === "pdf_processed") {
					// For PDF processed exports, highlight bids not in Excel as yellow
					this.highlightElement(element, bidNumber, "not-in-excel");
					highlightedCount++;
				}
			}
		});

		console.log("GEM Bid Highlighter: Highlighted", highlightedCount, "bids");
		this.createStatusPanel(highlightedCount, foundBids.size);
	}

	findBidElements() {
		const elements = [];

		// First, try to find table rows that contain bid information
		const tableRows = document.querySelectorAll("tr");
		tableRows.forEach((row) => {
			const text = row.textContent || "";
			// Check if this row contains a bid number pattern
			if (this.containsBidNumber(text)) {
				elements.push(row);
			}
		});

		// If no table rows found, try other containers
		if (elements.length === 0) {
			const selectors = [
				'div[class*="bid"]',
				'div[class*="tender"]',
				'div[class*="result"]',
				'div[class*="item"]',
				".card",
				".listing-item",
				"[data-bid]",
				"[data-tender]",
			];

			selectors.forEach((selector) => {
				const found = document.querySelectorAll(selector);
				Array.from(found).forEach((element) => {
					const text = element.textContent || "";
					if (this.containsBidNumber(text)) {
						elements.push(element);
					}
				});
			});
		}

		console.log(
			"GEM Bid Highlighter: Found",
			elements.length,
			"potential bid elements"
		);
		return elements;
	}

	containsBidNumber(text) {
		// Check if text contains any bid number patterns
		const patterns = [
			/GEM\/\d{4}\/[A-Z]\/\d+/gi,
			/\b\d{10,}\b/g,
			/[A-Z]{2,}\d{6,}/g,
			/\d{4}\/[A-Z]+\/\d+/g,
		];

		for (const pattern of patterns) {
			if (pattern.test(text)) {
				return true;
			}
		}
		return false;
	}

	extractBidNumber(element) {
		const text = element.textContent || element.innerText || "";

		// Common GEM bid number patterns
		const patterns = [
			/GEM\/\d{4}\/[A-Z]\/\d+/gi,
			/\b\d{10,}\b/g, // Long numeric IDs
			/[A-Z]{2,}\d{6,}/g, // Alphanumeric patterns
			/\d{4}\/[A-Z]+\/\d+/g, // Year/letters/numbers
		];

		for (const pattern of patterns) {
			const matches = text.match(pattern);
			if (matches) {
				for (const match of matches) {
					// Check if this bid number exists in our data or if we should highlight it anyway
					if (this.bidData.has(match) || this.exportType === "pdf_processed") {
						return match;
					}
				}
			}
		}

		return null;
	}

	highlightElement(element, bidNumber, type) {
		const bidInfo = this.bidData.get(bidNumber);

		// Add highlight class
		element.classList.add("gem-bid-highlighted");
		element.setAttribute("data-gem-bid-number", bidNumber);
		element.setAttribute("data-gem-highlight-type", type);

		let backgroundColor, borderColor, textColor, statusText;

		if (type === "not-in-excel") {
			// Yellow for bids not in our Excel (only for PDF processed exports)
			backgroundColor = "#fef3c7";
			borderColor = "#f59e0b";
			textColor = "#92400e";
			statusText = "NOT IN DB";
		} else {
			// Bid is in our Excel data
			const status = bidInfo.status;

			if (this.exportType === "filtered" || status === "PRESENT") {
				// Green for filtered bids or present bids
				backgroundColor = "#d1fae5";
				borderColor = "#10b981";
				textColor = "#065f46";
				statusText = this.exportType === "filtered" ? "FILTERED" : "PRESENT";
			} else if (status === "ACCEPTED") {
				// Green for accepted
				backgroundColor = "#d1fae5";
				borderColor = "#10b981";
				textColor = "#065f46";
				statusText = "ACCEPTED";
			} else if (status === "REJECTED") {
				// Red for rejected
				backgroundColor = "#fee2e2";
				borderColor = "#ef4444";
				textColor = "#991b1b";
				statusText = "REJECTED";
			} else {
				// Yellow for pending
				backgroundColor = "#fef3c7";
				borderColor = "#f59e0b";
				textColor = "#92400e";
				statusText = "PENDING";
			}
		}

		element.style.backgroundColor = backgroundColor;
		element.style.border = `2px solid ${borderColor}`;
		element.style.borderRadius = "4px";
		element.style.position = "relative";

		// Add status indicator
		const indicator = document.createElement("div");
		indicator.className = "gem-bid-indicator";
		indicator.innerHTML = `
            <div class="gem-status-badge" style="
                position: absolute;
                top: -8px;
                right: -8px;
                background: ${borderColor};
                color: white;
                padding: 2px 6px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: bold;
                z-index: 1000;
                cursor: pointer;
            ">${statusText}</div>
        `;

		element.appendChild(indicator);
		this.highlightedElements.add(element);
	}

	createStatusPanel(highlightedCount, foundInExcelCount) {
		// Remove existing panel
		if (this.statusPanel) {
			this.statusPanel.remove();
		}

		const counts = this.calculateCounts();

		this.statusPanel = document.createElement("div");
		this.statusPanel.className = "gem-status-panel";

		let panelContent = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                padding: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 12px;
                min-width: 220px;
            ">
                <div style="font-weight: bold; margin-bottom: 8px; color: #333;">
                    🎯 GEM Bid Highlighter v2.0
                </div>
                <div style="margin-bottom: 4px;">
                    📋 Highlighted: ${highlightedCount}
                </div>
                <div style="margin-bottom: 4px;">
                    📊 Export Type: ${
											this.exportType === "pdf_processed"
												? "PDF Processed"
												: "Filtered Bids"
										}
                </div>
        `;

		if (this.exportType === "pdf_processed") {
			panelContent += `
                <div style="margin-bottom: 4px;">
                    🟢 In Excel (Accepted): ${counts.accepted}
                </div>
                <div style="margin-bottom: 4px;">
                    🔴 In Excel (Rejected): ${counts.rejected}
                </div>
                <div style="margin-bottom: 4px;">
                    🟡 In Excel (Pending): ${counts.pending}
                </div>
                <div style="margin-bottom: 8px;">
                    🟡 Not in Database: ${highlightedCount - foundInExcelCount}
                </div>
            `;
		} else {
			panelContent += `
                <div style="margin-bottom: 8px;">
                    🟢 Filtered Bids Found: ${foundInExcelCount}
                </div>
            `;
		}

		panelContent += `
                <button id="gem-close-panel" style="
                    background: #ef4444;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 10px;
                ">Close</button>
            </div>
        `;

		this.statusPanel.innerHTML = panelContent;
		document.body.appendChild(this.statusPanel);

		// Add close handler
		document.getElementById("gem-close-panel").addEventListener("click", () => {
			this.statusPanel.remove();
			this.statusPanel = null;
		});
	}

	calculateCounts() {
		let accepted = 0,
			rejected = 0,
			pending = 0;

		for (const bid of this.bidData.values()) {
			switch (bid.status) {
				case "ACCEPTED":
					accepted++;
					break;
				case "REJECTED":
					rejected++;
					break;
				case "PENDING":
					pending++;
					break;
			}
		}

		return {
			total: this.bidData.size,
			accepted,
			rejected,
			pending,
		};
	}

	clearAllHighlights() {
		// Remove highlight classes and styles
		this.highlightedElements.forEach((element) => {
			element.classList.remove("gem-bid-highlighted");
			element.removeAttribute("data-gem-bid-number");
			element.removeAttribute("data-gem-highlight-type");
			element.style.backgroundColor = "";
			element.style.border = "";
			element.style.borderRadius = "";

			// Remove indicators
			const indicator = element.querySelector(".gem-bid-indicator");
			if (indicator) {
				indicator.remove();
			}
		});

		this.highlightedElements.clear();

		// Remove status panel
		if (this.statusPanel) {
			this.statusPanel.remove();
			this.statusPanel = null;
		}

		console.log("GEM Bid Highlighter: Cleared all highlights");
	}
}

// Initialize the content script
const gemBidHighlighter = new GEMBidHighlighterContent();
