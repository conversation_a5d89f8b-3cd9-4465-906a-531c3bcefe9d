// src/components/layout/Drawer.tsx
'use client';

import React, { ReactNode } from 'react';
import { X } from 'lucide-react'; // Close icon

interface DrawerProps {
    isOpen: boolean;
    onClose: () => void;
    children: ReactNode;
    title?: string;
}

const Drawer: React.FC<DrawerProps> = ({ isOpen, onClose, children, title = "Workflows" }) => {
    if (!isOpen) return null;

    return (
        <>
            {/* Overlay */}
            <div 
                className="fixed inset-0 bg-black/30 z-40 backdrop-blur-sm" 
                onClick={onClose}
                aria-hidden="true"
            ></div>

            {/* Drawer Panel */}
            <div 
                className={`fixed top-0 right-0 h-full w-full max-w-sm bg-white dark:bg-gray-800 shadow-xl z-50 transform transition-transform ease-in-out duration-300
                           ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
                role="dialog"
                aria-modal="true"
                aria-labelledby="drawer-title"
            >
                <div className="flex flex-col h-full">
                    {/* Drawer Header */}
                    <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 id="drawer-title" className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                            {title}
                        </h2>
                        <button 
                            onClick={onClose}
                            className="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                            aria-label="Close drawer"
                        >
                            <X size={24} />
                        </button>
                    </div>

                    {/* Drawer Content */}
                    <div className="flex-grow p-4 overflow-y-auto">
                        {children}
                    </div>
                </div>
            </div>
        </>
    );
};

export default Drawer;