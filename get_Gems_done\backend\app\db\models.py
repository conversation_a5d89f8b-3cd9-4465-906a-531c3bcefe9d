# app/db/models.py
from sqlalchemy.sql import func
from .database import Base
from sqlalchemy import Column, Integer, String, DateTime, Text, func, Enum as SAEnum # Add SAEnum
import enum # For Python enumx

class PDFAnalysisStatusEnum(str, enum.Enum):
    PENDING = "PENDING_ANALYSIS"
    ACCEPTED = "PDF_ACCEPTED"
    REJECTED = "PDF_REJECTED"
    NO_DOCUMENT_URL = "NO_DOCUMENT_URL"
    PROCESSING_ERROR = "PDF_PROCESSING_ERROR"

class ScrapeSourceTypeEnum(str, enum.Enum):
    MINISTRY_ORGANIZATION = "ministry_organization"
    LOCATION = "location"

class Bid(Base):
    __tablename__ = "bids"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    bid_id_internal = Column(String, index=True, unique=True, nullable=True)
    bid_number = Column(String, unique=True, index=True, nullable=False)

    items = Column(Text, nullable=True) # This will be our primary target for filtering
    quantity = Column(Integer, nullable=True)

    ministry_name = Column(String, index=True, nullable=True)
    department_name = Column(String, index=True, nullable=True)

    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)

    bid_details_url = Column(String, nullable=True)

    pdf_analysis_status = Column(SAEnum(PDFAnalysisStatusEnum), nullable=True, default=PDFAnalysisStatusEnum.PENDING, index=True)
    pdf_analysis_reasons = Column(Text, nullable=True) # Store rejection reasons as JSON string or comma-separated
    pdf_last_analysed_at = Column(DateTime(timezone=True), nullable=True)
    pdf_processing_session = Column(String, nullable=True, index=True)  # Track which processing session analyzed this PDF

    # raw_api_doc = Column(SQLAlchemyJSON, nullable=True) # REMOVED THIS LINE

    scraped_at = Column(DateTime(timezone=True), server_default=func.now())

    # New fields for tracking scraping sessions and sources
    scrape_session_id = Column(String, nullable=True, index=True)  # UUID to group bids from same scrape session
    scrape_source_type = Column(SAEnum(ScrapeSourceTypeEnum), nullable=True, index=True)  # ministry_organization or location
    scrape_source_details = Column(String, nullable=True)  # JSON string with ministry/org or state/city details