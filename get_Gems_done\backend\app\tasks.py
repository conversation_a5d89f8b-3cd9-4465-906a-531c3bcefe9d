# app/tasks.py
import uuid
import threading
from enum import Enum
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional, List, Set

class TaskStatus(str, Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLING = "CANCELLING"  # New status for tasks being cancelled

class TaskInfo(BaseModel):
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4())) # Auto-generate UUID
    status: TaskStatus = TaskStatus.PENDING
    message: str = "Task initiated."
    details: Dict[str, Any] = {} # Store initial request details (ministry, org, location etc.)

    # Progress Indicators (optional, update from background task)
    progress_stage: Optional[str] = None # e.g., "Fetching page", "Saving", "Parsing PDF"
    progress_current: int = 0
    progress_total: int = 0

    # Result Indicators (optional, update when done)
    result_count: Optional[int] = None
    error_message: Optional[str] = None
    zip_filename: Optional[str] = None # To store the name of the generated ZIP file
    result_detail: Optional[Any] = None # Could store list of filenames, etc.

    # Cancellation flag
    should_cancel: bool = False  # Flag to indicate if the task should be cancelled

# --- In-memory Task Store ---
# WARNING: Simple dict is NOT suitable for multi-worker/production environments.
# Use Redis, a DB table, or a dedicated task queue system (like Celery with results backend) for that.
TASK_STATUS_STORE: Dict[str, TaskInfo] = {}

# Set to track active background tasks
ACTIVE_BACKGROUND_TASKS: Set[str] = set()
# Lock for thread-safe operations on the task store
TASK_STORE_LOCK = threading.Lock()
# --- End In-memory Task Store ---

def update_task_status(
    task_id: str,
    status: Optional[TaskStatus] = None,
    message: Optional[str] = None,
    stage: Optional[str] = None,
    current: Optional[int] = None,
    total: Optional[int] = None,
    count: Optional[int] = None,
    error: Optional[str] = None,
    zip_file: Optional[str] = None,  # New parameter for 'zip_filename'
    result_dtl: Optional[Any] = None, # For generic 'result_detail'

):
    """Helper to update task status in the store."""
    with TASK_STORE_LOCK:
        if task_id in TASK_STATUS_STORE:
            task = TASK_STATUS_STORE[task_id]
            if status is not None: task.status = status
            if message is not None: task.message = message
            if stage is not None: task.progress_stage = stage
            if current is not None: task.progress_current = current
            if total is not None: task.progress_total = total
            if count is not None: task.result_count = count
            if error is not None: task.error_message = error # Correctly maps 'error' param to 'error_message' field

            if zip_file is not None: task.zip_filename = zip_file # Maps to task.zip_filename
            if result_dtl is not None: task.result_detail = result_dtl
            # print(f"Task Update [{task_id}]: Status={task.status}, Msg={task.message}") # Optional debug log
        else:
            print(f"Warning: Attempted to update non-existent task_id: {task_id}")

def should_task_cancel(task_id: str) -> bool:
    """Check if a task should be cancelled."""
    with TASK_STORE_LOCK:
        if task_id in TASK_STATUS_STORE:
            return TASK_STATUS_STORE[task_id].should_cancel
        return False

def cancel_task(task_id: str) -> bool:
    """Mark a task for cancellation."""
    with TASK_STORE_LOCK:
        if task_id in TASK_STATUS_STORE:
            task = TASK_STATUS_STORE[task_id]
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                task.should_cancel = True
                task.status = TaskStatus.CANCELLING
                task.message = "Task cancellation requested"
                return True
        return False

def register_background_task(task_id: str):
    """Register a background task as active."""
    with TASK_STORE_LOCK:
        ACTIVE_BACKGROUND_TASKS.add(task_id)

def unregister_background_task(task_id: str):
    """Unregister a background task."""
    with TASK_STORE_LOCK:
        ACTIVE_BACKGROUND_TASKS.discard(task_id)