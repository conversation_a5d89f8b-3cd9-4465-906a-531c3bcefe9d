/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    color: #1a202c;
    line-height: 1.5;
}

.container {
    width: 380px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.version {
    font-size: 12px;
    opacity: 0.9;
}

/* Sections */
section {
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
}

section:last-child {
    border-bottom: none;
}

/* Upload Section */
.upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f7fafc;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #edf2f7;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-icon {
    font-size: 32px;
}

.upload-text strong {
    display: block;
    font-size: 14px;
    color: #2d3748;
    margin-bottom: 4px;
}

.upload-text p {
    font-size: 12px;
    color: #718096;
}

.file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f7fafc;
    padding: 12px;
    border-radius: 6px;
    margin-top: 12px;
}

.file-name {
    font-size: 13px;
    color: #2d3748;
    font-weight: 500;
}

.clear-btn {
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Export Type */
.export-type {
    background: #f7fafc;
    text-align: center;
}

.type-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.type-badge.filtered {
    background: #bee3f8;
    color: #2b6cb0;
}

.type-badge.pdf-processed {
    background: #c6f6d5;
    color: #276749;
}

.type-description {
    font-size: 12px;
    color: #718096;
}

/* Statistics */
.stats h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #2d3748;
}

.stat-grid {
    display: grid;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.stat-label {
    font-size: 13px;
    color: #718096;
}

.stat-value {
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
}

/* Status */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
}

.status-label {
    font-size: 12px;
    color: #718096;
}

.status-value {
    font-size: 12px;
    font-weight: 600;
    color: #2d3748;
}

.status-value.gem-site {
    color: #38a169;
}

.status-value.not-gem-site {
    color: #e53e3e;
}

.status-value.active {
    color: #38a169;
}

.status-value.inactive {
    color: #718096;
}

/* Buttons */
.btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a67d8;
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #4a5568;
}

/* Messages */
.messages {
    min-height: 20px;
}

.message {
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 12px;
    line-height: 1.4;
}

.message.success {
    background: #c6f6d5;
    color: #276749;
    border: 1px solid #9ae6b4;
}

.message.error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.message.info {
    background: #bee3f8;
    color: #2b6cb0;
    border: 1px solid #90cdf4;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message {
    animation: fadeIn 0.3s ease;
}

/* Loading state */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
