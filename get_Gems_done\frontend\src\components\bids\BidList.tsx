// src/components/bids/BidList.tsx
"use client";

import React, { useState, useEffect, useCallback } from "react";
import { fetchApi, ApiError } from "@/lib/api";
import LoadingBar from "@/components/ui/LoadingBar";
import StatusBadge from "@/components/ui/StatusBadge";
import { format } from "date-fns";

// This interface should match your backend's BidInDB schema (without raw_api_doc)
export interface Bid {
	id: number; // DB primary key
	bid_id_internal: string | null; // GeM's internal ID for the bid document
	bid_number: string;
	items: string | null;
	quantity: number | null;
	ministry_name: string | null;
	department_name: string | null;
	start_date: string | null; // ISO string from backend
	end_date: string | null; // ISO string from backend
	bid_details_url: string | null; // Assumed to be direct PDF link or page leading to it
	scraped_at: string; // ISO string from backend

	// New fields for tracking scraping sessions and sources
	scrape_session_id?: string | null;
	scrape_source_type?: "ministry_organization" | "location" | null;
	scrape_source_details?: string | null;
}

interface BidCardProps {
	bid: Bid;
	downloadApiUrl: string; // Base URL for downloading single PDF by internal ID
}

const BidCard: React.FC<BidCardProps> = ({ bid, downloadApiUrl }) => {
	const formatDate = (dateString: string | null) => {
		if (!dateString) return "N/A";
		try {
			// Parse the date and ensure it's treated as UTC if it doesn't have timezone info
			const date = new Date(dateString);
			// If the date string doesn't include timezone info, it might be treated as local time
			// For UTC dates from backend, we want to display them in local timezone
			return format(date, "dd MMM yyyy, hh:mm a");
		} catch (e) {
			// console.error("Date formatting error:", e);
			return "Invalid Date";
		}
	};

	const handleDownload = () => {
		if (bid.bid_id_internal) {
			// This endpoint constructs the URL to GET the PDF directly from your backend
			const url = `${downloadApiUrl}/${bid.bid_id_internal}`;
			window.open(url, "_blank"); // Opens the backend URL which streams the PDF
		} else {
			alert("Cannot download: Internal Bid ID is missing for this bid.");
		}
	};

	return (
		<div className="card p-5 transition-all duration-200 flex flex-col justify-between h-full">
			<div>
				<div className="flex justify-between items-start mb-5">
					<h3 className="text-lg font-semibold text-gray-800 break-all">
						{bid.bid_number}
					</h3>
					{bid.bid_id_internal && bid.bid_details_url && (
						<button
							onClick={handleDownload}
							title="Download Bid Document"
							className="btn btn-primary ml-3 text-sm py-2 px-4 whitespace-nowrap"
						>
							Download PDF
						</button>
					)}
				</div>
				<div className="bg-gray-50 p-4 rounded-md mb-5">
					<p className="text-base text-gray-700 mb-3 break-words">
						<span className="font-medium text-gray-800">Items:</span>{" "}
						{bid.items
							? `${bid.items.substring(0, 120)}${
									bid.items.length > 120 ? "..." : ""
							  }`
							: "N/A"}
					</p>
				</div>
				<p className="text-base text-gray-700 mb-3">
					<span className="font-medium text-gray-800">Ministry:</span>{" "}
					{bid.ministry_name || "N/A"}
				</p>
				<p className="text-base text-gray-700">
					<span className="font-medium text-gray-800">Dept:</span>{" "}
					{bid.department_name || "N/A"}
				</p>
			</div>
			<div>
				<p className="text-sm text-gray-600 mt-3">
					Start: {formatDate(bid.start_date)}
				</p>
				<div className="flex justify-between text-sm text-gray-600 mt-6 pt-4 border-t border-gray-200">
					<span>
						<span className="font-medium">End:</span> {formatDate(bid.end_date)}
					</span>
					<span>
						<span className="font-medium">Qty:</span> {bid.quantity ?? "N/A"}
					</span>
				</div>
				<p className="text-sm text-gray-600 mt-3">
					Scraped: {formatDate(bid.scraped_at)}
				</p>
			</div>
		</div>
	);
};

interface BidListProps {
	bidsApiUrl: string; // Base URL to fetch all bids e.g., /api/v1/bids
	downloadApiUrl: string; // Base URL for downloading single PDF by internal ID e.g., /api/v1/bids/download/pdf
	bulkDownloadApiUrl: string; // Not used yet in this component version
	pdfCheckApiUrl: string; // Not used yet in this component version
	initialBids: Bid[] | null; // Filtered bids from parent, or null to fetch all
}

const API_ALL_BIDS_FETCH_LIMIT = 50; // How many "all bids" to fetch from API at a time
const UI_ITEMS_PER_PAGE = 10; // How many bids to show per page in the UI

const BidList: React.FC<BidListProps> = ({
	bidsApiUrl,
	downloadApiUrl,
	initialBids,
}) => {
	// This state holds the master list of bids (either all fetched so far, or the filtered set)
	const [masterBidList, setMasterBidList] = useState<Bid[]>([]);
	// Loading state for API calls to fetch "all bids"
	const [isLoadingApi, setIsLoadingApi] = useState(false);
	const [apiError, setApiError] = useState<string | null>(null);

	// For fetching "all bids" from API when initialBids is null
	const [apiPageToFetch, setApiPageToFetch] = useState(0); // 0-indexed for API skip
	const [hasMoreFromApi, setHasMoreFromApi] = useState(true);

	// For client-side UI pagination
	const [currentPageUI, setCurrentPageUI] = useState(1); // 1-indexed

	const fetchAllBidsChunk = useCallback(
		async (pageNumForApi: number) => {
			if (!hasMoreFromApi && pageNumForApi > 0) {
				setIsLoadingApi(false);
				return;
			}
			setIsLoadingApi(true);
			setApiError(null);

			const apiSkip = pageNumForApi * API_ALL_BIDS_FETCH_LIMIT;
			const url = `${bidsApiUrl}?skip=${apiSkip}&limit=${API_ALL_BIDS_FETCH_LIMIT}`;
			console.log(`BidList: Fetching ALL bids chunk from ${url}`);

			try {
				const fetchedBidsFromApi = await fetchApi<Bid[]>(url);
				if (fetchedBidsFromApi && fetchedBidsFromApi.length > 0) {
					setMasterBidList((prevBids) => {
						const existingIds = new Set(prevBids.map((b) => b.id));
						const newUniqueBids = fetchedBidsFromApi.filter(
							(b) => !existingIds.has(b.id)
						);
						return pageNumForApi === 0
							? newUniqueBids
							: [...prevBids, ...newUniqueBids];
					});
					setHasMoreFromApi(
						fetchedBidsFromApi.length === API_ALL_BIDS_FETCH_LIMIT
					);
				} else {
					setHasMoreFromApi(false);
					if (pageNumForApi === 0) setMasterBidList([]); // No bids at all if first fetch is empty
				}
			} catch (err) {
				console.error("Failed to fetch bids:", err);
				const errorMsg =
					err instanceof Error ? err.message : "An unknown error occurred.";
				setApiError(`Error fetching bids: ${errorMsg}`);
				setHasMoreFromApi(false);
			} finally {
				setIsLoadingApi(false);
			}
		},
		[bidsApiUrl] // Remove hasMoreFromApi to prevent infinite re-renders
	);

	useEffect(() => {
		if (initialBids === null) {
			// No filter applied by parent, fetch all
			console.log(
				"BidList: initialBids is null. Fetching initial chunk of all bids."
			);
			setMasterBidList([]); // Clear previous list
			setApiPageToFetch(0); // Reset API page tracker
			setHasMoreFromApi(true); // Assume there might be data
			setCurrentPageUI(1); // Reset UI pagination

			// Immediate fetch for the first chunk
			fetchAllBidsChunk(0); // Fetch first chunk
		} else if (Array.isArray(initialBids)) {
			// Filtered bids are provided by parent
			console.log(
				`BidList: Received ${initialBids.length} initialBids (filtered). Displaying them.`
			);
			setMasterBidList(initialBids);
			setCurrentPageUI(1); // Reset UI pagination for the new filtered list
			setApiError(null);
			setIsLoadingApi(false); // Not loading from API in this case
			setHasMoreFromApi(false); // When displaying filtered, we assume we have the whole filtered set
		}
	}, [initialBids]); // Remove fetchAllBidsChunk dependency

	// Removed redundant useEffect - the first one already handles initial fetch

	// Client-side pagination logic for the 'masterBidList'
	const indexOfLastBidUI = currentPageUI * UI_ITEMS_PER_PAGE;
	const indexOfFirstBidUI = indexOfLastBidUI - UI_ITEMS_PER_PAGE;
	const currentBidsForUIPage = masterBidList.slice(
		indexOfFirstBidUI,
		indexOfLastBidUI
	);
	const totalUIPages = Math.ceil(masterBidList.length / UI_ITEMS_PER_PAGE);

	const paginateUI = (pageNumber: number) => {
		if (pageNumber > 0 && pageNumber <= totalUIPages) {
			setCurrentPageUI(pageNumber);
		}
	};

	const handleLoadMoreFromApi = () => {
		if (!isLoadingApi && initialBids === null && hasMoreFromApi) {
			// If we have no bids yet and this is the first load, start from page 0
			const nextPageToFetchFromApi =
				masterBidList.length === 0 ? 0 : apiPageToFetch + 1;
			setApiPageToFetch(nextPageToFetchFromApi);
			fetchAllBidsChunk(nextPageToFetchFromApi);
		}
	};

	return (
		<div className="w-full">
			{apiError && (
				<div className="mb-6">
					<div className="p-4 text-red-700 bg-red-50 border border-red-200 rounded-md">
						{apiError}
					</div>
				</div>
			)}

			{isLoadingApi && masterBidList.length === 0 && (
				<div className="text-center py-8 bg-gray-50 rounded-md">
					<div className="flex flex-col items-center gap-4">
						<StatusBadge status="running" text="Loading" />
						<p className="text-gray-700">Loading bids from server...</p>
						<LoadingBar height={4} className="w-64" />
					</div>
				</div>
			)}

			{!isLoadingApi && masterBidList.length === 0 && !apiError && (
				<div className="text-center py-8 bg-gray-50 rounded-md">
					<div className="flex flex-col items-center gap-2">
						<StatusBadge status="completed" text="No Results" />
						<p className="text-gray-700">
							No bids found or matching current filter.
						</p>
					</div>
				</div>
			)}

			{currentBidsForUIPage.length > 0 && (
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
					{currentBidsForUIPage.map((bid) => (
						<div
							className="p-5 transition-all duration-200 flex flex-col justify-between h-full"
							key={bid.id}
						>
							<BidCard bid={bid} downloadApiUrl={downloadApiUrl} />
						</div>
					))}
				</div>
			)}

			{/* UI Pagination Controls */}
			{totalUIPages > 1 && (
				<div className="flex justify-center items-center mt-8 mb-4">
					<button
						onClick={() => paginateUI(currentPageUI - 1)}
						disabled={currentPageUI === 1 || isLoadingApi}
						className="btn btn-secondary"
						style={{
							height: "30px",
							border: "none",
						}}
					>
						Previous
					</button>
					<span className="text-base text-gray-600 bg-white px-4 py-2 rounded-md border border-gray-200">
						Page {currentPageUI} of {totalUIPages}
					</span>
					<button
						onClick={() => paginateUI(currentPageUI + 1)}
						disabled={currentPageUI === totalUIPages || isLoadingApi}
						className="btn btn-secondary"
						style={{
							height: "30px",
							border: "none",
						}}
					>
						Next
					</button>
				</div>
			)}

			{/* Button to load more data from API when viewing "all bids" */}
			{initialBids === null && !isLoadingApi && hasMoreFromApi && (
				<div className="text-center mt-8">
					<button onClick={handleLoadMoreFromApi} className="btn btn-primary">
						{masterBidList.length === 0
							? "Load Bids From Server"
							: "Load More From Server"}
					</button>
				</div>
			)}

			{isLoadingApi && initialBids === null && masterBidList.length > 0 && (
				<div className="text-center py-6 bg-blue-50 rounded-md border border-blue-100 mt-6">
					<div className="flex flex-col items-center gap-3">
						<StatusBadge status="running" text="Loading More" />
						<p className="text-blue-700">Loading more from server...</p>
						<LoadingBar height={3} variant="primary" className="w-48" />
					</div>
				</div>
			)}

			{initialBids === null &&
				!hasMoreFromApi &&
				masterBidList.length > 0 &&
				!isLoadingApi && (
					<div className="text-center mt-8 bg-green-50 rounded-md border border-green-100 py-3">
						<p className="text-green-700">-- All bids from server loaded --</p>
					</div>
				)}
		</div>
	);
};

export default BidList;
