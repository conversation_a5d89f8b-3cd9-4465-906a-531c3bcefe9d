# app/api/v1/endpoints/gem_data_proxy.py
from pydantic import BaseModel
import requests
import json
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, status
from app.core.config import settings # To get CSRF_COOKIE_NAME, GEM_INITIAL_PAGE_URL etc.

router = APIRouter()

# --- GeM Data Fetching Session & CSRF Helper ---
# This session will be used for all calls to GeM data endpoints to maintain cookies.
# For development with a single Uvicorn worker, a module-level session is okay.
# For production with multiple workers, consider a more sophisticated session management
# or ensure each worker process gets its own session if state needs to be isolated,
# though for just fetching CSRF and passing it, this might be acceptable.
GEM_PROXY_SESSION = requests.Session()
GEM_PROXY_SESSION.headers.update({
    "User-Agent": settings.REQUEST_HEADERS.get("User-Agent"),
    "X-Requested-With": "XMLHttpRequest",
    "Accept": "application/json, text/javascript, */*",
    # Origin and Referer will be set by requests for specific calls
})

# Store the CSRF token fetched by the session
# This is a simple in-memory cache for the token.
# In a multi-process/worker setup, this would need a shared cache (e.g., Redis).
CACHED_CSRF_TOKEN: Optional[str] = None


async def _get_and_cache_csrf_token() -> Optional[str]:
    """
    Fetches the initial GeM page to get/refresh the CSRF cookie value
    and caches it. Uses the module-level GEM_PROXY_SESSION.
    The CSRF token from the cookie will be used as the value for 'csrf_bd_gem_nk' form field.
    """
    global CACHED_CSRF_TOKEN
    try:
        # print("Proxy: Attempting to fetch initial page for CSRF token...")
        response = GEM_PROXY_SESSION.get(str(settings.GEM_INITIAL_PAGE_URL), timeout=(20, 40))
        response.raise_for_status()
        token = GEM_PROXY_SESSION.cookies.get(settings.CSRF_COOKIE_NAME)
        if token:
            CACHED_CSRF_TOKEN = token
            # print(f"Proxy: Successfully fetched and cached CSRF token: {token[:10]}...")
            return token
        else:
            print(f"Proxy Error: CSRF cookie '{settings.CSRF_COOKIE_NAME}' not found after fetching initial page.")
            CACHED_CSRF_TOKEN = None # Invalidate cache
            return None
    except requests.RequestException as e:
        print(f"Proxy Error: Failed to fetch initial page for CSRF: {e}")
        CACHED_CSRF_TOKEN = None # Invalidate cache
        return None

async def _get_csrf_token_for_payload() -> str:
    """
    Ensures a CSRF token is available, fetching a new one if necessary.
    Raises HTTPException if unable to obtain a token.
    """
    # Try to use cached token first
    if CACHED_CSRF_TOKEN:
        # Optionally add a check here if the token might be stale (e.g., based on time)
        # For now, we assume if it's cached, it's good for a short while.
        # print("Proxy: Using cached CSRF token.")
        return CACHED_CSRF_TOKEN

    token = await _get_and_cache_csrf_token()
    if not token:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE, 
            detail="Could not obtain necessary CSRF token from GeM. GeM might be down or unresponsive."
        )
    return token


# --- Pydantic Models for Response Types ---
class StateResponseItem(BaseModel):
    state_name: str
    state_id: str

class CityResponseItem(BaseModel):
    city_name: str
    pin_code: Optional[str] = None
    

# --- Ministry Endpoint ---
@router.get("/gem-data/ministries", response_model=List[str], summary="Fetch list of ministries from GeM")
async def get_gem_ministries_proxy():
    gem_url = "https://bidplus.gem.gov.in/ministry-list-adv"
    csrf_token = await _get_csrf_token_for_payload()
    payload = {settings.CSRF_FORM_FIELD_NAME: csrf_token} # e.g., {"csrf_bd_gem_nk": token_value}

    try:
        # print(f"Proxy (Ministries): POSTing to {gem_url} with payload containing CSRF.")
        response = GEM_PROXY_SESSION.post(gem_url, data=payload, timeout=(10, 20))
        response.raise_for_status()
        
        data = response.json()
        ministry_list = data.get("data", {}).get("MinistryList", [])
        # ministry_list = data.get("data", {}).get("ministryList", [])
        
        if not isinstance(ministry_list, list) or (ministry_list and not all(isinstance(item, str) for item in ministry_list)):
            print(f"Proxy Error (Ministries): Unexpected structure for ministryList: {ministry_list}")
            raise HTTPException(status_code=502, detail="Unexpected data structure from GeM for ministries.")
        
        return ministry_list
    except requests.RequestException as e:
        print(f"Proxy Error (Ministries): Request to GeM failed: {e}")
        # If a request fails, the CSRF token might have become invalid. Try refreshing it for next time.
        await _get_and_cache_csrf_token() 
        raise HTTPException(status_code=502, detail=f"Failed to fetch ministries from GeM: {e}")
    except json.JSONDecodeError:
        print(f"Proxy Error (Ministries): GeM response was not valid JSON. Content: {response.text[:200]}")
        raise HTTPException(status_code=502, detail="GeM returned non-JSON response for ministries.")
    except Exception as e:
        print(f"Proxy Error (Ministries): Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal error fetching ministries.")

# --- Organization Endpoint ---
@router.get("/gem-data/organizations", response_model=List[str], summary="Fetch organizations for a given ministry from GeM")
async def get_gem_organizations_proxy(ministry_name: str):
    if not ministry_name:
        raise HTTPException(status_code=400, detail="Ministry name query parameter is required.")
        
    gem_url = "https://bidplus.gem.gov.in/org-list-adv"
    csrf_token = await _get_csrf_token_for_payload()
    payload = {
        "ministry": ministry_name,
        settings.CSRF_FORM_FIELD_NAME: csrf_token 
    }

    try:
        # print(f"Proxy (Organizations): POSTing to {gem_url} for ministry '{ministry_name}'.")
        response = GEM_PROXY_SESSION.post(gem_url, data=payload, timeout=(10, 20))
        response.raise_for_status()
        
        data = response.json()
        # Adjust key if necessary based on actual response structure for organizations
        org_list = data
        # org_list = data.get("data", {}).get("orgList", []) 
        
        if not isinstance(org_list, list) or (org_list and not all(isinstance(item, str) for item in org_list)):
            print(f"Proxy Error (Organizations): Unexpected structure for orgList: {org_list}")
            raise HTTPException(status_code=502, detail="Unexpected data structure from GeM for organizations.")

        return org_list
    except requests.RequestException as e:
        print(f"Proxy Error (Organizations): Request to GeM failed: {e}")
        await _get_and_cache_csrf_token()
        raise HTTPException(status_code=502, detail=f"Failed to fetch organizations from GeM: {e}")
    except json.JSONDecodeError:
        print(f"Proxy Error (Organizations): GeM response not valid JSON. Content: {response.text[:200]}")
        raise HTTPException(status_code=502, detail="GeM returned non-JSON response for organizations.")
    except Exception as e:
        print(f"Proxy Error (Organizations): Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal error fetching organizations.")


# --- State Endpoint ---
class StateResponseItem(BaseModel): # Pydantic model for state items
    state_name: str
    state_id: str

@router.get("/gem-data/states", response_model=List[StateResponseItem], summary="Fetch list of states from GeM")
async def get_gem_states_proxy():
    gem_url = "https://bidplus.gem.gov.in/state-list-adv"
    csrf_token = await _get_csrf_token_for_payload()
    payload = {settings.CSRF_FORM_FIELD_NAME: csrf_token}

    try:
        # print(f"Proxy (States): POSTing to {gem_url}.")
        response = GEM_PROXY_SESSION.post(gem_url, data=payload, timeout=(10, 20))
        response.raise_for_status()
        
        data = response.json()
        # Based on your screenshot, 'data' is directly the list of state objects
        state_list_raw = data.get("data", []) 
        
        # Validate structure and convert to Pydantic model list
        validated_states: List[StateResponseItem] = []
        if not isinstance(state_list_raw, list):
            raise ValueError("State list is not a list.")
        for item in state_list_raw:
            if not isinstance(item, dict) or "state_name" not in item or "state_id" not in item:
                print(f"Proxy Warning (States): Skipping invalid state item: {item}")
                continue
            validated_states.append(StateResponseItem(state_name=str(item["state_name"]), state_id=str(item["state_id"])))
        
        return validated_states
    except requests.RequestException as e: # ... (similar error handling) ...
        print(f"Proxy Error (States): Request to GeM failed: {e}")
        await _get_and_cache_csrf_token()
        raise HTTPException(status_code=502, detail=f"Failed to fetch states from GeM: {e}")
    except (json.JSONDecodeError, ValueError) as e_parse: # Catch parsing or validation errors
        print(f"Proxy Error (States): GeM response parsing/validation failed. Error: {e_parse}. Content: {response.text[:200] if 'response' in locals() else 'N/A'}")
        raise HTTPException(status_code=502, detail="GeM returned unexpected data for states.")
    except Exception as e: # ... (similar error handling) ...
        print(f"Proxy Error (States): Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal error fetching states.")


# --- City Endpoint ---
class CityResponseItem(BaseModel): # Pydantic model for city items
    city_name: str
    pin_code: Optional[str] = None # pin_code might not always be there or needed

@router.get("/gem-data/cities", response_model=List[CityResponseItem], summary="Fetch cities for a given state from GeM")
async def get_gem_cities_proxy(state_name: str): # GeM API expects state_name, not state_id for this call
    if not state_name:
        raise HTTPException(status_code=400, detail="State name query parameter is required.")

    gem_url = "https://bidplus.gem.gov.in/city-list-adv"
    csrf_token = await _get_csrf_token_for_payload()
    payload = {
        "state_name": state_name, # GeM uses state_name for this request
        settings.CSRF_FORM_FIELD_NAME: csrf_token
    }

    try:
        # print(f"Proxy (Cities): POSTing to {gem_url} for state '{state_name}'.")
        response = GEM_PROXY_SESSION.post(gem_url, data=payload, timeout=(10, 20))
        response.raise_for_status()
        
        data = response.json()
        city_list_raw = data.get("data", [])

        validated_cities: List[CityResponseItem] = []
        if not isinstance(city_list_raw, list):
            raise ValueError("City list is not a list.")
        for item in city_list_raw:
            if not isinstance(item, dict) or "city_name" not in item:
                print(f"Proxy Warning (Cities): Skipping invalid city item: {item}")
                continue
            validated_cities.append(CityResponseItem(city_name=str(item["city_name"]), pin_code=str(item.get("pin_code")) if item.get("pin_code") else None))
        
        return validated_cities
    except requests.RequestException as e: # ... (similar error handling) ...
        print(f"Proxy Error (Cities): Request to GeM failed: {e}")
        await _get_and_cache_csrf_token()
        raise HTTPException(status_code=502, detail=f"Failed to fetch cities from GeM: {e}")
    except (json.JSONDecodeError, ValueError) as e_parse: # ... (similar error handling) ...
        print(f"Proxy Error (Cities): GeM response parsing/validation failed. Error: {e_parse}. Content: {response.text[:200] if 'response' in locals() else 'N/A'}")
        raise HTTPException(status_code=502, detail="GeM returned unexpected data for cities.")
    except Exception as e: # ... (similar error handling) ...
        print(f"Proxy Error (Cities): Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal error fetching cities.")