#!/usr/bin/env python3
"""
Test Script for Enhanced Vendor API Tool Features
=================================================

This script tests all the enhanced features of the vendor API tool including:
- System status checking
- Configuration validation
- Enhanced UI components
- Error handling improvements
- Connection verification

Author: AI Assistant
Date: 2025-07-02
"""

import sys
import traceback
from interactive_ui import VendorAPIUI
from vendor_api_tool import VendorAPITool, FILTER_OPTIONS

def test_ui_creation():
    """Test UI creation and basic functionality."""
    print("🧪 Testing UI Creation...")
    try:
        ui = VendorAPIUI()
        print("✅ UI created successfully")
        return True
    except Exception as e:
        print(f"❌ UI creation failed: {e}")
        return False

def test_banner_display():
    """Test banner display."""
    print("\n🧪 Testing Banner Display...")
    try:
        ui = VendorAPIUI()
        ui.display_banner()
        print("✅ Banner displayed successfully")
        return True
    except Exception as e:
        print(f"❌ Banner display failed: {e}")
        return False

def test_menu_display():
    """Test menu display with status indicators."""
    print("\n🧪 Testing Menu Display...")
    try:
        ui = VendorAPIUI()
        ui.display_menu()
        print("✅ Menu displayed successfully")
        return True
    except Exception as e:
        print(f"❌ Menu display failed: {e}")
        return False

def test_system_status():
    """Test system status checking."""
    print("\n🧪 Testing System Status...")
    try:
        ui = VendorAPIUI()
        ui.show_system_status()
        print("✅ System status check completed")
        return True
    except Exception as e:
        print(f"❌ System status check failed: {e}")
        return False

def test_configuration_validation():
    """Test configuration validation."""
    print("\n🧪 Testing Configuration Validation...")
    try:
        ui = VendorAPIUI()
        ui.validate_configuration()
        print("✅ Configuration validation completed")
        return True
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

def test_view_configuration():
    """Test configuration viewing."""
    print("\n🧪 Testing Configuration View...")
    try:
        ui = VendorAPIUI()
        ui.view_configuration()
        print("✅ Configuration view completed")
        return True
    except Exception as e:
        print(f"❌ Configuration view failed: {e}")
        return False

def test_api_tool_creation():
    """Test API tool creation and connection verification."""
    print("\n🧪 Testing API Tool Creation...")
    try:
        api_tool = VendorAPITool()
        print("✅ API tool created successfully")
        
        # Test system status
        status = api_tool.get_system_status()
        print(f"✅ System status retrieved: {len(status)} fields")
        return True
    except Exception as e:
        print(f"❌ API tool creation failed: {e}")
        return False

def test_filter_options():
    """Test FILTER_OPTIONS structure."""
    print("\n🧪 Testing Filter Options...")
    try:
        required_keys = ['factories', 'industry_categories', 'used_for', 'sorting']
        for key in required_keys:
            if key not in FILTER_OPTIONS:
                print(f"❌ Missing key: {key}")
                return False
            print(f"✅ Found {key}: {len(FILTER_OPTIONS[key])} options")
        
        print("✅ All filter options validated")
        return True
    except Exception as e:
        print(f"❌ Filter options test failed: {e}")
        return False

def test_search_params_display():
    """Test search parameters display with sample data."""
    print("\n🧪 Testing Search Parameters Display...")
    try:
        ui = VendorAPIUI()
        # Add some sample search parameters
        ui.config['search_params'] = {
            'factory_id': '1',
            'industry_category': 'L',
            'used_for': 'R',
            'vendor': 'Test Vendor'
        }
        ui.display_current_search_params()
        print("✅ Search parameters display completed")
        return True
    except Exception as e:
        print(f"❌ Search parameters display failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests and provide summary."""
    print("=" * 70)
    print("🧪 ENHANCED VENDOR API TOOL - COMPREHENSIVE TEST SUITE")
    print("=" * 70)
    
    tests = [
        ("UI Creation", test_ui_creation),
        ("Banner Display", test_banner_display),
        ("Menu Display", test_menu_display),
        ("System Status", test_system_status),
        ("Configuration Validation", test_configuration_validation),
        ("Configuration View", test_view_configuration),
        ("API Tool Creation", test_api_tool_creation),
        ("Filter Options", test_filter_options),
        ("Search Parameters Display", test_search_params_display)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print("-" * 70)
    print(f"📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced tool is ready for use.")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    print("=" * 70)
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
