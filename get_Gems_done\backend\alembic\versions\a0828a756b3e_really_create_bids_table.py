"""really_create_bids_table

Revision ID: a0828a756b3e
Revises: 
Create Date: 2025-05-08 11:17:03.759818

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a0828a756b3e'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bids',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('bid_id_internal', sa.String(), nullable=True),
    sa.Column('bid_number', sa.String(), nullable=False),
    sa.Column('items', sa.Text(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('ministry_name', sa.String(), nullable=True),
    sa.Column('department_name', sa.String(), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('bid_details_url', sa.String(), nullable=True),
    sa.Column('scraped_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bids_bid_id_internal'), 'bids', ['bid_id_internal'], unique=True)
    op.create_index(op.f('ix_bids_bid_number'), 'bids', ['bid_number'], unique=True)
    op.create_index(op.f('ix_bids_department_name'), 'bids', ['department_name'], unique=False)
    op.create_index(op.f('ix_bids_id'), 'bids', ['id'], unique=False)
    op.create_index(op.f('ix_bids_ministry_name'), 'bids', ['ministry_name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bids_ministry_name'), table_name='bids')
    op.drop_index(op.f('ix_bids_id'), table_name='bids')
    op.drop_index(op.f('ix_bids_department_name'), table_name='bids')
    op.drop_index(op.f('ix_bids_bid_number'), table_name='bids')
    op.drop_index(op.f('ix_bids_bid_id_internal'), table_name='bids')
    op.drop_table('bids')
    # ### end Alembic commands ###
