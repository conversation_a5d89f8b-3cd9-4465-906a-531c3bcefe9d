// src/components/pdf/PdfProcessingControls.tsx
"use client";
import React, { useState, useEffect, useRef } from "react"; // Added useEffect, useRef
import { fetchApi, ApiError, stopTask } from "@/lib/api"; // Added stopTask
import LoadingBar from "@/components/ui/LoadingBar";
import ProgressBar from "@/components/ui/ProgressBar";
import StatusBadge from "@/components/ui/StatusBadge";
import { StopCircle } from "lucide-react";

// Re-using TaskInfo structure for status display
interface BackendTaskInfo {
	task_id: string;
	status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED";
	message: string;
	details?: Record<string, unknown>;
	progress_stage?: string | null;
	progress_current?: number;
	progress_total?: number;
	result_count?: number | null; // e.g., number accepted
	error_message?: string | null;
	result_detail?: Record<string, unknown> | null; // Could hold { accepted_dir_rel, rejected_dir_rel, processed_results }
}

interface PdfCheckResultItemClient {
	// From backend's PDFProcessResultItem
	filename: string; // original filename
	status: string;
	reasons: string[];
	// new_path: string; // Server path where it was moved
}

interface PdfProcessingControlsProps {
	uploadAndProcessApiUrl: string; // e.g., /api/v1/bids/upload-and-process-pdfs
	statusApiUrl: string; // For polling the processing task
}

const PdfProcessingControls: React.FC<PdfProcessingControlsProps> = ({
	uploadAndProcessApiUrl,
	statusApiUrl,
}) => {
	const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [processStatusMessage, setProcessStatusMessage] = useState<
		string | null
	>(null);
	const [processError, setProcessError] = useState<string | null>(null);
	const [activeProcessTaskId, setActiveProcessTaskId] = useState<string | null>(
		null
	);
	const [processProgress, setProcessProgress] = useState<{
		current: number;
		total: number;
		count: number;
		stage: string;
	} | null>(null);
	const [detailedResults, setDetailedResults] = useState<
		PdfCheckResultItemClient[] | null
	>(null);
	const [serverResultDirs, setServerResultDirs] = useState<{
		accepted: string;
		rejected: string;
	} | null>(null);

	const fileInputRef = useRef<HTMLInputElement>(null);
	const pollIntervalIdRef = useRef<NodeJS.Timeout | null>(null);

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.files && event.target.files.length > 0) {
			setSelectedFiles(event.target.files);
			// setProcessResults(null); // Clear previous results
			setServerResultDirs(null);
			setProcessStatusMessage(`${event.target.files.length} file(s) selected.`);
		} else {
			setSelectedFiles(null);
			setProcessStatusMessage("No files selected.");
		}
	};

	const handleProcessPdfs = async () => {
		if (!selectedFiles || selectedFiles.length === 0) {
			setProcessError("Please select PDF files or a folder to process.");
			return;
		}
		setIsProcessing(true);
		setProcessError(null);
		// setProcessResults(null);
		setServerResultDirs(null);
		setProcessProgress(null); // Clear previous progress
		setProcessStatusMessage("Uploading PDFs and initiating analysis...");
		if (pollIntervalIdRef.current) clearInterval(pollIntervalIdRef.current);

		const formData = new FormData();
		let pdfCount = 0;
		for (let i = 0; i < selectedFiles.length; i++) {
			if (
				selectedFiles[i].type === "application/pdf" ||
				selectedFiles[i].name.toLowerCase().endsWith(".pdf")
			) {
				formData.append("files", selectedFiles[i]); // "files" must match backend File(...) param name
				pdfCount++;
			}
		}

		if (pdfCount === 0) {
			setProcessError("No PDF files were found in the selection.");
			setIsProcessing(false);
			return;
		}
		setProcessStatusMessage(`Uploading ${pdfCount} PDF(s)...`);

		try {
			// Using raw fetch for FormData
			const response = await fetch(uploadAndProcessApiUrl, {
				method: "POST",
				body: formData,
			});

			if (!response.ok) {
				let errorData;
				try {
					errorData = await response.json();
				} catch {
					/* ignore */
				}
				throw new ApiError(
					`API Error: ${response.status}`,
					response.status,
					errorData?.detail || "Failed to initiate processing"
				);
			}

			const result = await response.json();

			if (result.task_id) {
				setActiveProcessTaskId(result.task_id);
				setProcessStatusMessage(
					`${result.message} (Task ID: ${result.task_id}). Polling for results...`
				);
				pollIntervalIdRef.current = setInterval(
					() => pollPdfProcessingStatus(result.task_id!),
					5000 // Poll every 5 seconds
				);
				pollPdfProcessingStatus(result.task_id); // Initial poll
			} else {
				setProcessError(
					"Error: Backend did not return a task ID for PDF processing."
				);
				setIsProcessing(false);
			}
		} catch (err) {
			console.error("PDF Processing API Error:", err);
			let errMsg =
				"An unknown error occurred during PDF processing initiation.";
			if (err instanceof ApiError)
				errMsg = `Error: ${err.message} ${
					err.details ? `(${JSON.stringify(err.details)})` : ""
				}`;
			else if (err instanceof Error) errMsg = `Error: ${err.message}`;
			setProcessError(errMsg);
			setIsProcessing(false);
		}
		// setIsProcessing will be set to false by polling logic on completion/failure
	};

	const handleStopProcessing = async () => {
		if (!activeProcessTaskId) return;

		try {
			// Call the API to stop the task
			await stopTask(activeProcessTaskId);

			// Update the UI to show the task as stopped
			setProcessStatusMessage("PDF processing stopped by user request");
			setProcessError("PDF processing was manually stopped");
			setIsProcessing(false);
			setProcessProgress(null); // Clear progress

			// Clear the polling interval
			if (pollIntervalIdRef.current) {
				clearInterval(pollIntervalIdRef.current);
				pollIntervalIdRef.current = null;
			}

			setActiveProcessTaskId(null);
		} catch (error) {
			console.error("Failed to stop PDF processing task:", error);
			setProcessError("Failed to stop the PDF processing. Please try again.");
		}
	};

	const pollPdfProcessingStatus = async (taskId: string) => {
		console.log(`Polling PDF processing status for Task ID: ${taskId}`);
		try {
			const taskStatus = await fetchApi<BackendTaskInfo>(
				`${statusApiUrl}/${taskId}`
			);

			// Update progress information
			if (
				taskStatus.progress_current !== undefined &&
				taskStatus.progress_total !== undefined
			) {
				setProcessProgress({
					current: taskStatus.progress_current,
					total: taskStatus.progress_total,
					count: taskStatus.result_count || 0,
					stage: taskStatus.progress_stage || "Processing",
				});
			}

			setProcessStatusMessage(
				`${taskStatus.message} (Status: ${taskStatus.status})`
			);

			if (taskStatus.status === "COMPLETED") {
				if (pollIntervalIdRef.current) clearInterval(pollIntervalIdRef.current);
				setIsProcessing(false);
				setActiveProcessTaskId(null);
				setProcessProgress(null);
				if (taskStatus.result_detail) {
					const results = taskStatus.result_detail
						.processed_results as PdfCheckResultItemClient[];
					setDetailedResults(results || []);
					setServerResultDirs({
						accepted:
							(taskStatus.result_detail as any)?.accepted_dir_rel || "N/A",
						rejected:
							(taskStatus.result_detail as any)?.rejected_dir_rel || "N/A",
					});
					setProcessStatusMessage(
						`Processing complete! Found ${
							results.length
						} results. Accepted dir: ${
							(taskStatus.result_detail as any)?.accepted_dir_rel
						}, Rejected dir: ${
							(taskStatus.result_detail as any)?.rejected_dir_rel
						}`
					);
				} else {
					setProcessStatusMessage(
						"Processing completed, but detailed results not provided by backend."
					);
				}
			} else if (taskStatus.status === "FAILED") {
				if (pollIntervalIdRef.current) clearInterval(pollIntervalIdRef.current);
				setIsProcessing(false);
				setActiveProcessTaskId(null);
				setProcessProgress(null);
				setProcessError(
					`Processing Failed: ${taskStatus.error_message || taskStatus.message}`
				);
			}
			// If still PENDING or RUNNING, polling continues
		} catch (err) {
			console.error("Error polling PDF processing task status:", err);
			setProcessStatusMessage(
				"Error fetching processing status. Polling may continue or stop if too many errors."
			);
		}
	};

	useEffect(() => {
		// Cleanup polling interval on unmount
		return () => {
			if (pollIntervalIdRef.current) clearInterval(pollIntervalIdRef.current);
		};
	}, []);

	return (
		<div className="space-y-4">
			{" "}
			{/* Styling applied by parent section card */}
			<div>
				<label
					htmlFor="pdfUploadInput"
					className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
				>
					Select Local PDF Files or Folder for Analysis:
				</label>
				<input
					type="file"
					id="pdfUploadInput"
					multiple
					accept=".pdf"
					// @ts-expect-error - webkitdirectory is not in the standard HTML input attributes
					webkitdirectory=""
					directory="" // For folder selection (non-standard but common)
					ref={fileInputRef}
					onChange={handleFileChange}
					className="block w-full text-sm text-gray-500 dark:text-gray-400
                               file:mr-4 file:py-2 file:px-4
                               file:rounded-md file:border-0
                               file:text-sm file:font-semibold
                               file:bg-violet-100 dark:file:bg-violet-700/50
                               file:text-violet-700 dark:file:text-violet-300
                               hover:file:bg-violet-200 dark:hover:file:bg-violet-600/50
                               cursor-pointer"
				/>
				<p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
					You can select multiple PDF files or a folder containing PDFs.
				</p>
			</div>
			{isProcessing && activeProcessTaskId ? (
				<div className="flex gap-2">
					<button
						onClick={handleStopProcessing}
						className="btn btn-danger flex items-center justify-center gap-1 bg-red-600 hover:bg-red-700 text-white"
					>
						<StopCircle size={16} />
						Stop PDF Processing
					</button>
					<div className="flex-1 text-sm flex items-center justify-center bg-gray-100 rounded-md">
						Processing in progress...
					</div>
				</div>
			) : (
				<button
					onClick={handleProcessPdfs}
					disabled={
						isProcessing || !selectedFiles || selectedFiles.length === 0
					}
					className="btn btn-primary w-full bg-sky-600 hover:bg-sky-700 dark:bg-sky-500 dark:hover:bg-sky-600"
				>
					{isProcessing
						? "Processing PDFs..."
						: "Upload and Analyze Selected PDFs"}
				</button>
			)}
			{processStatusMessage && (
				<div
					className={`mt-3 p-3 rounded-md border text-sm ${
						processError
							? "bg-red-50 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700"
							: "bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700"
					}`}
				>
					<div className="flex justify-between items-start mb-2">
						<div className="flex items-center gap-2">
							<StatusBadge
								status={
									processError
										? "failed"
										: isProcessing
										? "running"
										: "completed"
								}
								text={
									processError
										? "Error"
										: isProcessing
										? "Processing"
										: "Completed"
								}
							/>
							<span>
								{processProgress
									? `${processProgress.stage}: ${processProgress.current}/${processProgress.total} (Accepted: ${processProgress.count})`
									: processStatusMessage}
							</span>
						</div>
						{isProcessing && activeProcessTaskId && (
							<button
								onClick={handleStopProcessing}
								className="ml-2 px-2 py-1 bg-red-600 hover:bg-red-700 text-white rounded-md flex items-center text-xs"
							>
								<StopCircle size={14} className="mr-1" />
								Stop
							</button>
						)}
					</div>
					{isProcessing &&
						(processProgress ? (
							<ProgressBar
								current={processProgress.current}
								total={processProgress.total}
								height={6}
								variant="processing"
								label={`${processProgress.stage} Progress`}
								showDetailed={true}
							/>
						) : (
							<LoadingBar
								height={3}
								variant={processError ? "error" : "primary"}
								pulse={true}
							/>
						))}
				</div>
			)}
			{/* {processError && <p className="text-sm text-red-600 dark:text-red-400 mt-2">{processError}</p>} */}
			{detailedResults && detailedResults.length > 0 && (
				<div className="mt-6">
					<h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-2">
						Analysis Results ({detailedResults.length} PDFs Processed)
					</h4>
					{serverResultDirs && (
						<div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
							<p>Files sorted on server into:</p>
							<p>
								Accepted: <code>{serverResultDirs.accepted}</code>
							</p>
							<p>
								Rejected: <code>{serverResultDirs.rejected}</code>
							</p>
						</div>
					)}
					<div className="max-h-96 overflow-y-auto space-y-1 pr-1">
						{detailedResults.map((result, index) => (
							<div
								key={index}
								className={`p-2 rounded-md border text-xs ${
									result.status === "ACCEPT"
										? "bg-green-50 dark:bg-green-900/50 border-green-300 dark:border-green-700 text-green-700 dark:text-green-300"
										: "bg-red-50 dark:bg-red-900/50 border-red-300 dark:border-red-700 text-red-700 dark:text-red-300"
								}`}
							>
								<p className="font-semibold break-all">{result.filename}</p>
								<p>
									Status: <span className="font-bold">{result.status}</span>
								</p>
								{result.reasons && result.reasons.length > 0 && (
									<div>
										<p className="mt-1 text-gray-600 dark:text-gray-400">
											Reasons:
										</p>
										<ul className="list-disc list-inside pl-3">
											{result.reasons.map((reason, rIndex) => (
												<li key={rIndex}>{reason}</li>
											))}
										</ul>
									</div>
								)}
							</div>
						))}
					</div>
				</div>
			)}
		</div>
	);
};

export default PdfProcessingControls;
